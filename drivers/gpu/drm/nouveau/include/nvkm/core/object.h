/* SPDX-License-Identifier: MIT */
#ifndef __NVKM_OBJECT_H__
#define __NVKM_OBJECT_H__
#include <core/oclass.h>
struct nvkm_event;
struct nvkm_gpuobj;
struct nvkm_uevent;

struct nvkm_object {
	const struct nvkm_object_func *func;
	struct nvkm_client *client;
	struct nvkm_engine *engine;
	s32 oclass;
	u32 handle;

	struct list_head head;
	struct list_head tree;
	u64 object;
	struct rb_node node;
};

enum nvkm_object_map {
	NVKM_OBJECT_MAP_IO,
	NVKM_OBJECT_MAP_VA
};

struct nvkm_object_func {
	void *(*dtor)(struct nvkm_object *);
	int (*init)(struct nvkm_object *);
	int (*fini)(struct nvkm_object *, bool suspend);
	int (*mthd)(struct nvkm_object *, u32 mthd, void *data, u32 size);
	int (*ntfy)(struct nvkm_object *, u32 mthd, struct nvkm_event **);
	int (*map)(struct nvkm_object *, void *argv, u32 argc,
		   enum nvkm_object_map *, u64 *addr, u64 *size);
	int (*unmap)(struct nvkm_object *);
	int (*bind)(struct nvkm_object *, struct nvkm_gpuobj *, int align,
		    struct nvkm_gpuobj **);
	int (*sclass)(struct nvkm_object *, int index, struct nvkm_oclass *);
	int (*uevent)(struct nvkm_object *, void *argv, u32 argc, struct nvkm_uevent *);
};

void nvkm_object_ctor(const struct nvkm_object_func *,
		      const struct nvkm_oclass *, struct nvkm_object *);
int nvkm_object_new_(const struct nvkm_object_func *,
		     const struct nvkm_oclass *, void *data, u32 size,
		     struct nvkm_object **);
int nvkm_object_new(const struct nvkm_oclass *, void *data, u32 size,
		    struct nvkm_object **);
void nvkm_object_del(struct nvkm_object **);
void *nvkm_object_dtor(struct nvkm_object *);
int nvkm_object_init(struct nvkm_object *);
int nvkm_object_fini(struct nvkm_object *, bool suspend);
int nvkm_object_mthd(struct nvkm_object *, u32 mthd, void *data, u32 size);
int nvkm_object_ntfy(struct nvkm_object *, u32 mthd, struct nvkm_event **);
int nvkm_object_map(struct nvkm_object *, void *argv, u32 argc,
		    enum nvkm_object_map *, u64 *addr, u64 *size);
int nvkm_object_unmap(struct nvkm_object *);
int nvkm_object_bind(struct nvkm_object *, struct nvkm_gpuobj *, int align,
		     struct nvkm_gpuobj **);

bool nvkm_object_insert(struct nvkm_object *);
void nvkm_object_remove(struct nvkm_object *);
struct nvkm_object *nvkm_object_search(struct nvkm_client *, u64 object,
				       const struct nvkm_object_func *);
#endif
