# SPDX-License-Identifier: GPL-2.0-only

config DRM_MSM
	tristate "MSM DRM"
	depends on DRM
	depends on ARCH_QCOM || SOC_IMX5 || COMPILE_TEST
	depends on COMMON_CLK
	depends on IOMMU_SUPPORT
	depends on QCOM_AOSS_QMP || QCOM_AOSS_QMP=n
	depends on QCOM_OCMEM || QCOM_OCMEM=n
	depends on QCOM_LLCC || QCOM_LLCC=n
	depends on QCOM_COMMAND_DB || QCOM_COMMAND_DB=n
	depends on PM
	select IOMMU_IO_PGTABLE
	select QCOM_MDT_LOADER if <PERSON><PERSON>_QCOM
	select REGULATOR
	select DRM_DISPLAY_DP_AUX_BUS
	select DRM_DISPLAY_DP_HELPER
	select DRM_DISPLAY_HELPER
	select DRM_BRIDGE_CONNECTOR
	select DRM_EXEC
	select DRM_KMS_HELPER
	select DRM_PANEL
	select DRM_BRIDGE
	select DRM_PANEL_BRIDGE
	select DRM_SCHED
	select FB_SYSMEM_HELPERS if DRM_FBDEV_EMULATION
	select SHMEM
	select TMPFS
	select QCOM_SCM
	select WANT_DEV_COREDUMP
	select SND_SOC_HDMI_CODEC if SND_SOC
	select SYNC_FILE
	select PM_OPP
	select NVMEM
	select PM_GENERIC_DOMAINS
	select TRACE_GPU_MEM
	help
	  DRM/KMS driver for MSM/snapdragon.

config DRM_MSM_GPU_STATE
	bool
	depends on DRM_MSM && (DEBUG_FS || DEV_COREDUMP)
	default y

config DRM_MSM_GPU_SUDO
	bool "Enable SUDO flag on submits"
	depends on DRM_MSM && EXPERT
	default n
	help
	  Enable userspace that has CAP_SYS_RAWIO to submit GPU commands
	  that are run from RB instead of IB1.  This essentially gives
	  userspace kernel level access, but is useful for firmware
	  debugging.

	  Only use this if you are a driver developer.  This should *not*
	  be enabled for production kernels.  If unsure, say N.

config DRM_MSM_VALIDATE_XML
	bool "Validate XML register files against schema"
	depends on DRM_MSM && EXPERT
	depends on $(success,$(PYTHON3) -c "import lxml")
	help
	  Validate XML files with register definitions against rules-fd schema.
	  This option is mostly targeting DRM MSM developers. If unsure, say N.

config DRM_MSM_MDSS
	bool
	depends on DRM_MSM
	default n

config DRM_MSM_MDP4
	bool "Enable MDP4 support in MSM DRM driver"
	depends on DRM_MSM
	default y
	help
	  Compile in support for the Mobile Display Processor v4 (MDP4) in
	  the MSM DRM driver. It is the older display controller found in
	  devices using APQ8064/MSM8960/MSM8x60 platforms.

config DRM_MSM_MDP5
	bool "Enable MDP5 support in MSM DRM driver"
	depends on DRM_MSM
	select DRM_MSM_MDSS
	default y
	help
	  Compile in support for the Mobile Display Processor v5 (MDP5) in
	  the MSM DRM driver. It is the display controller found in devices
	  using e.g. APQ8016/MSM8916/APQ8096/MSM8996/MSM8974/SDM6x0 platforms.

config DRM_MSM_DPU
	bool "Enable DPU support in MSM DRM driver"
	depends on DRM_MSM
	select DRM_MSM_MDSS
	default y
	help
	  Compile in support for the Display Processing Unit in
	  the MSM DRM driver. It is the display controller found in devices
	  using e.g. SDM845 and newer platforms.

config DRM_MSM_DP
	bool "Enable DisplayPort support in MSM DRM driver"
	depends on DRM_MSM
	select RATIONAL
	default y
	help
	  Compile in support for DP driver in MSM DRM driver. DP external
	  display support is enabled through this config option. It can
	  be primary or secondary display on device.

config DRM_MSM_DSI
	bool "Enable DSI support in MSM DRM driver"
	depends on DRM_MSM
	select DRM_PANEL
	select DRM_MIPI_DSI
	default y
	help
	  Choose this option if you have a need for MIPI DSI connector
	  support.

config DRM_MSM_DSI_28NM_PHY
	bool "Enable DSI 28nm PHY driver in MSM DRM"
	depends on DRM_MSM_DSI
	default y
	help
	  Choose this option if the 28nm DSI PHY is used on the platform.

config DRM_MSM_DSI_20NM_PHY
	bool "Enable DSI 20nm PHY driver in MSM DRM"
	depends on DRM_MSM_DSI
	default y
	help
	  Choose this option if the 20nm DSI PHY is used on the platform.

config DRM_MSM_DSI_28NM_8960_PHY
	bool "Enable DSI 28nm 8960 PHY driver in MSM DRM"
	depends on DRM_MSM_DSI
	default y
	help
	  Choose this option if the 28nm DSI PHY 8960 variant is used on the
	  platform.

config DRM_MSM_DSI_14NM_PHY
	bool "Enable DSI 14nm PHY driver in MSM DRM (used by MSM8996/APQ8096)"
	depends on DRM_MSM_DSI
	default y
	help
	  Choose this option if DSI PHY on 8996 is used on the platform.

config DRM_MSM_DSI_10NM_PHY
	bool "Enable DSI 10nm PHY driver in MSM DRM (used by SDM845)"
	depends on DRM_MSM_DSI
	default y
	help
	  Choose this option if DSI PHY on SDM845 is used on the platform.

config DRM_MSM_DSI_7NM_PHY
	bool "Enable DSI 7nm/5nm/4nm PHY driver in MSM DRM"
	depends on DRM_MSM_DSI
	default y
	help
	  Choose this option if DSI PHY on SM8150/SM8250/SM8350/SM8450/SM8550/SC7280
	  is used on the platform.

config DRM_MSM_HDMI
	bool "Enable HDMI support in MSM DRM driver"
	depends on DRM_MSM
	default y
	help
	  Compile in support for the HDMI output MSM DRM driver. It can
	  be a primary or a secondary display on device. Note that this is used
	  only for the direct HDMI output. If the device outputs HDMI data
	  through some kind of DSI-to-HDMI bridge, this option can be disabled.

config DRM_MSM_HDMI_HDCP
	bool "Enable HDMI HDCP support in MSM DRM driver"
	depends on DRM_MSM && DRM_MSM_HDMI
	default y
	help
	  Choose this option to enable HDCP state machine
