<?xml version="1.0" encoding="UTF-8"?>
<schema xmlns="http://www.w3.org/2001/XMLSchema"
	targetNamespace="http://nouveau.freedesktop.org/"
	xmlns:rng="http://nouveau.freedesktop.org/"
	elementFormDefault="qualified">

	<annotation>
		<documentation>
			An updated version of the old rules.xml file from the
			RivaTV project. Specifications by Pek<PERSON>,
			preliminary attempt by KoalaBR,
			first working version by <PERSON>.
			For specifications, see the file rules-ng-format.txt
			in Nouveau CVS module 'rules-ng'.
		</documentation>
		<documentation>Version 0.1</documentation>
	</annotation>


	<!-- Elements -->

	<element name="database"       type="rng:databaseType" />
	<element name="import"         type="rng:importType" />
	<element name="copyright"      type="rng:copyrightType" />
	<element name="domain"         type="rng:domainType" />
	<element name="array"          type="rng:arrayType" />
	<element name="stripe"         type="rng:stripeType" />
	<element name="reg64"          type="rng:registerType" />
	<element name="reg32"          type="rng:registerType" />
	<element name="bitset"         type="rng:bitsetType" />
	<element name="bitfield"       type="rng:bitfieldType" />
	<element name="enum"           type="rng:enumType" />
	<element name="value"          type="rng:valueType" />

	<!-- Copyright elements -->
	<element name="author"         type="rng:authorType" />
	<element name="nick"           type="rng:nickType" />
	<element name="license"        type="rng:docType" />

	<!-- Documentation elements -->
	
	<!-- FIXME: allowed only one  per parent element -->
	<element name="brief" type="rng:briefType" />
	
	<element name="doc"  type="rng:docType" />
	<element name="b"    type="rng:textformatType" />
	<element name="i"    type="rng:textformatType" />
	<element name="u"    type="rng:textformatType" />
	<element name="code" type="rng:textcodeType" />
	<element name="ul"   type="rng:listType" />
	<element name="ol"   type="rng:listType" />
	<element name="li"   type="rng:listitemType" />

	<!-- Copyright element types -->

	<complexType name="authorType" mixed="true">
		<annotation>
			<documentation>
				register database author
			</documentation>
		</annotation>
		<choice minOccurs="0" maxOccurs="unbounded">
			<element ref="rng:nick" />
		</choice>
		<attribute name="name" type="string" use="required" />
		<attribute name="email" type="string" use="required" />
	</complexType>

	<complexType name="nickType">
		<annotation>
			<documentation>nickType</documentation>
		</annotation>
		<attribute name="name" type="string" use="required" />
	</complexType>

	<!-- Database element types -->

	<complexType name="databaseType">
		<annotation>
			<documentation>databaseType</documentation>
		</annotation>
		<choice minOccurs="0" maxOccurs="unbounded">
			<group ref="rng:docGroup" />
			<group ref="rng:topGroup" />
		</choice>
	</complexType>

	<complexType name="importType">
		<annotation>
			<documentation>importType</documentation>
		</annotation>
		<attribute name="file" type="string" use="required" />
	</complexType>

	<complexType name="copyrightType">
		<annotation>
			<documentation>copyrightType</documentation>
		</annotation>
		<choice minOccurs="0" maxOccurs="unbounded">
			<group ref="rng:docGroup" />
			<group ref="rng:topGroup" />
			<element ref="rng:author" />
			<element ref="rng:license" />
		</choice>
		<attribute name="year" type="nonNegativeInteger" use="optional" />
	</complexType>

	<complexType name="domainType">
		<annotation>
			<documentation>domainType</documentation>
		</annotation>
		<choice minOccurs="0" maxOccurs="unbounded">
			<group ref="rng:docGroup" />
			<group ref="rng:topGroup" />
			<group ref="rng:regarrayGroup" />
		</choice>
		<attribute name="name" type="NMTOKEN" use="required" />
		<attribute name="prefix" type="NMTOKENS" use="optional" />
		<attribute name="width" type="rng:DomainWidth" use="optional" />
		<attribute name="varset" type="NMTOKEN" use="optional" />
		<attribute name="variants" type="string" use="optional" />
	</complexType>

	<complexType name="arrayType">
		<annotation>
			<documentation>arrayType</documentation>
		</annotation>
		<choice minOccurs="0" maxOccurs="unbounded">
			<group ref="rng:docGroup" />
			<group ref="rng:topGroup" />
			<group ref="rng:regarrayGroup" />
		</choice>
		<attribute name="name" type="NMTOKEN" use="optional" />
		<attribute name="offset" type="rng:HexOrNumber" use="optional" />
		<attribute name="offsets" type="string" use="optional"/>
		<attribute name="doffsets" type="string" use="optional"/>
		<attribute name="index" type="NMTOKENS" use="optional"/>
		<attribute name="stride" type="rng:HexOrNumber" use="required" />
		<attribute name="length" type="rng:HexOrNumber" use="required" />
		<attribute name="varset" type="NMTOKEN" use="optional" />
		<attribute name="variants" type="string" use="optional" />
		<attribute name="usage" type="string" use="optional" />
	</complexType>

	<complexType name="stripeType">
		<annotation>
			<documentation>stripeType</documentation>
		</annotation>
		<choice minOccurs="0" maxOccurs="unbounded">
			<group ref="rng:docGroup" />
			<group ref="rng:topGroup" />
			<group ref="rng:regarrayGroup" minOccurs="0" />
		</choice>
		<attribute name="varset" type="NMTOKEN" use="optional" />
		<attribute name="variants" type="string" use="optional" />
		<attribute name="prefix" type="NMTOKENS" use="optional" />
	</complexType>

	<complexType name="registerType">
		<annotation>
			<documentation>
				registerType used by reg32, reg64
			</documentation>
		</annotation>
		<choice minOccurs="0" maxOccurs="unbounded">
			<group ref="rng:docGroup" />
			<group ref="rng:topGroup" />
			<element ref="rng:value" />
			<element ref="rng:bitfield" />
		</choice>
		<attribute name="name" type="NMTOKEN" use="required" />
		<attribute name="offset" type="rng:HexOrNumber" use="required" />
		<attribute name="type" type="NMTOKENS" use="optional" />
		<attribute name="shr" type="nonNegativeInteger" use="optional" />
		<attribute name="varset" type="NMTOKEN" use="optional" />
		<attribute name="variants" type="string" use="optional" />
		<attribute name="stride" type="rng:HexOrNumber" use="optional" />
		<attribute name="length" type="rng:HexOrNumber" use="optional" />
		<attribute name="high" type="nonNegativeInteger" use="optional" />
		<attribute name="low" type="nonNegativeInteger" use="optional" />
		<attribute name="pos" type="nonNegativeInteger" use="optional" />
		<attribute name="align" type="nonNegativeInteger" use="optional" />
		<attribute name="radix" type="nonNegativeInteger" use="optional" />
		<attribute name="usage" type="string" use="optional" />
	</complexType>

	<complexType name="bitsetType">
		<annotation>
			<documentation>bitsetType</documentation>
		</annotation>
		<choice maxOccurs="unbounded">
			<element ref="rng:bitfield" />
			<group ref="rng:docGroup" />
			<group ref="rng:topGroup" />
		</choice>
		<attribute name="name" type="NMTOKEN" use="required" />
		<attribute name="inline" type="rng:Boolean" use="optional" />
		<attribute name="varset" type="NMTOKEN" use="optional" />
	</complexType>

	<complexType name="bitfieldType">
		<annotation>
			<documentation>bitfieldType</documentation>
		</annotation>
		<choice minOccurs="0" maxOccurs="unbounded">
			<element ref="rng:value" maxOccurs="unbounded" />
			<group ref="rng:docGroup" />
			<group ref="rng:topGroup" />
		</choice>
		<attribute name="name" type="NMTOKEN" use="required" />
		<attribute name="high" type="nonNegativeInteger" use="optional" />
		<attribute name="low" type="nonNegativeInteger" use="optional" />
		<attribute name="pos" type="nonNegativeInteger" use="optional" />
		<attribute name="radix" type="nonNegativeInteger" use="optional" />
		<attribute name="type" type="NMTOKENS" use="optional" />
		<attribute name="varset" type="NMTOKEN" use="optional" />
		<attribute name="variants" type="string" use="optional" />
		<attribute name="addvariant" type="rng:Boolean" use="optional" />
		<attribute name="shr" type="nonNegativeInteger" use="optional" />
	</complexType>

	<complexType name="enumType">
		<annotation>
			<documentation>enumType</documentation>
		</annotation>
		<choice maxOccurs="unbounded">
			<element ref="rng:value" />
			<group ref="rng:docGroup" />
			<group ref="rng:topGroup" />
		</choice>
		<attribute name="name" type="NMTOKEN" use="required" />
		<attribute name="bare" type="rng:Boolean" use="optional" />
		<attribute name="prefix" type="NMTOKENS" use="optional" />
		<attribute name="varset" type="NMTOKEN" use="optional" />
	</complexType>

	<complexType name="valueType">
		<annotation>
			<documentation>valueType</documentation>
		</annotation>
		<choice minOccurs="0" maxOccurs="unbounded">
			<group ref="rng:docGroup" />
			<group ref="rng:topGroup" />
		</choice>
		<attribute name="name" type="NMTOKEN" use="required" />
		<attribute name="value" type="string" use="optional" />
		<attribute name="varset" type="NMTOKEN" use="optional" />
		<attribute name="variants" type="string" use="optional" />
	</complexType>

	<!-- Documentation element types -->

	<complexType name="briefType">
		<annotation>
			<documentation>
				brief documentation, no markup
			</documentation>
		</annotation>
		<simpleContent>
			<extension base="string" />
		</simpleContent>
	</complexType>
	
	<complexType name="docType" mixed="true">
		<annotation>
			<documentation>
				root element of documentation sub-tree
			</documentation>
		</annotation>
		<choice minOccurs="0" maxOccurs="unbounded">
			<group ref="rng:textformatGroup" />
			<group ref="rng:listGroup" />
			<element ref="rng:code" />
		</choice>
	</complexType>
	
	<complexType name="textformatType" mixed="true">
		<annotation>
			<documentation>
				for bold, underline, italics
			</documentation>
		</annotation>
		<choice minOccurs="0" maxOccurs="unbounded">
			<group ref="rng:textformatGroup" />
		</choice>
	</complexType>
	
	<complexType name="textcodeType">
		<simpleContent>
			<extension base="string">
				<attribute name="title" type="string" />
			</extension>
		</simpleContent>
	</complexType>
	
	<complexType name="listType">
		<annotation>
			<documentation>
				definition of a list, ordered or unordered
			</documentation>
		</annotation>
		<choice minOccurs="0" maxOccurs="unbounded">
			<element ref="rng:li" />
		</choice>
	</complexType>
	
	<complexType name="listitemType" mixed="true">
		<annotation>
			<documentation>
				items of a list
			</documentation>
		</annotation>
		<choice minOccurs="0" maxOccurs="unbounded">
			<group ref="rng:textformatGroup" />
			<group ref="rng:listGroup" />
			<element ref="rng:code" />
		</choice>
	</complexType>



	<!-- Attribute value types -->

	<simpleType name="Hexadecimal">
		<restriction base="string">
			<pattern value="0x[0-9a-f]+" />
			<pattern value="0x[0-9A-F]+" />
			<pattern value="[0-9]" />
		</restriction>
	</simpleType>

	<simpleType name="HexOrNumber">
		<annotation>
			<documentation>HexOrNumber</documentation>
		</annotation>
		<union memberTypes="rng:Hexadecimal nonNegativeInteger" />
	</simpleType>

	<simpleType name="Boolean">
		<restriction base="string">
			<enumeration value="true" />
			<enumeration value="1" />
			<enumeration value="yes" />
			<enumeration value="false" />
			<enumeration value="0" />
			<enumeration value="no" />
		</restriction>
	</simpleType>

	<simpleType name="DomainWidth">
		<annotation>
			<documentation>DomainWidth</documentation>
		</annotation>
		<restriction base="string">
			<enumeration value="32" />
		</restriction>
	</simpleType>



	<!-- Element groups -->

	<group name="topGroup">
		<choice>
			<element ref="rng:copyright" />
			<element ref="rng:domain" />
			<element ref="rng:enum" />
			<element ref="rng:bitset" />
			<element ref="rng:import" />
		</choice>
	</group>
	
	<group name="regarrayGroup">
		<choice>
			<element ref="rng:reg64" />
			<element ref="rng:reg32" />
			<element ref="rng:array" />
			<element ref="rng:stripe" />
		</choice>
	</group>
	
	<group name="docGroup">
		<choice>
			<element ref="rng:brief" />
			<element ref="rng:doc" />
		</choice>
	</group>
	
	<group name="textformatGroup">
		<choice>
			<element ref="rng:b" />
			<element ref="rng:i" />
			<element ref="rng:u" />
		</choice>
	</group>
	
	<group name="listGroup">
		<choice>
			<element ref="rng:ul" />
			<element ref="rng:ol" />
		</choice>
	</group>

</schema>
