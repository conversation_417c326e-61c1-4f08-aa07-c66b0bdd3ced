<?xml version="1.0" encoding="UTF-8"?>
<database xmlns="http://nouveau.freedesktop.org/"
xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
xsi:schemaLocation="https://gitlab.freedesktop.org/freedreno/ rules-fd.xsd">
<import file="freedreno_copyright.xml"/>

<domain name="DSI" width="32">
	<enum name="dsi_traffic_mode">
		<value name="NON_BURST_SYNCH_PULSE" value="0"/>
		<value name="NON_BURST_SYNCH_EVENT" value="1"/>
		<value name="BURST_MODE" value="2"/>
	</enum>
	<enum name="dsi_vid_dst_format">
		<value name="VID_DST_FORMAT_RGB565" value="0"/>
		<value name="VID_DST_FORMAT_RGB666" value="1"/>
		<value name="VID_DST_FORMAT_RGB666_LOOSE" value="2"/>
		<value name="VID_DST_FORMAT_RGB888" value="3"/>
	</enum>
	<enum name="dsi_rgb_swap">
		<value name="SWAP_RGB" value="0"/>
		<value name="SWAP_RBG" value="1"/>
		<value name="SWAP_BGR" value="2"/>
		<value name="SWAP_BRG" value="3"/>
		<value name="SWAP_GRB" value="4"/>
		<value name="SWAP_GBR" value="5"/>
	</enum>
	<enum name="dsi_cmd_trigger">
		<value name="TRIGGER_NONE" value="0"/>
		<value name="TRIGGER_SEOF" value="1"/>
		<value name="TRIGGER_TE" value="2"/>
		<value name="TRIGGER_SW" value="4"/>
		<value name="TRIGGER_SW_SEOF" value="5"/>
		<value name="TRIGGER_SW_TE" value="6"/>
	</enum>
	<enum name="dsi_cmd_dst_format">
		<value name="CMD_DST_FORMAT_RGB111" value="0"/>
		<value name="CMD_DST_FORMAT_RGB332" value="3"/>
		<value name="CMD_DST_FORMAT_RGB444" value="4"/>
		<value name="CMD_DST_FORMAT_RGB565" value="6"/>
		<value name="CMD_DST_FORMAT_RGB666" value="7"/>
		<value name="CMD_DST_FORMAT_RGB888" value="8"/>
	</enum>
	<enum name="dsi_lane_swap">
		<value name="LANE_SWAP_0123" value="0"/>
		<value name="LANE_SWAP_3012" value="1"/>
		<value name="LANE_SWAP_2301" value="2"/>
		<value name="LANE_SWAP_1230" value="3"/>
		<value name="LANE_SWAP_0321" value="4"/>
		<value name="LANE_SWAP_1032" value="5"/>
		<value name="LANE_SWAP_2103" value="6"/>
		<value name="LANE_SWAP_3210" value="7"/>
	</enum>
		<enum name="video_config_bpp">
		<value name="VIDEO_CONFIG_18BPP" value="0"/>
		<value name="VIDEO_CONFIG_24BPP" value="1"/>
	</enum>
	<enum name="video_pattern_sel">
		<value name="VID_PRBS" value="0"/>
		<value name="VID_INCREMENTAL" value="1"/>
		<value name="VID_FIXED" value="2"/>
		<value name="VID_MDSS_GENERAL_PATTERN" value="3"/>
	</enum>
	<enum name="cmd_mdp_stream0_pattern_sel">
		<value name="CMD_MDP_PRBS" value="0"/>
		<value name="CMD_MDP_INCREMENTAL" value="1"/>
		<value name="CMD_MDP_FIXED" value="2"/>
		<value name="CMD_MDP_MDSS_GENERAL_PATTERN" value="3"/>
	</enum>
	<enum name="cmd_dma_pattern_sel">
		<value name="CMD_DMA_PRBS" value="0"/>
		<value name="CMD_DMA_INCREMENTAL" value="1"/>
		<value name="CMD_DMA_FIXED" value="2"/>
		<value name="CMD_DMA_CUSTOM_PATTERN_DMA_FIFO" value="3"/>
	</enum>
	<bitset name="DSI_IRQ">
		<bitfield name="CMD_DMA_DONE" pos="0" type="boolean"/>
		<bitfield name="MASK_CMD_DMA_DONE" pos="1" type="boolean"/>
		<bitfield name="CMD_MDP_DONE" pos="8" type="boolean"/>
		<bitfield name="MASK_CMD_MDP_DONE" pos="9" type="boolean"/>
		<bitfield name="VIDEO_DONE" pos="16" type="boolean"/>
		<bitfield name="MASK_VIDEO_DONE" pos="17" type="boolean"/>
		<bitfield name="BTA_DONE" pos="20" type="boolean"/>
		<bitfield name="MASK_BTA_DONE" pos="21" type="boolean"/>
		<bitfield name="ERROR" pos="24" type="boolean"/>
		<bitfield name="MASK_ERROR" pos="25" type="boolean"/>
	</bitset>

	<reg32 offset="0x00000" name="6G_HW_VERSION">
		<bitfield name="MAJOR" low="28" high="31" type="uint"/>
		<bitfield name="MINOR" low="16" high="27" type="uint"/>
		<bitfield name="STEP" low="0" high="15" type="uint"/>
	</reg32>

	<reg32 offset="0x00000" name="CTRL">
		<bitfield name="ENABLE" pos="0" type="boolean"/>
		<bitfield name="VID_MODE_EN" pos="1" type="boolean"/>
		<bitfield name="CMD_MODE_EN" pos="2" type="boolean"/>
		<bitfield name="LANE0" pos="4" type="boolean"/>
		<bitfield name="LANE1" pos="5" type="boolean"/>
		<bitfield name="LANE2" pos="6" type="boolean"/>
		<bitfield name="LANE3" pos="7" type="boolean"/>
		<bitfield name="CLK_EN" pos="8" type="boolean"/>
		<bitfield name="ECC_CHECK" pos="20" type="boolean"/>
		<bitfield name="CRC_CHECK" pos="24" type="boolean"/>
	</reg32>

	<reg32 offset="0x00004" name="STATUS0">
		<bitfield name="CMD_MODE_ENGINE_BUSY" pos="0" type="boolean"/>
		<bitfield name="CMD_MODE_DMA_BUSY" pos="1" type="boolean"/>
		<bitfield name="CMD_MODE_MDP_BUSY" pos="2" type="boolean"/>
		<bitfield name="VIDEO_MODE_ENGINE_BUSY" pos="3" type="boolean"/>
		<bitfield name="DSI_BUSY" pos="4" type="boolean"/>  <!-- see mipi_dsi_cmd_bta_sw_trigger() -->
		<bitfield name="INTERLEAVE_OP_CONTENTION" pos="31" type="boolean"/>
	</reg32>

	<reg32 offset="0x00008" name="FIFO_STATUS">
		<bitfield name="VIDEO_MDP_FIFO_OVERFLOW" pos="0" type="boolean"/>
		<bitfield name="VIDEO_MDP_FIFO_UNDERFLOW" pos="3" type="boolean"/>
		<bitfield name="CMD_MDP_FIFO_UNDERFLOW" pos="7" type="boolean"/>
		<bitfield name="CMD_DMA_FIFO_RD_WATERMARK_REACH" pos="8" type="boolean"/>
		<bitfield name="CMD_DMA_FIFO_WR_WATERMARK_REACH" pos="9" type="boolean"/>
		<bitfield name="CMD_DMA_FIFO_UNDERFLOW" pos="10" type="boolean"/>
		<bitfield name="DLN0_LP_FIFO_EMPTY"     pos="12" type="boolean"/>
		<bitfield name="DLN0_LP_FIFO_FULL"      pos="13" type="boolean"/>
		<bitfield name="DLN0_LP_FIFO_OVERFLOW"  pos="14" type="boolean"/>
		<bitfield name="DLN0_HS_FIFO_EMPTY"     pos="16" type="boolean"/>
		<bitfield name="DLN0_HS_FIFO_FULL"      pos="17" type="boolean"/>
		<bitfield name="DLN0_HS_FIFO_OVERFLOW"  pos="18" type="boolean"/>
		<bitfield name="DLN0_HS_FIFO_UNDERFLOW" pos="19" type="boolean"/>
		<bitfield name="DLN1_HS_FIFO_EMPTY"     pos="20" type="boolean"/>
		<bitfield name="DLN1_HS_FIFO_FULL"      pos="21" type="boolean"/>
		<bitfield name="DLN1_HS_FIFO_OVERFLOW"  pos="22" type="boolean"/>
		<bitfield name="DLN1_HS_FIFO_UNDERFLOW" pos="23" type="boolean"/>
		<bitfield name="DLN2_HS_FIFO_EMPTY"     pos="24" type="boolean"/>
		<bitfield name="DLN2_HS_FIFO_FULL"      pos="25" type="boolean"/>
		<bitfield name="DLN2_HS_FIFO_OVERFLOW"  pos="26" type="boolean"/>
		<bitfield name="DLN2_HS_FIFO_UNDERFLOW" pos="27" type="boolean"/>
		<bitfield name="DLN3_HS_FIFO_EMPTY"     pos="28" type="boolean"/>
		<bitfield name="DLN3_HS_FIFO_FULL"      pos="29" type="boolean"/>
		<bitfield name="DLN3_HS_FIFO_OVERFLOW"  pos="30" type="boolean"/>
		<bitfield name="DLN3_HS_FIFO_UNDERFLOW" pos="31" type="boolean"/>
	</reg32>
	<reg32 offset="0x0000c" name="VID_CFG0">
		<bitfield name="VIRT_CHANNEL" low="0" high="1" type="uint"/>  <!-- always zero? -->
		<bitfield name="DST_FORMAT" low="4" high="5" type="dsi_vid_dst_format"/>
		<bitfield name="TRAFFIC_MODE" low="8" high="9" type="dsi_traffic_mode"/>
		<bitfield name="BLLP_POWER_STOP" pos="12" type="boolean"/>
		<bitfield name="EOF_BLLP_POWER_STOP" pos="15" type="boolean"/>
		<bitfield name="HSA_POWER_STOP" pos="16" type="boolean"/>
		<bitfield name="HBP_POWER_STOP" pos="20" type="boolean"/>
		<bitfield name="HFP_POWER_STOP" pos="24" type="boolean"/>
		<bitfield name="DATABUS_WIDEN" pos="25" type="boolean"/>
		<bitfield name="PULSE_MODE_HSA_HE" pos="28" type="boolean"/>
	</reg32>
	<reg32 offset="0x0001c" name="VID_CFG1">
		<bitfield name="R_SEL" pos="0" type="boolean"/>
		<bitfield name="G_SEL" pos="4" type="boolean"/>
		<bitfield name="B_SEL" pos="8" type="boolean"/>
		<bitfield name="RGB_SWAP" low="12" high="14" type="dsi_rgb_swap"/>
	</reg32>
	<reg32 offset="0x00020" name="ACTIVE_H">
		<bitfield name="START" low="0" high="11" type="uint"/>
		<bitfield name="END" low="16" high="27" type="uint"/>
	</reg32>
	<reg32 offset="0x00024" name="ACTIVE_V">
		<bitfield name="START" low="0" high="11" type="uint"/>
		<bitfield name="END" low="16" high="27" type="uint"/>
	</reg32>
	<reg32 offset="0x00028" name="TOTAL">
		<bitfield name="H_TOTAL" low="0" high="11" type="uint"/>
		<bitfield name="V_TOTAL" low="16" high="27" type="uint"/>
	</reg32>
	<reg32 offset="0x0002c" name="ACTIVE_HSYNC">
		<bitfield name="START" low="0" high="11" type="uint"/>
		<bitfield name="END" low="16" high="27" type="uint"/>
	</reg32>
	<reg32 offset="0x00030" name="ACTIVE_VSYNC_HPOS">
		<bitfield name="START" low="0" high="11" type="uint"/>
		<bitfield name="END" low="16" high="27" type="uint"/>
	</reg32>
	<reg32 offset="0x00034" name="ACTIVE_VSYNC_VPOS">
		<bitfield name="START" low="0" high="11" type="uint"/>
		<bitfield name="END" low="16" high="27" type="uint"/>
	</reg32>

	<reg32 offset="0x00038" name="CMD_DMA_CTRL">
		<bitfield name="BROADCAST_EN" pos="31" type="boolean"/>
		<bitfield name="FROM_FRAME_BUFFER" pos="28" type="boolean"/>
		<bitfield name="LOW_POWER" pos="26" type="boolean"/>
	</reg32>
	<reg32 offset="0x0003c" name="CMD_CFG0">
		<bitfield name="DST_FORMAT" low="0" high="3" type="dsi_cmd_dst_format"/>
		<bitfield name="R_SEL" pos="4" type="boolean"/>
		<bitfield name="G_SEL" pos="8" type="boolean"/>
		<bitfield name="B_SEL" pos="12" type="boolean"/>
		<bitfield name="INTERLEAVE_MAX" low="20" high="23" type="uint"/>
		<bitfield name="RGB_SWAP" low="16" high="18" type="dsi_rgb_swap"/>
	</reg32>
	<reg32 offset="0x00040" name="CMD_CFG1">
		<bitfield name="WR_MEM_START" low="0" high="7" type="uint"/>
		<bitfield name="WR_MEM_CONTINUE" low="8" high="15" type="uint"/>
		<bitfield name="INSERT_DCS_COMMAND" pos="16" type="boolean"/>
	</reg32>
	<reg32 offset="0x00044" name="DMA_BASE"/>
	<reg32 offset="0x00048" name="DMA_LEN"/>
	<reg32 offset="0x00054" name="CMD_MDP_STREAM0_CTRL">
		<bitfield name="DATA_TYPE" low="0" high="5" type="uint"/>
		<bitfield name="VIRTUAL_CHANNEL" low="8" high="9" type="uint"/>
		<bitfield name="WORD_COUNT" low="16" high="31" type="uint"/>
	</reg32>
	<reg32 offset="0x00058" name="CMD_MDP_STREAM0_TOTAL">
		<bitfield name="H_TOTAL" low="0" high="11" type="uint"/>
		<bitfield name="V_TOTAL" low="16" high="27" type="uint"/>
	</reg32>
	<reg32 offset="0x0005c" name="CMD_MDP_STREAM1_CTRL">
		<bitfield name="DATA_TYPE" low="0" high="5" type="uint"/>
		<bitfield name="VIRTUAL_CHANNEL" low="8" high="9" type="uint"/>
		<bitfield name="WORD_COUNT" low="16" high="31" type="uint"/>
	</reg32>
	<reg32 offset="0x00060" name="CMD_MDP_STREAM1_TOTAL">
		<bitfield name="H_TOTAL" low="0" high="15" type="uint"/>
		<bitfield name="V_TOTAL" low="16" high="31" type="uint"/>
	</reg32>
	<reg32 offset="0x00064" name="ACK_ERR_STATUS"/>
	<array offset="0x00068" name="RDBK" length="4" stride="4">
		<reg32 offset="0x0" name="DATA"/>
	</array>
	<reg32 offset="0x00080" name="TRIG_CTRL">
		<bitfield name="DMA_TRIGGER" low="0" high="2" type="dsi_cmd_trigger"/>
		<bitfield name="MDP_TRIGGER" low="4" high="6" type="dsi_cmd_trigger"/>
		<bitfield name="STREAM" low="8" high="9" type="uint"/>
		<bitfield name="BLOCK_DMA_WITHIN_FRAME" pos="12" type="boolean"/>
		<bitfield name="TE" pos="31" type="boolean"/>
	</reg32>
	<reg32 offset="0x0008c" name="TRIG_DMA"/>
	<reg32 offset="0x000b0" name="DLN0_PHY_ERR">
		<bitfield name="DLN0_ERR_ESC" pos="0" type="boolean"/>
		<bitfield name="DLN0_ERR_SYNC_ESC" pos="4" type="boolean"/>
		<bitfield name="DLN0_ERR_CONTROL" pos="8" type="boolean"/>
		<bitfield name="DLN0_ERR_CONTENTION_LP0" pos="12" type="boolean"/>
		<bitfield name="DLN0_ERR_CONTENTION_LP1" pos="16" type="boolean"/>
	</reg32>
	<reg32 offset="0x000b4" name="LP_TIMER_CTRL">
		<bitfield name="LP_RX_TO" low="0" high="15" type="uint"/>
		<bitfield name="BTA_TO" low="16" high="31" type="uint"/>
	</reg32>
	<reg32 offset="0x000b8" name="HS_TIMER_CTRL">
		<bitfield name="HS_TX_TO" low="0" high="15" type="uint"/>
		<bitfield name="TIMER_RESOLUTION" low="16" high="19" type="uint"/>
		<bitfield name="HS_TX_TO_STOP_EN" pos="28" type="boolean"/>
	</reg32>
	<reg32 offset="0x000bc" name="TIMEOUT_STATUS"/>
	<reg32 offset="0x000c0" name="CLKOUT_TIMING_CTRL">
		<bitfield name="T_CLK_PRE" low="0" high="5" type="uint"/>
		<bitfield name="T_CLK_POST" low="8" high="13" type="uint"/>
	</reg32>
	<reg32 offset="0x000c8" name="EOT_PACKET_CTRL">
		<bitfield name="TX_EOT_APPEND" pos="0" type="boolean"/>
		<bitfield name="RX_EOT_IGNORE" pos="4" type="boolean"/>
	</reg32>
	<reg32 offset="0x000a4" name="LANE_STATUS">
		<bitfield name="DLN0_STOPSTATE" pos="0" type="boolean"/>
		<bitfield name="DLN1_STOPSTATE" pos="1" type="boolean"/>
		<bitfield name="DLN2_STOPSTATE" pos="2" type="boolean"/>
		<bitfield name="DLN3_STOPSTATE" pos="3" type="boolean"/>
		<bitfield name="CLKLN_STOPSTATE" pos="4" type="boolean"/>
		<bitfield name="DLN0_ULPS_ACTIVE_NOT" pos="8" type="boolean"/>
		<bitfield name="DLN1_ULPS_ACTIVE_NOT" pos="9" type="boolean"/>
		<bitfield name="DLN2_ULPS_ACTIVE_NOT" pos="10" type="boolean"/>
		<bitfield name="DLN3_ULPS_ACTIVE_NOT" pos="11" type="boolean"/>
		<bitfield name="CLKLN_ULPS_ACTIVE_NOT" pos="12" type="boolean"/>
		<bitfield name="DLN0_DIRECTION" pos="16" type="boolean"/>
	</reg32>
	<reg32 offset="0x000a8" name="LANE_CTRL">
		<bitfield name="HS_REQ_SEL_PHY" pos="24" type="boolean"/>
		<bitfield name="CLKLN_HS_FORCE_REQUEST" pos="28" type="boolean"/>
	</reg32>
	<reg32 offset="0x000ac" name="LANE_SWAP_CTRL">
		<bitfield name="DLN_SWAP_SEL" low="0" high="2" type="dsi_lane_swap"/>
	</reg32>
	<reg32 offset="0x00108" name="ERR_INT_MASK0"/>
	<reg32 offset="0x0010c" name="INTR_CTRL" type="DSI_IRQ"/>
	<reg32 offset="0x00114" name="RESET"/>
	<reg32 offset="0x00118" name="CLK_CTRL">
		<bitfield name="AHBS_HCLK_ON" pos="0" type="boolean"/>
		<bitfield name="AHBM_SCLK_ON" pos="1" type="boolean"/>
		<bitfield name="PCLK_ON" pos="2" type="boolean"/>
		<bitfield name="DSICLK_ON" pos="3" type="boolean"/>
		<bitfield name="BYTECLK_ON" pos="4" type="boolean"/>
		<bitfield name="ESCCLK_ON" pos="5" type="boolean"/>
		<bitfield name="FORCE_ON_DYN_AHBM_HCLK" pos="9" type="boolean"/>
	</reg32>
	<reg32 offset="0x0011c" name="CLK_STATUS">
		<bitfield name="DSI_AON_AHBM_HCLK_ACTIVE" pos="0" type="boolean"/>
		<bitfield name="DSI_DYN_AHBM_HCLK_ACTIVE" pos="1" type="boolean"/>
		<bitfield name="DSI_AON_AHBS_HCLK_ACTIVE" pos="2" type="boolean"/>
		<bitfield name="DSI_DYN_AHBS_HCLK_ACTIVE" pos="3" type="boolean"/>
		<bitfield name="DSI_AON_DSICLK_ACTIVE" pos="4" type="boolean"/>
		<bitfield name="DSI_DYN_DSICLK_ACTIVE" pos="5" type="boolean"/>
		<bitfield name="DSI_AON_BYTECLK_ACTIVE" pos="6" type="boolean"/>
		<bitfield name="DSI_DYN_BYTECLK_ACTIVE" pos="7" type="boolean"/>
		<bitfield name="DSI_AON_ESCCLK_ACTIVE" pos="8" type="boolean"/>
		<bitfield name="DSI_AON_PCLK_ACTIVE" pos="9" type="boolean"/>
		<bitfield name="DSI_DYN_PCLK_ACTIVE" pos="10" type="boolean"/>
		<bitfield name="DSI_DYN_CMD_PCLK_ACTIVE" pos="12" type="boolean"/>
		<bitfield name="DSI_CMD_PCLK_ACTIVE" pos="13" type="boolean"/>
		<bitfield name="DSI_VID_PCLK_ACTIVE" pos="14" type="boolean"/>
		<bitfield name="DSI_CAM_BIST_PCLK_ACT" pos="15" type="boolean"/>
		<bitfield name="PLL_UNLOCKED" pos="16" type="boolean"/>
	</reg32>
	<reg32 offset="0x00128" name="PHY_RESET">
		<bitfield name="RESET" pos="0" type="boolean"/>
	</reg32>
	<reg32 offset="0x00160" name="TEST_PATTERN_GEN_VIDEO_INIT_VAL"/>
	<reg32 offset="0x00198" name="TPG_MAIN_CONTROL">
		<bitfield name="CHECKERED_RECTANGLE_PATTERN" pos="8" type="boolean"/>
	</reg32>
	<reg32 offset="0x001a0" name="TPG_VIDEO_CONFIG">
		<bitfield name="BPP" low="0" high="1" type="video_config_bpp"/>
		<bitfield name="RGB" pos="2" type="boolean"/>
	</reg32>
	<reg32 offset="0x00158" name="TEST_PATTERN_GEN_CTRL">
		<bitfield name="CMD_DMA_PATTERN_SEL" low="16" high="17" type="cmd_dma_pattern_sel"/>
		<bitfield name="CMD_MDP_STREAM0_PATTERN_SEL" low="8" high="9" type="cmd_mdp_stream0_pattern_sel"/>
		<bitfield name="VIDEO_PATTERN_SEL" low="4" high="5" type="video_pattern_sel"/>
		<bitfield name="TPG_DMA_FIFO_MODE" pos="2" type="boolean"/>
		<bitfield name="CMD_DMA_TPG_EN" pos="1" type="boolean"/>
		<bitfield name="EN" pos="0" type="boolean"/>
	</reg32>
	<reg32 offset="0x00168" name="TEST_PATTERN_GEN_CMD_MDP_INIT_VAL0"/>
	<reg32 offset="0x00180" name="TEST_PATTERN_GEN_CMD_STREAM0_TRIGGER">
		<bitfield name="SW_TRIGGER" pos="0" type="boolean"/>
	</reg32>
	<reg32 offset="0x0019c" name="TPG_MAIN_CONTROL2">
		<bitfield name="CMD_MDP0_CHECKERED_RECTANGLE_PATTERN" pos="7" type="boolean"/>
		<bitfield name="CMD_MDP1_CHECKERED_RECTANGLE_PATTERN" pos="16" type="boolean"/>
		<bitfield name="CMD_MDP2_CHECKERED_RECTANGLE_PATTERN" pos="25" type="boolean"/>
	</reg32>
	<reg32 offset="0x0017c" name="T_CLK_PRE_EXTEND">
		<bitfield name="INC_BY_2_BYTECLK" pos="0" type="boolean"/>
	</reg32>
	<reg32 offset="0x001b4" name="CMD_MODE_MDP_CTRL2">
		<bitfield name="DST_FORMAT2" low="0" high="3" type="dsi_cmd_dst_format"/>
		<bitfield name="R_SEL" pos="4" type="boolean"/>
		<bitfield name="G_SEL" pos="5" type="boolean"/>
		<bitfield name="B_SEL" pos="6" type="boolean"/>
		<bitfield name="BYTE_MSB_LSB_FLIP" pos="7" type="boolean"/>
		<bitfield name="RGB_SWAP" low="8" high="10" type="dsi_rgb_swap"/>
		<bitfield name="INPUT_RGB_SWAP" low="12" high="14" type="dsi_rgb_swap"/>
		<bitfield name="BURST_MODE" pos="16" type="boolean"/>
		<bitfield name="DATABUS_WIDEN" pos="20" type="boolean"/>
	</reg32>
	<reg32 offset="0x001b8" name="CMD_MODE_MDP_STREAM2_CTRL">
		<bitfield name="DATA_TYPE" low="0" high="5" type="uint"/>
		<bitfield name="VIRTUAL_CHANNEL" low="8" high="9" type="uint"/>
		<bitfield name="WORD_COUNT" low="16" high="31" type="uint"/>
	</reg32>
	<reg32 offset="0x001d0" name="RDBK_DATA_CTRL">
		<bitfield name="COUNT" low="16" high="23" type="uint"/>
		<bitfield name="CLR" pos="0" type="boolean"/>
	</reg32>
	<reg32 offset="0x001f0" name="VERSION">
		<bitfield name="MAJOR" low="24" high="31" type="uint"/>
	</reg32>
	<reg32 offset="0x002d4" name="CPHY_MODE_CTRL"/>
	<reg32 offset="0x0029c" name="VIDEO_COMPRESSION_MODE_CTRL">
		<bitfield name="WC" low="16" high="31" type="uint"/>
		<bitfield name="DATATYPE" low="8" high="13" type="uint"/>
		<bitfield name="PKT_PER_LINE" low="6" high="7" type="uint"/>
		<bitfield name="EOL_BYTE_NUM" low="4" high="5" type="uint"/>
		<bitfield name="EN" pos="0" type="boolean"/>
	</reg32>
	<reg32 offset="0x002a4" name="COMMAND_COMPRESSION_MODE_CTRL">
		<bitfield name="STREAM1_DATATYPE" low="24" high="29" type="uint"/>
		<bitfield name="STREAM1_PKT_PER_LINE" low="22" high="23" type="uint"/>
		<bitfield name="STREAM1_EOL_BYTE_NUM" low="20" high="21" type="uint"/>
		<bitfield name="STREAM1_EN" pos="16" type="boolean"/>
		<bitfield name="STREAM0_DATATYPE" low="8" high="13" type="uint"/>
		<bitfield name="STREAM0_PKT_PER_LINE" low="6" high="7" type="uint"/>
		<bitfield name="STREAM0_EOL_BYTE_NUM" low="4" high="5" type="uint"/>
		<bitfield name="STREAM0_EN" pos="0" type="boolean"/>
	</reg32>
	<reg32 offset="0x002a8" name="COMMAND_COMPRESSION_MODE_CTRL2">
		<bitfield name="STREAM1_SLICE_WIDTH" low="16" high="31" type="uint"/>
		<bitfield name="STREAM0_SLICE_WIDTH" low="0" high="15" type="uint"/>
	</reg32>

</domain>

</database>
