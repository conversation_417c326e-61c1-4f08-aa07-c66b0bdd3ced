<?xml version="1.0" encoding="UTF-8"?>
<database xmlns="http://nouveau.freedesktop.org/"
xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
xsi:schemaLocation="https://gitlab.freedesktop.org/freedreno/ rules-fd.xsd">
<import file="freedreno_copyright.xml"/>


<!-- random bits that seem same between mdp4 and mdp5 (ie. not much) -->

<enum name="mdp_chroma_samp_type">
    <value name="CHROMA_FULL"  value="0"/>
    <value name="CHROMA_H2V1" value="1"/>
    <value name="CHROMA_H1V2" value="2"/>
    <value name="CHROMA_420"  value="3"/>
</enum>

<enum name="mdp_fetch_type">
    <value name="MDP_PLANE_INTERLEAVED"   value="0"/>
    <value name="MDP_PLANE_PLANAR"        value="1"/>
    <value name="MDP_PLANE_PSEUDO_PLANAR" value="2"/>
</enum>

<enum name="mdp_mixer_stage_id">
	<value name="STAGE_UNUSED" value="0"/>
	<value name="STAGE_BASE" value="1"/>
	<value name="STAGE0" value="2"/>   <!-- zorder 0 -->
	<value name="STAGE1" value="3"/>   <!-- zorder 1 -->
	<value name="STAGE2" value="4"/>   <!-- zorder 2 -->
	<value name="STAGE3" value="5"/>   <!-- zorder 3 -->
	<value name="STAGE4" value="6"/>   <!-- zorder 4 -->
	<value name="STAGE5" value="7"/>   <!-- zorder 5 -->
	<value name="STAGE6" value="8"/>   <!-- zorder 6 -->
	<value name="STAGE_MAX" value="8"/> <!-- maximum zorder -->
</enum>

<enum name="mdp_alpha_type">
	<value name="FG_CONST" value="0"/>
	<value name="BG_CONST" value="1"/>
	<value name="FG_PIXEL" value="2"/>
	<value name="BG_PIXEL" value="3"/>
</enum>

<enum name="mdp_component_type">
        <value name="COMP_0" value="0"/>	<!-- Y component -->
        <value name="COMP_1_2" value="1"/>	<!-- Cb/Cr comp. -->
        <value name="COMP_3" value="2"/>	<!-- Trans comp. -->
        <value name="COMP_MAX" value="3"/>
</enum>

<enum name="mdp_bpc">
	<brief>bits per component (non-alpha channel)</brief>
	<value name="BPC4" value="0"/> <!-- 4 bits -->
	<value name="BPC5" value="1"/> <!-- 5 bits -->
	<value name="BPC6" value="2"/> <!-- 6 bits -->
	<value name="BPC8" value="3"/> <!-- 8 bits -->
</enum>

<enum name="mdp_bpc_alpha">
	<brief>bits per component (alpha channel)</brief>
	<value name="BPC1A" value="0"/> <!-- 1 bit -->
	<value name="BPC4A" value="1"/> <!-- 4 bits -->
	<value name="BPC6A" value="2"/> <!-- 6 bits -->
	<value name="BPC8A" value="3"/> <!-- 8 bits -->
</enum>

<enum name="mdp_fetch_mode">
	<value name="MDP_FETCH_LINEAR" value="0"/>
	<value name="MDP_FETCH_TILE" value="1"/>
	<value name="MDP_FETCH_UBWC" value="2"/>
</enum>

<bitset name="reg_wh" inline="yes">
    <bitfield name="HEIGHT" low="16" high="31" type="uint"/>
    <bitfield name="WIDTH" low="0" high="15" type="uint"/>
</bitset>

<bitset name="reg_xy" inline="yes">
    <bitfield name="Y" low="16" high="31" type="uint"/>
    <bitfield name="X" low="0" high="15" type="uint"/>
</bitset>

<bitset name="mdp_unpack_pattern" inline="yes">
	<bitfield name="ELEM0" low="0"  high="7"/>
	<bitfield name="ELEM1" low="8"  high="15"/>
	<bitfield name="ELEM2" low="16" high="23"/>
	<bitfield name="ELEM3" low="24" high="31"/>
</bitset>

</database>

