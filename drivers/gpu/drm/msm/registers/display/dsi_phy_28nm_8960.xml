<?xml version="1.0" encoding="UTF-8"?>
<database xmlns="http://nouveau.freedesktop.org/"
xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
xsi:schemaLocation="https://gitlab.freedesktop.org/freedreno/ rules-fd.xsd">
<import file="freedreno_copyright.xml"/>

<domain name="DSI_28nm_8960_PHY" width="32">

	<array offset="0x00000" name="LN" length="4" stride="0x40">
		<reg32 offset="0x00" name="CFG_0"/>
		<reg32 offset="0x04" name="CFG_1"/>
		<reg32 offset="0x08" name="CFG_2"/>
		<reg32 offset="0x0c" name="TEST_DATAPATH"/>
		<reg32 offset="0x14" name="TEST_STR_0"/>
		<reg32 offset="0x18" name="TEST_STR_1"/>
	</array>

	<reg32 offset="0x00100" name="LNCK_CFG_0"/>
	<reg32 offset="0x00104" name="LNCK_CFG_1"/>
	<reg32 offset="0x00108" name="LNCK_CFG_2"/>

	<reg32 offset="0x0010c" name="LNCK_TEST_DATAPATH"/>
	<reg32 offset="0x00114" name="LNCK_TEST_STR0"/>
	<reg32 offset="0x00118" name="LNCK_TEST_STR1"/>

	<reg32 offset="0x00140" name="TIMING_CTRL_0">
		<bitfield name="CLK_ZERO" low="0" high="7" type="uint"/>
	</reg32>
	<reg32 offset="0x00144" name="TIMING_CTRL_1">
		<bitfield name="CLK_TRAIL" low="0" high="7" type="uint"/>
	</reg32>
	<reg32 offset="0x00148" name="TIMING_CTRL_2">
		<bitfield name="CLK_PREPARE" low="0" high="7" type="uint"/>
	</reg32>

	<reg32 offset="0x0014c" name="TIMING_CTRL_3"/>

	<reg32 offset="0x00150" name="TIMING_CTRL_4">
		<bitfield name="HS_EXIT" low="0" high="7" type="uint"/>
	</reg32>
	<reg32 offset="0x00154" name="TIMING_CTRL_5">
		<bitfield name="HS_ZERO" low="0" high="7" type="uint"/>
	</reg32>
	<reg32 offset="0x00158" name="TIMING_CTRL_6">
		<bitfield name="HS_PREPARE" low="0" high="7" type="uint"/>
	</reg32>
	<reg32 offset="0x0015c" name="TIMING_CTRL_7">
		<bitfield name="HS_TRAIL" low="0" high="7" type="uint"/>
	</reg32>
	<reg32 offset="0x00160" name="TIMING_CTRL_8">
		<bitfield name="HS_RQST" low="0" high="7" type="uint"/>
	</reg32>
	<reg32 offset="0x00164" name="TIMING_CTRL_9">
		<bitfield name="TA_GO" low="0" high="2" type="uint"/>
		<bitfield name="TA_SURE" low="4" high="6" type="uint"/>
	</reg32>
	<reg32 offset="0x00168" name="TIMING_CTRL_10">
		<bitfield name="TA_GET" low="0" high="2" type="uint"/>
	</reg32>
	<reg32 offset="0x0016c" name="TIMING_CTRL_11">
		<bitfield name="TRIG3_CMD" low="0" high="7" type="uint"/>
	</reg32>

	<reg32 offset="0x00170" name="CTRL_0"/>
	<reg32 offset="0x00174" name="CTRL_1"/>
	<reg32 offset="0x00178" name="CTRL_2"/>
	<reg32 offset="0x0017c" name="CTRL_3"/>

	<reg32 offset="0x00180" name="STRENGTH_0"/>
	<reg32 offset="0x00184" name="STRENGTH_1"/>
	<reg32 offset="0x00188" name="STRENGTH_2"/>

	<reg32 offset="0x0018c" name="BIST_CTRL_0"/>
	<reg32 offset="0x00190" name="BIST_CTRL_1"/>
	<reg32 offset="0x00194" name="BIST_CTRL_2"/>
	<reg32 offset="0x00198" name="BIST_CTRL_3"/>
	<reg32 offset="0x0019c" name="BIST_CTRL_4"/>

	<reg32 offset="0x001b0" name="LDO_CTRL"/>
</domain>

<domain name="DSI_28nm_8960_PHY_MISC" width="32">
	<reg32 offset="0x00000" name="REGULATOR_CTRL_0"/>
	<reg32 offset="0x00004" name="REGULATOR_CTRL_1"/>
	<reg32 offset="0x00008" name="REGULATOR_CTRL_2"/>
	<reg32 offset="0x0000c" name="REGULATOR_CTRL_3"/>
	<reg32 offset="0x00010" name="REGULATOR_CTRL_4"/>
	<reg32 offset="0x00014" name="REGULATOR_CTRL_5"/>
	<reg32 offset="0x00018" name="REGULATOR_CAL_PWR_CFG"/>
	<reg32 offset="0x00028" name="CAL_HW_TRIGGER"/>
	<reg32 offset="0x0002c" name="CAL_SW_CFG_0"/>
	<reg32 offset="0x00030" name="CAL_SW_CFG_1"/>
	<reg32 offset="0x00034" name="CAL_SW_CFG_2"/>
	<reg32 offset="0x00038" name="CAL_HW_CFG_0"/>
	<reg32 offset="0x0003c" name="CAL_HW_CFG_1"/>
	<reg32 offset="0x00040" name="CAL_HW_CFG_2"/>
	<reg32 offset="0x00044" name="CAL_HW_CFG_3"/>
	<reg32 offset="0x00048" name="CAL_HW_CFG_4"/>
	<reg32 offset="0x00050" name="CAL_STATUS">
		<bitfield name="CAL_BUSY" pos="4" type="boolean"/>
	</reg32>
</domain>

<domain name="DSI_28nm_8960_PHY_PLL" width="32">
	<reg32 offset="0x00000" name="CTRL_0">
		<bitfield name="ENABLE" pos="0" type="boolean"/>
	</reg32>
	<reg32 offset="0x00004" name="CTRL_1"/>
	<reg32 offset="0x00008" name="CTRL_2"/>
	<reg32 offset="0x0000c" name="CTRL_3"/>
	<reg32 offset="0x00010" name="CTRL_4"/>
	<reg32 offset="0x00014" name="CTRL_5"/>
	<reg32 offset="0x00018" name="CTRL_6"/>
	<reg32 offset="0x0001c" name="CTRL_7"/>
	<reg32 offset="0x00020" name="CTRL_8"/>
	<reg32 offset="0x00024" name="CTRL_9"/>
	<reg32 offset="0x00028" name="CTRL_10"/>
	<reg32 offset="0x0002c" name="CTRL_11"/>
	<reg32 offset="0x00030" name="CTRL_12"/>
	<reg32 offset="0x00034" name="CTRL_13"/>
	<reg32 offset="0x00038" name="CTRL_14"/>
	<reg32 offset="0x0003c" name="CTRL_15"/>
	<reg32 offset="0x00040" name="CTRL_16"/>
	<reg32 offset="0x00044" name="CTRL_17"/>
	<reg32 offset="0x00048" name="CTRL_18"/>
	<reg32 offset="0x0004c" name="CTRL_19"/>
	<reg32 offset="0x00050" name="CTRL_20"/>

	<reg32 offset="0x00080" name="RDY">
		<bitfield name="PLL_RDY" pos="0" type="boolean"/>
	</reg32>
</domain>

</database>
