<?xml version="1.0" encoding="UTF-8"?>
<database xmlns="http://nouveau.freedesktop.org/"
xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
xsi:schemaLocation="https://gitlab.freedesktop.org/freedreno/ rules-fd.xsd">
<import file="freedreno_copyright.xml"/>
<import file="display/mdp_common.xml"/>

<domain name="MDP4" width="32">
	<enum name="mdp4_pipe">
		<brief>pipe names, index into PIPE[]</brief>
		<value name="VG1" value="0"/>
		<value name="VG2" value="1"/>
		<value name="RGB1" value="2"/>
		<value name="RGB2" value="3"/>
		<value name="RGB3" value="4"/>
		<value name="VG3" value="5"/>
		<value name="VG4" value="6"/>
	</enum>

	<enum name="mdp4_mixer">
		<value name="MIXER0" value="0"/>
		<value name="MIXER1" value="1"/>
		<value name="MIXER2" value="2"/>
	</enum>

	<enum name="mdp4_intf">
		<!--
			A bit confusing the enums for interface selection:
				enum {
					LCDC_RGB_INTF,			/* 0 */
					DTV_INTF = LCDC_RGB_INTF,	/* 0 */
					MDDI_LCDC_INTF,			/* 1 */
					MDDI_INTF,			/* 2 */
					EBI2_INTF,			/* 3 */
					TV_INTF = EBI2_INTF,		/* 3 */
					DSI_VIDEO_INTF,
					DSI_CMD_INTF
				};
			there is some overlap, and not all the values end up getting
			written to hw (mdp4_display_intf_sel() remaps the last two
			values to MDDI_LCDC_INTF/MDDI_INTF with extra bits set).. so
			taking some liberties in guessing the actual meanings/names:
		 -->
		<value name="INTF_LCDC_DTV" value="0"/>  <!-- LCDC RGB or DTV (external) -->
		<value name="INTF_DSI_VIDEO" value="1"/>
		<value name="INTF_DSI_CMD" value="2"/>
		<value name="INTF_EBI2_TV" value="3"/>   <!-- EBI2 or TV (external) -->
	</enum>
	<enum name="mdp4_cursor_format">
		<value name="CURSOR_ARGB" value="1"/>
		<value name="CURSOR_XRGB" value="2"/>
	</enum>
	<enum name="mdp4_frame_format">
		<value name="FRAME_LINEAR" value="0"/>
		<value name="FRAME_TILE_ARGB_4X4" value="1"/>
		<value name="FRAME_TILE_YCBCR_420" value="2"/>
	</enum>
	<enum name="mdp4_scale_unit">
		<value name="SCALE_FIR" value="0"/>
		<value name="SCALE_MN_PHASE" value="1"/>
		<value name="SCALE_PIXEL_RPT" value="2"/>
	</enum>

	<bitset name="mdp4_layermixer_in_cfg" inline="yes">
		<brief>appears to map pipe to mixer stage</brief>
		<bitfield name="PIPE0" low="0"  high="2"  type="mdp_mixer_stage_id"/>
		<bitfield name="PIPE0_MIXER1" pos="3" type="boolean"/>
		<bitfield name="PIPE1" low="4"  high="6"  type="mdp_mixer_stage_id"/>
		<bitfield name="PIPE1_MIXER1" pos="7" type="boolean"/>
		<bitfield name="PIPE2" low="8"  high="10" type="mdp_mixer_stage_id"/>
		<bitfield name="PIPE2_MIXER1" pos="11" type="boolean"/>
		<bitfield name="PIPE3" low="12" high="14" type="mdp_mixer_stage_id"/>
		<bitfield name="PIPE3_MIXER1" pos="15" type="boolean"/>
		<bitfield name="PIPE4" low="16" high="18" type="mdp_mixer_stage_id"/>
		<bitfield name="PIPE4_MIXER1" pos="19" type="boolean"/>
		<bitfield name="PIPE5" low="20" high="22" type="mdp_mixer_stage_id"/>
		<bitfield name="PIPE5_MIXER1" pos="23" type="boolean"/>
		<bitfield name="PIPE6" low="24" high="26" type="mdp_mixer_stage_id"/>
		<bitfield name="PIPE6_MIXER1" pos="27" type="boolean"/>
		<bitfield name="PIPE7" low="28" high="30" type="mdp_mixer_stage_id"/>
		<bitfield name="PIPE7_MIXER1" pos="31" type="boolean"/>
	</bitset>

	<bitset name="MDP4_IRQ">
		<bitfield name="OVERLAY0_DONE" pos="0" type="boolean"/>
		<bitfield name="OVERLAY1_DONE" pos="1" type="boolean"/>
		<bitfield name="DMA_S_DONE" pos="2" type="boolean"/>
		<bitfield name="DMA_E_DONE" pos="3" type="boolean"/>
		<bitfield name="DMA_P_DONE" pos="4" type="boolean"/>
		<bitfield name="VG1_HISTOGRAM" pos="5" type="boolean"/>
		<bitfield name="VG2_HISTOGRAM" pos="6" type="boolean"/>
		<bitfield name="PRIMARY_VSYNC" pos="7" type="boolean"/>
		<bitfield name="PRIMARY_INTF_UDERRUN" pos="8" type="boolean"/>
		<bitfield name="EXTERNAL_VSYNC" pos="9" type="boolean"/>
		<bitfield name="EXTERNAL_INTF_UDERRUN" pos="10" type="boolean"/>
		<bitfield name="PRIMARY_RDPTR" pos="11" type="boolean"/>  <!-- read pointer -->
		<bitfield name="DMA_P_HISTOGRAM" pos="17" type="boolean"/>
		<bitfield name="DMA_S_HISTOGRAM" pos="26" type="boolean"/>
		<bitfield name="OVERLAY2_DONE" pos="30" type="boolean"/>
	</bitset>

	<reg32 offset="0x00000" name="VERSION">
		<!--
			from mdp_probe() we can see minor rev starts at 16.. assume
			major is above that.. not sure the rest of bits but doesn't
			really seem to matter
		 -->
		<bitfield name="MINOR" low="16" high="23" type="uint"/>
		<bitfield name="MAJOR" low="24" high="31" type="uint"/>
	</reg32>
	<reg32 offset="0x00004" name="OVLP0_KICK"/>
	<reg32 offset="0x00008" name="OVLP1_KICK"/>
	<reg32 offset="0x000d0" name="OVLP2_KICK"/>
	<reg32 offset="0x0000c" name="DMA_P_KICK"/>
	<reg32 offset="0x00010" name="DMA_S_KICK"/>
	<reg32 offset="0x00014" name="DMA_E_KICK"/>
	<reg32 offset="0x00018" name="DISP_STATUS"/>

	<reg32 offset="0x00038" name="DISP_INTF_SEL">
		<bitfield name="PRIM" low="0" high="1" type="mdp4_intf"/>
		<bitfield name="SEC" low="2" high="3" type="mdp4_intf"/>
		<bitfield name="EXT" low="4" high="5" type="mdp4_intf"/>
		<bitfield name="DSI_VIDEO" pos="6" type="boolean"/>
		<bitfield name="DSI_CMD" pos="7" type="boolean"/>
	</reg32>
	<reg32 offset="0x0003c" name="RESET_STATUS"/>  <!-- only mdp4 >v2.1 -->
	<reg32 offset="0x0004c" name="READ_CNFG"/>  <!-- something about # of pending requests.. -->
	<reg32 offset="0x00050" name="INTR_ENABLE" type="MDP4_IRQ"/>
	<reg32 offset="0x00054" name="INTR_STATUS" type="MDP4_IRQ"/>
	<reg32 offset="0x00058" name="INTR_CLEAR" type="MDP4_IRQ"/>
	<reg32 offset="0x00060" name="EBI2_LCD0"/>
	<reg32 offset="0x00064" name="EBI2_LCD1"/>
	<reg32 offset="0x00070" name="PORTMAP_MODE"/>

	<!-- mdp chip-select controller: -->
	<reg32 offset="0x000c0" name="CS_CONTROLLER0"/>
	<reg32 offset="0x000c4" name="CS_CONTROLLER1"/>

	<reg32 offset="0x100f0" name="LAYERMIXER2_IN_CFG" type="mdp4_layermixer_in_cfg"/>
	<reg32 offset="0x100fc" name="LAYERMIXER_IN_CFG_UPDATE_METHOD"/>
	<reg32 offset="0x10100" name="LAYERMIXER_IN_CFG" type="mdp4_layermixer_in_cfg"/>

	<reg32 offset="0x30050" name="VG2_SRC_FORMAT"/>
	<reg32 offset="0x31008" name="VG2_CONST_COLOR"/>

	<reg32 offset="0x18000" name="OVERLAY_FLUSH">
		<bitfield name="OVLP0" pos="0" type="boolean"/>
		<bitfield name="OVLP1" pos="1" type="boolean"/>
		<bitfield name="VG1" pos="2" type="boolean"/>
		<bitfield name="VG2" pos="3" type="boolean"/>
		<bitfield name="RGB1" pos="4" type="boolean"/>
		<bitfield name="RGB2" pos="5" type="boolean"/>
	</reg32>

	<array offsets="0x10000,0x18000,0x88000" name="OVLP" length="3" stride="0x8000">
		<reg32 offset="0x0004" name="CFG"/>
		<reg32 offset="0x0008" name="SIZE" type="reg_wh"/>
		<reg32 offset="0x000c" name="BASE"/>
		<reg32 offset="0x0010" name="STRIDE" type="uint"/>
		<reg32 offset="0x0014" name="OPMODE"/>

		<array offsets="0x0104,0x0124,0x0144,0x0160" name="STAGE" length="4" stride="0x1c">
			<reg32 offset="0x00" name="OP">
				<bitfield name="FG_ALPHA" low="0" high="1" type="mdp_alpha_type"/>
				<bitfield name="FG_INV_ALPHA" pos="2" type="boolean"/>
				<bitfield name="FG_MOD_ALPHA" pos="3" type="boolean"/>
				<bitfield name="BG_ALPHA" low="4" high="5" type="mdp_alpha_type"/>
				<bitfield name="BG_INV_ALPHA" pos="6" type="boolean"/>
				<bitfield name="BG_MOD_ALPHA" pos="7" type="boolean"/>
				<bitfield name="FG_TRANSP" pos="8" type="boolean"/>
				<bitfield name="BG_TRANSP" pos="9" type="boolean"/>
			</reg32>
			<reg32 offset="0x04" name="FG_ALPHA"/>
			<reg32 offset="0x08" name="BG_ALPHA"/>
			<reg32 offset="0x0c" name="TRANSP_LOW0"/>
			<reg32 offset="0x10" name="TRANSP_LOW1"/>
			<reg32 offset="0x14" name="TRANSP_HIGH0"/>
			<reg32 offset="0x18" name="TRANSP_HIGH1"/>
		</array>

		<array offsets="0x1004,0x1404,0x1804,0x1b84" name="STAGE_CO3" length="4" stride="4">
			<reg32 offset="0" name="SEL">
				<bitfield name="FG_ALPHA" pos="0" type="boolean"/> <!-- otherwise bg alpha -->
			</reg32>
		</array>

		<reg32 offset="0x0180" name="TRANSP_LOW0"/>
		<reg32 offset="0x0184" name="TRANSP_LOW1"/>
		<reg32 offset="0x0188" name="TRANSP_HIGH0"/>
		<reg32 offset="0x018c" name="TRANSP_HIGH1"/>

		<reg32 offset="0x0200" name="CSC_CONFIG"/>

		<array offset="0x2000" name="CSC" length="1" stride="0x700">
			<array offset="0x400" name="MV" length="9" stride="4">
				<reg32 offset="0" name="VAL"/>
			</array>
			<array offset="0x500" name="PRE_BV" length="3" stride="4">
				<reg32 offset="0" name="VAL"/>
			</array>
			<array offset="0x580" name="POST_BV" length="3" stride="4">
				<reg32 offset="0" name="VAL"/>
			</array>
			<array offset="0x600" name="PRE_LV" length="6" stride="4">
				<reg32 offset="0" name="VAL"/>
			</array>
			<array offset="0x680" name="POST_LV" length="6" stride="4">
				<reg32 offset="0" name="VAL"/>
			</array>
		</array>
	</array>

	<enum name="mdp4_dma">
		<value name="DMA_P" value="0"/>
		<value name="DMA_S" value="1"/>
		<value name="DMA_E" value="2"/>
	</enum>
	<reg32 offset="0x90070" name="DMA_P_OP_MODE"/>
	<array offset="0x94800" name="LUTN" length="2" stride="0x400">
		<array offset="0" name="LUT" length="0x100" stride="4">
			<reg32 offset="0" name="VAL"/>
		</array>
	</array>
	<reg32 offset="0xa0028" name="DMA_S_OP_MODE"/>
	<!-- I guess if DMA_S has an OP_MODE, it must have a LUT too.. -->
	<reg32 offset="0xb0070" name="DMA_E_QUANT" length="3" stride="4"/>
	<array offsets="0x90000,0xa0000,0xb0000" name="DMA" length="3" stride="0x10000" index="mdp4_dma">
		<reg32 offset="0x0000" name="CONFIG">
			<bitfield name="G_BPC" low="0" high="1" type="mdp_bpc"/>
			<bitfield name="B_BPC" low="2" high="3" type="mdp_bpc"/>
			<bitfield name="R_BPC" low="4" high="5" type="mdp_bpc"/>
			<bitfield name="PACK_ALIGN_MSB" pos="7" type="boolean"/>
			<bitfield name="PACK" low="8" high="15"/>
			<!-- bit 24 is DITHER_EN on DMA_P, DEFLKR_EN on DMA_E -->
			<bitfield name="DEFLKR_EN" pos="24" type="boolean"/>
			<bitfield name="DITHER_EN" pos="24" type="boolean"/>
		</reg32>
		<reg32 offset="0x0004" name="SRC_SIZE" type="reg_wh"/>
		<reg32 offset="0x0008" name="SRC_BASE"/>
		<reg32 offset="0x000c" name="SRC_STRIDE" type="uint"/>
		<reg32 offset="0x0010" name="DST_SIZE" type="reg_wh"/>

		<reg32 offset="0x0044" name="CURSOR_SIZE">
			<!-- seems the limit is 64x64: -->
			<bitfield name="WIDTH" low="0" high="6" type="uint"/>
			<bitfield name="HEIGHT" low="16" high="22" type="uint"/>
		</reg32>
		<reg32 offset="0x0048" name="CURSOR_BASE"/>
		<reg32 offset="0x004c" name="CURSOR_POS">
			<bitfield name="X" low="0" high="15" type="uint"/>
			<bitfield name="Y" low="16" high="31" type="uint"/>
		</reg32>
		<reg32 offset="0x0060" name="CURSOR_BLEND_CONFIG">
			<bitfield name="CURSOR_EN" pos="0" type="boolean"/>
			<bitfield name="FORMAT" low="1" high="2" type="mdp4_cursor_format"/>
			<bitfield name="TRANSP_EN" pos="3" type="boolean"/>
		</reg32>
		<reg32 offset="0x0064" name="CURSOR_BLEND_PARAM"/>
		<reg32 offset="0x0068" name="BLEND_TRANS_LOW"/>
		<reg32 offset="0x006c" name="BLEND_TRANS_HIGH"/>

		<reg32 offset="0x1004" name="FETCH_CONFIG"/>
		<array offset="0x3000" name="CSC" length="1" stride="0x700">
			<array offset="0x400" name="MV" length="9" stride="4">
				<reg32 offset="0" name="VAL"/>
			</array>
			<array offset="0x500" name="PRE_BV" length="3" stride="4">
				<reg32 offset="0" name="VAL"/>
			</array>
			<array offset="0x580" name="POST_BV" length="3" stride="4">
				<reg32 offset="0" name="VAL"/>
			</array>
			<array offset="0x600" name="PRE_LV" length="6" stride="4">
				<reg32 offset="0" name="VAL"/>
			</array>
			<array offset="0x680" name="POST_LV" length="6" stride="4">
				<reg32 offset="0" name="VAL"/>
			</array>
		</array>
	</array>

	<!--
		TODO length should be 7, but that would collide w/ OVLP2..!?!
		this register map is a bit strange..
	 -->
	<array offset="0x20000" name="PIPE" length="6" stride="0x10000" index="mdp4_pipe">
		<reg32 offset="0x0000" name="SRC_SIZE" type="reg_wh"/>
		<reg32 offset="0x0004" name="SRC_XY" type="reg_xy"/>
		<reg32 offset="0x0008" name="DST_SIZE" type="reg_wh"/>
		<reg32 offset="0x000c" name="DST_XY" type="reg_xy"/>
		<reg32 offset="0x0010" name="SRCP0_BASE"/>
		<reg32 offset="0x0014" name="SRCP1_BASE"/>
		<reg32 offset="0x0018" name="SRCP2_BASE"/>
		<reg32 offset="0x001c" name="SRCP3_BASE"/>
		<reg32 offset="0x0040" name="SRC_STRIDE_A">
			<bitfield name="P0" low="0" high="15" type="uint"/>
			<bitfield name="P1" low="16" high="31" type="uint"/>
		</reg32>
		<reg32 offset="0x0044" name="SRC_STRIDE_B">
			<bitfield name="P2" low="0" high="15" type="uint"/>
			<bitfield name="P3" low="16" high="31" type="uint"/>
		</reg32>
		<reg32 offset="0x0048" name="SSTILE_FRAME_SIZE" type="reg_wh"/>
		<reg32 offset="0x0050" name="SRC_FORMAT">
			<bitfield name="G_BPC" low="0" high="1" type="mdp_bpc"/>
			<bitfield name="B_BPC" low="2" high="3" type="mdp_bpc"/>
			<bitfield name="R_BPC" low="4" high="5" type="mdp_bpc"/>
			<bitfield name="A_BPC" low="6" high="7" type="mdp_bpc_alpha"/>
			<bitfield name="ALPHA_ENABLE" pos="8" type="boolean"/>
			<bitfield name="CPP" low="9" high="10" type="uint">
				<brief>8bit characters per pixel minus 1</brief>
			</bitfield>
			<bitfield name="ROTATED_90" pos="12" type="boolean"/>
			<bitfield name="UNPACK_COUNT" low="13" high="14" type="uint"/>
			<bitfield name="UNPACK_TIGHT" pos="17" type="boolean"/>
			<bitfield name="UNPACK_ALIGN_MSB" pos="18" type="boolean"/>
			<bitfield name="FETCH_PLANES" low="19" high="20" type="uint"/>
			<bitfield name="SOLID_FILL" pos="22" type="boolean"/>
			<bitfield name="CHROMA_SAMP" low="26" high="27" type="mdp_chroma_samp_type"/>
			<bitfield name="FRAME_FORMAT" low="29" high="30" type="mdp4_frame_format"/>
		</reg32>
		<reg32 offset="0x0054" name="SRC_UNPACK" type="mdp_unpack_pattern"/>
		<reg32 offset="0x0058" name="OP_MODE">
			<bitfield name="SCALEX_EN" pos="0" type="boolean"/>
			<bitfield name="SCALEY_EN" pos="1" type="boolean"/>
			<bitfield name="SCALEX_UNIT_SEL" low="2" high="3" type="mdp4_scale_unit"/>
			<bitfield name="SCALEY_UNIT_SEL" low="4" high="5" type="mdp4_scale_unit"/>
			<bitfield name="SRC_YCBCR" pos="9" type="boolean"/>
			<bitfield name="DST_YCBCR" pos="10" type="boolean"/>
			<bitfield name="CSC_EN" pos="11" type="boolean"/>
			<bitfield name="FLIP_LR" pos="13" type="boolean"/>
			<bitfield name="FLIP_UD" pos="14" type="boolean"/>
			<bitfield name="DITHER_EN" pos="15" type="boolean"/>
			<bitfield name="IGC_LUT_EN" pos="16" type="boolean"/>
			<bitfield name="DEINT_EN" pos="18" type="boolean"/>
			<bitfield name="DEINT_ODD_REF" pos="19" type="boolean"/>
		</reg32>
		<reg32 offset="0x005c" name="PHASEX_STEP"/>
		<reg32 offset="0x0060" name="PHASEY_STEP"/>
		<reg32 offset="0x1004" name="FETCH_CONFIG"/>
		<reg32 offset="0x1008" name="SOLID_COLOR"/>

		<array offset="0x4000" name="CSC" length="1" stride="0x700">
			<array offset="0x400" name="MV" length="9" stride="4">
				<reg32 offset="0" name="VAL"/>
			</array>
			<array offset="0x500" name="PRE_BV" length="3" stride="4">
				<reg32 offset="0" name="VAL"/>
			</array>
			<array offset="0x580" name="POST_BV" length="3" stride="4">
				<reg32 offset="0" name="VAL"/>
			</array>
			<array offset="0x600" name="PRE_LV" length="6" stride="4">
				<reg32 offset="0" name="VAL"/>
			</array>
			<array offset="0x680" name="POST_LV" length="6" stride="4">
				<reg32 offset="0" name="VAL"/>
			</array>
		</array>
	</array>

	<!--
		ENCODERS
			LCDC and DSI seem the same, DTV is just slightly different..
	 -->

	<bitset name="mdp4_ctrl_polarity" inline="yes">
		<!-- not entirely sure if these bits mean hi or low.. -->
		<bitfield name="HSYNC_LOW" pos="0" type="boolean"/>
		<bitfield name="VSYNC_LOW" pos="1" type="boolean"/>
		<bitfield name="DATA_EN_LOW" pos="2" type="boolean"/>
	</bitset>

	<bitset name="mdp4_active_hctl" inline="yes">
		<bitfield name="START" low="0" high="14" type="uint"/>
		<bitfield name="END" low="16" high="30" type="uint"/>
		<bitfield name="ACTIVE_START_X" pos="31" type="boolean"/>
	</bitset>

	<bitset name="mdp4_display_hctl" inline="yes">
		<bitfield name="START" low="0" high="15" type="uint"/>
		<bitfield name="END" low="16" high="31" type="uint"/>
	</bitset>

	<bitset name="mdp4_hsync_ctrl" inline="yes">
		<bitfield name="PULSEW" low="0" high="15" type="uint"/>
		<bitfield name="PERIOD" low="16" high="31" type="uint"/>
	</bitset>

	<bitset name="mdp4_underflow_clr" inline="yes">
		<bitfield name="COLOR" low="0" high="23"/>
		<bitfield name="ENABLE_RECOVERY" pos="31" type="boolean"/>
	</bitset>

	<!-- offset is 0xe0000 on !mdp4.. -->
	<array offset="0xc0000" name="LCDC" length="1" stride="0x1000">
		<reg32 offset="0x0000" name="ENABLE"/>
		<reg32 offset="0x0004" name="HSYNC_CTRL" type="mdp4_hsync_ctrl"/>
		<reg32 offset="0x0008" name="VSYNC_PERIOD" type="uint"/>
		<reg32 offset="0x000c" name="VSYNC_LEN" type="uint"/>
		<reg32 offset="0x0010" name="DISPLAY_HCTRL" type="mdp4_display_hctl"/>
		<reg32 offset="0x0014" name="DISPLAY_VSTART" type="uint"/>
		<reg32 offset="0x0018" name="DISPLAY_VEND" type="uint"/>
		<reg32 offset="0x001c" name="ACTIVE_HCTL" type="mdp4_active_hctl"/>
		<reg32 offset="0x0020" name="ACTIVE_VSTART" type="uint"/>
		<reg32 offset="0x0024" name="ACTIVE_VEND" type="uint"/>
		<reg32 offset="0x0028" name="BORDER_CLR"/>
		<reg32 offset="0x002c" name="UNDERFLOW_CLR" type="mdp4_underflow_clr"/>
		<reg32 offset="0x0030" name="HSYNC_SKEW"/>
		<reg32 offset="0x0034" name="TEST_CNTL"/>
		<reg32 offset="0x0038" name="CTRL_POLARITY" type="mdp4_ctrl_polarity"/>
	</array>

	<reg32 offset="0xc2000" name="LCDC_LVDS_INTF_CTL">
		<bitfield name="MODE_SEL"           pos="2"  type="boolean"/>
		<bitfield name="RGB_OUT"            pos="3"  type="boolean"/>
		<bitfield name="CH_SWAP"            pos="4"  type="boolean"/>
		<bitfield name="CH1_RES_BIT"        pos="5"  type="boolean"/>
		<bitfield name="CH2_RES_BIT"        pos="6"  type="boolean"/>
		<bitfield name="ENABLE"             pos="7"  type="boolean"/>
		<bitfield name="CH1_DATA_LANE0_EN"  pos="8"  type="boolean"/>
		<bitfield name="CH1_DATA_LANE1_EN"  pos="9"  type="boolean"/>
		<bitfield name="CH1_DATA_LANE2_EN"  pos="10" type="boolean"/>
		<bitfield name="CH1_DATA_LANE3_EN"  pos="11" type="boolean"/>
		<bitfield name="CH2_DATA_LANE0_EN"  pos="12" type="boolean"/>
		<bitfield name="CH2_DATA_LANE1_EN"  pos="13" type="boolean"/>
		<bitfield name="CH2_DATA_LANE2_EN"  pos="14" type="boolean"/>
		<bitfield name="CH2_DATA_LANE3_EN"  pos="15" type="boolean"/>
		<bitfield name="CH1_CLK_LANE_EN"    pos="16" type="boolean"/>
		<bitfield name="CH2_CLK_LANE_EN"    pos="17" type="boolean"/>
	</reg32>

	<array offset="0xc2014" name="LCDC_LVDS_MUX_CTL" length="4" stride="0x8">
		<reg32 offset="0x0" name="3_TO_0">
			<bitfield name="BIT0" low="0"  high="7"/>
			<bitfield name="BIT1" low="8"  high="15"/>
			<bitfield name="BIT2" low="16" high="23"/>
			<bitfield name="BIT3" low="24" high="31"/>
		</reg32>
		<reg32 offset="0x4" name="6_TO_4">
			<bitfield name="BIT4" low="0"  high="7"/>
			<bitfield name="BIT5" low="8"  high="15"/>
			<bitfield name="BIT6" low="16" high="23"/>
		</reg32>
	</array>

	<reg32 offset="0xc2034" name="LCDC_LVDS_PHY_RESET"/>

	<reg32 offset="0xc3000" name="LVDS_PHY_PLL_CTRL_0"/>
	<reg32 offset="0xc3004" name="LVDS_PHY_PLL_CTRL_1"/>
	<reg32 offset="0xc3008" name="LVDS_PHY_PLL_CTRL_2"/>
	<reg32 offset="0xc300c" name="LVDS_PHY_PLL_CTRL_3"/>
	<reg32 offset="0xc3014" name="LVDS_PHY_PLL_CTRL_5"/>
	<reg32 offset="0xc3018" name="LVDS_PHY_PLL_CTRL_6"/>
	<reg32 offset="0xc301c" name="LVDS_PHY_PLL_CTRL_7"/>
	<reg32 offset="0xc3020" name="LVDS_PHY_PLL_CTRL_8"/>
	<reg32 offset="0xc3024" name="LVDS_PHY_PLL_CTRL_9"/>
	<reg32 offset="0xc3080" name="LVDS_PHY_PLL_LOCKED"/>
	<reg32 offset="0xc3108" name="LVDS_PHY_CFG2"/>

	<reg32 offset="0xc3100" name="LVDS_PHY_CFG0">
		<bitfield name="SERIALIZATION_ENBLE" pos="4" type="boolean"/>
		<bitfield name="CHANNEL0" pos="6" type="boolean"/>
		<bitfield name="CHANNEL1" pos="7" type="boolean"/>
	</reg32>

	<array offset="0xd0000" name="DTV" length="1" stride="0x1000">
		<reg32 offset="0x0000" name="ENABLE"/>
		<reg32 offset="0x0004" name="HSYNC_CTRL" type="mdp4_hsync_ctrl"/>
		<reg32 offset="0x0008" name="VSYNC_PERIOD" type="uint"/>
		<reg32 offset="0x000c" name="VSYNC_LEN" type="uint"/>
		<reg32 offset="0x0018" name="DISPLAY_HCTRL" type="mdp4_display_hctl"/>
		<reg32 offset="0x001c" name="DISPLAY_VSTART" type="uint"/>
		<reg32 offset="0x0020" name="DISPLAY_VEND" type="uint"/>
		<reg32 offset="0x002c" name="ACTIVE_HCTL" type="mdp4_active_hctl"/>
		<reg32 offset="0x0030" name="ACTIVE_VSTART" type="uint"/>
		<reg32 offset="0x0038" name="ACTIVE_VEND" type="uint"/>
		<reg32 offset="0x0040" name="BORDER_CLR"/>
		<reg32 offset="0x0044" name="UNDERFLOW_CLR" type="mdp4_underflow_clr"/>
		<reg32 offset="0x0048" name="HSYNC_SKEW"/>
		<reg32 offset="0x004c" name="TEST_CNTL"/>
		<reg32 offset="0x0050" name="CTRL_POLARITY" type="mdp4_ctrl_polarity"/>
	</array>

	<array offset="0xe0000" name="DSI" length="1" stride="0x1000">
		<reg32 offset="0x0000" name="ENABLE"/>
		<reg32 offset="0x0004" name="HSYNC_CTRL" type="mdp4_hsync_ctrl"/>
		<reg32 offset="0x0008" name="VSYNC_PERIOD" type="uint"/>
		<reg32 offset="0x000c" name="VSYNC_LEN" type="uint"/>
		<reg32 offset="0x0010" name="DISPLAY_HCTRL" type="mdp4_display_hctl"/>
		<reg32 offset="0x0014" name="DISPLAY_VSTART" type="uint"/>
		<reg32 offset="0x0018" name="DISPLAY_VEND" type="uint"/>
		<reg32 offset="0x001c" name="ACTIVE_HCTL" type="mdp4_active_hctl"/>
		<reg32 offset="0x0020" name="ACTIVE_VSTART" type="uint"/>
		<reg32 offset="0x0024" name="ACTIVE_VEND" type="uint"/>
		<reg32 offset="0x0028" name="BORDER_CLR"/>
		<reg32 offset="0x002c" name="UNDERFLOW_CLR" type="mdp4_underflow_clr"/>
		<reg32 offset="0x0030" name="HSYNC_SKEW"/>
		<reg32 offset="0x0034" name="TEST_CNTL"/>
		<reg32 offset="0x0038" name="CTRL_POLARITY" type="mdp4_ctrl_polarity"/>
	</array>
</domain>

</database>
