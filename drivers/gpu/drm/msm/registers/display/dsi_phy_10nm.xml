<?xml version="1.0" encoding="UTF-8"?>
<database xmlns="http://nouveau.freedesktop.org/"
xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
xsi:schemaLocation="https://gitlab.freedesktop.org/freedreno/ rules-fd.xsd">
<import file="freedreno_copyright.xml"/>

<domain name="DSI_10nm_PHY_CMN" width="32">
	<reg32 offset="0x00000" name="REVISION_ID0"/>
	<reg32 offset="0x00004" name="REVISION_ID1"/>
	<reg32 offset="0x00008" name="REVISION_ID2"/>
	<reg32 offset="0x0000c" name="REVISION_ID3"/>
	<reg32 offset="0x00010" name="CLK_CFG0"/>
	<reg32 offset="0x00014" name="CLK_CFG1"/>
	<reg32 offset="0x00018" name="GLBL_CTRL"/>
	<reg32 offset="0x0001c" name="RBUF_CTRL"/>
	<reg32 offset="0x00020" name="VREG_CTRL"/>
	<reg32 offset="0x00024" name="CTRL_0"/>
	<reg32 offset="0x00028" name="CTRL_1"/>
	<reg32 offset="0x0002c" name="CTRL_2"/>
	<reg32 offset="0x00030" name="LANE_CFG0"/>
	<reg32 offset="0x00034" name="LANE_CFG1"/>
	<reg32 offset="0x00038" name="PLL_CNTRL"/>
	<reg32 offset="0x00098" name="LANE_CTRL0"/>
	<reg32 offset="0x0009c" name="LANE_CTRL1"/>
	<reg32 offset="0x000a0" name="LANE_CTRL2"/>
	<reg32 offset="0x000a4" name="LANE_CTRL3"/>
	<reg32 offset="0x000a8" name="LANE_CTRL4"/>
	<reg32 offset="0x000ac" name="TIMING_CTRL_0"/>
	<reg32 offset="0x000b0" name="TIMING_CTRL_1"/>
	<reg32 offset="0x000b4" name="TIMING_CTRL_2"/>
	<reg32 offset="0x000b8" name="TIMING_CTRL_3"/>
	<reg32 offset="0x000bc" name="TIMING_CTRL_4"/>
	<reg32 offset="0x000c0" name="TIMING_CTRL_5"/>
	<reg32 offset="0x000c4" name="TIMING_CTRL_6"/>
	<reg32 offset="0x000c8" name="TIMING_CTRL_7"/>
	<reg32 offset="0x000cc" name="TIMING_CTRL_8"/>
	<reg32 offset="0x000d0" name="TIMING_CTRL_9"/>
	<reg32 offset="0x000d4" name="TIMING_CTRL_10"/>
	<reg32 offset="0x000d8" name="TIMING_CTRL_11"/>
	<reg32 offset="0x000ec" name="PHY_STATUS"/>
	<reg32 offset="0x000f4" name="LANE_STATUS0"/>
	<reg32 offset="0x000f8" name="LANE_STATUS1"/>
</domain>

<domain name="DSI_10nm_PHY" width="32">
	<array offset="0x00000" name="LN" length="5" stride="0x80">
		<reg32 offset="0x00" name="CFG0"/>
		<reg32 offset="0x04" name="CFG1"/>
		<reg32 offset="0x08" name="CFG2"/>
		<reg32 offset="0x0c" name="CFG3"/>
		<reg32 offset="0x10" name="TEST_DATAPATH"/>
		<reg32 offset="0x14" name="PIN_SWAP"/>
		<reg32 offset="0x18" name="HSTX_STR_CTRL"/>
		<reg32 offset="0x1c" name="OFFSET_TOP_CTRL"/>
		<reg32 offset="0x20" name="OFFSET_BOT_CTRL"/>
		<reg32 offset="0x24" name="LPTX_STR_CTRL"/>
		<reg32 offset="0x28" name="LPRX_CTRL"/>
		<reg32 offset="0x2c" name="TX_DCTRL"/>
	</array>
</domain>

<domain name="DSI_10nm_PHY_PLL" width="32">
	<reg32 offset="0x0000" name="ANALOG_CONTROLS_ONE"/>
	<reg32 offset="0x0004" name="ANALOG_CONTROLS_TWO"/>
	<reg32 offset="0x0010" name="ANALOG_CONTROLS_THREE"/>
	<reg32 offset="0x001c" name="DSM_DIVIDER"/>
	<reg32 offset="0x0020" name="FEEDBACK_DIVIDER"/>
	<reg32 offset="0x0024" name="SYSTEM_MUXES"/>
	<reg32 offset="0x002c" name="CMODE"/>
	<reg32 offset="0x0030" name="CALIBRATION_SETTINGS"/>
	<reg32 offset="0x0054" name="BAND_SEL_CAL_SETTINGS_THREE"/>
	<reg32 offset="0x0064" name="FREQ_DETECT_SETTINGS_ONE"/>
	<reg32 offset="0x007c" name="PFILT"/>
	<reg32 offset="0x0080" name="IFILT"/>
	<reg32 offset="0x0094" name="OUTDIV"/>
	<reg32 offset="0x00a4" name="CORE_OVERRIDE"/>
	<reg32 offset="0x00a8" name="CORE_INPUT_OVERRIDE"/>
	<reg32 offset="0x00b4" name="PLL_DIGITAL_TIMERS_TWO"/>
	<reg32 offset="0x00cc" name="DECIMAL_DIV_START_1"/>
	<reg32 offset="0x00d0" name="FRAC_DIV_START_LOW_1"/>
	<reg32 offset="0x00d4" name="FRAC_DIV_START_MID_1"/>
	<reg32 offset="0x00d8" name="FRAC_DIV_START_HIGH_1"/>
	<reg32 offset="0x010c" name="SSC_STEPSIZE_LOW_1"/>
	<reg32 offset="0x0110" name="SSC_STEPSIZE_HIGH_1"/>
	<reg32 offset="0x0114" name="SSC_DIV_PER_LOW_1"/>
	<reg32 offset="0x0118" name="SSC_DIV_PER_HIGH_1"/>
	<reg32 offset="0x011c" name="SSC_DIV_ADJPER_LOW_1"/>
	<reg32 offset="0x0120" name="SSC_DIV_ADJPER_HIGH_1"/>
	<reg32 offset="0x013c" name="SSC_CONTROL"/>
	<reg32 offset="0x0140" name="PLL_OUTDIV_RATE"/>
	<reg32 offset="0x0144" name="PLL_LOCKDET_RATE_1"/>
	<reg32 offset="0x014c" name="PLL_PROP_GAIN_RATE_1"/>
	<reg32 offset="0x0154" name="PLL_BAND_SET_RATE_1"/>
	<reg32 offset="0x015c" name="PLL_INT_GAIN_IFILT_BAND_1"/>
	<reg32 offset="0x0164" name="PLL_FL_INT_GAIN_PFILT_BAND_1"/>
	<reg32 offset="0x0180" name="PLL_LOCK_OVERRIDE"/>
	<reg32 offset="0x0184" name="PLL_LOCK_DELAY"/>
	<reg32 offset="0x018c" name="CLOCK_INVERTERS"/>
	<reg32 offset="0x01a0" name="COMMON_STATUS_ONE"/>
</domain>

</database>
