<?xml version="1.0" encoding="UTF-8"?>
<database xmlns="http://nouveau.freedesktop.org/"
xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
xsi:schemaLocation="https://gitlab.freedesktop.org/freedreno/ rules-fd.xsd">
<import file="freedreno_copyright.xml"/>

<domain name="DSI_7nm_PHY_CMN" width="32">
	<reg32 offset="0x00000" name="REVISION_ID0"/>
	<reg32 offset="0x00004" name="REVISION_ID1"/>
	<reg32 offset="0x00008" name="REVISION_ID2"/>
	<reg32 offset="0x0000c" name="REVISION_ID3"/>
	<reg32 offset="0x00010" name="CLK_CFG0"/>
	<reg32 offset="0x00014" name="CLK_CFG1"/>
	<reg32 offset="0x00018" name="GLBL_CTRL"/>
	<reg32 offset="0x0001c" name="RBUF_CTRL"/>
	<reg32 offset="0x00020" name="VREG_CTRL_0"/>
	<reg32 offset="0x00024" name="CTRL_0"/>
	<reg32 offset="0x00028" name="CTRL_1"/>
	<reg32 offset="0x0002c" name="CTRL_2"/>
	<reg32 offset="0x00030" name="CTRL_3"/>
	<reg32 offset="0x00034" name="LANE_CFG0"/>
	<reg32 offset="0x00038" name="LANE_CFG1"/>
	<reg32 offset="0x0003c" name="PLL_CNTRL"/>
	<reg32 offset="0x00040" name="DPHY_SOT"/>
	<reg32 offset="0x000a0" name="LANE_CTRL0"/>
	<reg32 offset="0x000a4" name="LANE_CTRL1"/>
	<reg32 offset="0x000a8" name="LANE_CTRL2"/>
	<reg32 offset="0x000ac" name="LANE_CTRL3"/>
	<reg32 offset="0x000b0" name="LANE_CTRL4"/>
	<reg32 offset="0x000b4" name="TIMING_CTRL_0"/>
	<reg32 offset="0x000b8" name="TIMING_CTRL_1"/>
	<reg32 offset="0x000bc" name="TIMING_CTRL_2"/>
	<reg32 offset="0x000c0" name="TIMING_CTRL_3"/>
	<reg32 offset="0x000c4" name="TIMING_CTRL_4"/>
	<reg32 offset="0x000c8" name="TIMING_CTRL_5"/>
	<reg32 offset="0x000cc" name="TIMING_CTRL_6"/>
	<reg32 offset="0x000d0" name="TIMING_CTRL_7"/>
	<reg32 offset="0x000d4" name="TIMING_CTRL_8"/>
	<reg32 offset="0x000d8" name="TIMING_CTRL_9"/>
	<reg32 offset="0x000dc" name="TIMING_CTRL_10"/>
	<reg32 offset="0x000e0" name="TIMING_CTRL_11"/>
	<reg32 offset="0x000e4" name="TIMING_CTRL_12"/>
	<reg32 offset="0x000e8" name="TIMING_CTRL_13"/>
	<reg32 offset="0x000ec" name="GLBL_HSTX_STR_CTRL_0"/>
	<reg32 offset="0x000f0" name="GLBL_HSTX_STR_CTRL_1"/>
	<reg32 offset="0x000f4" name="GLBL_RESCODE_OFFSET_TOP_CTRL"/>
	<reg32 offset="0x000f8" name="GLBL_RESCODE_OFFSET_BOT_CTRL"/>
	<reg32 offset="0x000fc" name="GLBL_RESCODE_OFFSET_MID_CTRL"/>
	<reg32 offset="0x00100" name="GLBL_LPTX_STR_CTRL"/>
	<reg32 offset="0x00104" name="GLBL_PEMPH_CTRL_0"/>
	<reg32 offset="0x00108" name="GLBL_PEMPH_CTRL_1"/>
	<reg32 offset="0x0010c" name="GLBL_STR_SWI_CAL_SEL_CTRL"/>
	<reg32 offset="0x00110" name="VREG_CTRL_1"/>
	<reg32 offset="0x00114" name="CTRL_4"/>
	<reg32 offset="0x00128" name="GLBL_DIGTOP_SPARE4"/>
	<reg32 offset="0x00140" name="PHY_STATUS"/>
	<reg32 offset="0x00148" name="LANE_STATUS0"/>
	<reg32 offset="0x0014c" name="LANE_STATUS1"/>
	<reg32 offset="0x001ac" name="GLBL_DIGTOP_SPARE10"/>
</domain>

<domain name="DSI_7nm_PHY" width="32">
	<array offset="0x00000" name="LN" length="5" stride="0x80">
		<reg32 offset="0x00" name="CFG0"/>
		<reg32 offset="0x04" name="CFG1"/>
		<reg32 offset="0x08" name="CFG2"/>
		<reg32 offset="0x0c" name="TEST_DATAPATH"/>
		<reg32 offset="0x10" name="PIN_SWAP"/>
		<reg32 offset="0x14" name="LPRX_CTRL"/>
		<reg32 offset="0x18" name="TX_DCTRL"/>
	</array>
</domain>

<domain name="DSI_7nm_PHY_PLL" width="32">
	<reg32 offset="0x0000" name="ANALOG_CONTROLS_ONE"/>
	<reg32 offset="0x0004" name="ANALOG_CONTROLS_TWO"/>
	<reg32 offset="0x0008" name="INT_LOOP_SETTINGS"/>
	<reg32 offset="0x000c" name="INT_LOOP_SETTINGS_TWO"/>
	<reg32 offset="0x0010" name="ANALOG_CONTROLS_THREE"/>
	<reg32 offset="0x0014" name="ANALOG_CONTROLS_FOUR"/>
	<reg32 offset="0x0018" name="ANALOG_CONTROLS_FIVE"/>
	<reg32 offset="0x001c" name="INT_LOOP_CONTROLS"/>
	<reg32 offset="0x0020" name="DSM_DIVIDER"/>
	<reg32 offset="0x0024" name="FEEDBACK_DIVIDER"/>
	<reg32 offset="0x0028" name="SYSTEM_MUXES"/>
	<reg32 offset="0x002c" name="FREQ_UPDATE_CONTROL_OVERRIDES"/>
	<reg32 offset="0x0030" name="CMODE"/>
	<reg32 offset="0x0034" name="PSM_CTRL"/>
	<reg32 offset="0x0038" name="RSM_CTRL"/>
	<reg32 offset="0x003c" name="VCO_TUNE_MAP"/>
	<reg32 offset="0x0040" name="PLL_CNTRL"/>
	<reg32 offset="0x0044" name="CALIBRATION_SETTINGS"/>
	<reg32 offset="0x0048" name="BAND_SEL_CAL_TIMER_LOW"/>
	<reg32 offset="0x004c" name="BAND_SEL_CAL_TIMER_HIGH"/>
	<reg32 offset="0x0050" name="BAND_SEL_CAL_SETTINGS"/>
	<reg32 offset="0x0054" name="BAND_SEL_MIN"/>
	<reg32 offset="0x0058" name="BAND_SEL_MAX"/>
	<reg32 offset="0x005c" name="BAND_SEL_PFILT"/>
	<reg32 offset="0x0060" name="BAND_SEL_IFILT"/>
	<reg32 offset="0x0064" name="BAND_SEL_CAL_SETTINGS_TWO"/>
	<reg32 offset="0x0068" name="BAND_SEL_CAL_SETTINGS_THREE"/>
	<reg32 offset="0x006c" name="BAND_SEL_CAL_SETTINGS_FOUR"/>
	<reg32 offset="0x0070" name="BAND_SEL_ICODE_HIGH"/>
	<reg32 offset="0x0074" name="BAND_SEL_ICODE_LOW"/>
	<reg32 offset="0x0078" name="FREQ_DETECT_SETTINGS_ONE"/>
	<reg32 offset="0x007c" name="FREQ_DETECT_THRESH"/>
	<reg32 offset="0x0080" name="FREQ_DET_REFCLK_HIGH"/>
	<reg32 offset="0x0084" name="FREQ_DET_REFCLK_LOW"/>
	<reg32 offset="0x0088" name="FREQ_DET_PLLCLK_HIGH"/>
	<reg32 offset="0x008c" name="FREQ_DET_PLLCLK_LOW"/>
	<reg32 offset="0x0090" name="PFILT"/>
	<reg32 offset="0x0094" name="IFILT"/>
	<reg32 offset="0x0098" name="PLL_GAIN"/>
	<reg32 offset="0x009c" name="ICODE_LOW"/>
	<reg32 offset="0x00a0" name="ICODE_HIGH"/>
	<reg32 offset="0x00a4" name="LOCKDET"/>
	<reg32 offset="0x00a8" name="OUTDIV"/>
	<reg32 offset="0x00ac" name="FASTLOCK_CONTROL"/>
	<reg32 offset="0x00b0" name="PASS_OUT_OVERRIDE_ONE"/>
	<reg32 offset="0x00b4" name="PASS_OUT_OVERRIDE_TWO"/>
	<reg32 offset="0x00b8" name="CORE_OVERRIDE"/>
	<reg32 offset="0x00bc" name="CORE_INPUT_OVERRIDE"/>
	<reg32 offset="0x00c0" name="RATE_CHANGE"/>
	<reg32 offset="0x00c4" name="PLL_DIGITAL_TIMERS"/>
	<reg32 offset="0x00c8" name="PLL_DIGITAL_TIMERS_TWO"/>
	<reg32 offset="0x00cc" name="DECIMAL_DIV_START"/>
	<reg32 offset="0x00d0" name="FRAC_DIV_START_LOW"/>
	<reg32 offset="0x00d4" name="FRAC_DIV_START_MID"/>
	<reg32 offset="0x00d8" name="FRAC_DIV_START_HIGH"/>
	<reg32 offset="0x00dc" name="DEC_FRAC_MUXES"/>
	<reg32 offset="0x00e0" name="DECIMAL_DIV_START_1"/>
	<reg32 offset="0x00e4" name="FRAC_DIV_START_LOW_1"/>
	<reg32 offset="0x00e8" name="FRAC_DIV_START_MID_1"/>
	<reg32 offset="0x00ec" name="FRAC_DIV_START_HIGH_1"/>
	<reg32 offset="0x00f0" name="DECIMAL_DIV_START_2"/>
	<reg32 offset="0x00f4" name="FRAC_DIV_START_LOW_2"/>
	<reg32 offset="0x00f8" name="FRAC_DIV_START_MID_2"/>
	<reg32 offset="0x00fc" name="FRAC_DIV_START_HIGH_2"/>
	<reg32 offset="0x0100" name="MASH_CONTROL"/>
	<reg32 offset="0x0104" name="SSC_STEPSIZE_LOW"/>
	<reg32 offset="0x0108" name="SSC_STEPSIZE_HIGH"/>
	<reg32 offset="0x010c" name="SSC_DIV_PER_LOW"/>
	<reg32 offset="0x0110" name="SSC_DIV_PER_HIGH"/>
	<reg32 offset="0x0114" name="SSC_ADJPER_LOW"/>
	<reg32 offset="0x0118" name="SSC_ADJPER_HIGH"/>
	<reg32 offset="0x011c" name="SSC_MUX_CONTROL"/>
	<reg32 offset="0x0120" name="SSC_STEPSIZE_LOW_1"/>
	<reg32 offset="0x0124" name="SSC_STEPSIZE_HIGH_1"/>
	<reg32 offset="0x0128" name="SSC_DIV_PER_LOW_1"/>
	<reg32 offset="0x012c" name="SSC_DIV_PER_HIGH_1"/>
	<reg32 offset="0x0130" name="SSC_ADJPER_LOW_1"/>
	<reg32 offset="0x0134" name="SSC_ADJPER_HIGH_1"/>
	<reg32 offset="0x0138" name="SSC_STEPSIZE_LOW_2"/>
	<reg32 offset="0x013c" name="SSC_STEPSIZE_HIGH_2"/>
	<reg32 offset="0x0140" name="SSC_DIV_PER_LOW_2"/>
	<reg32 offset="0x0144" name="SSC_DIV_PER_HIGH_2"/>
	<reg32 offset="0x0148" name="SSC_ADJPER_LOW_2"/>
	<reg32 offset="0x014c" name="SSC_ADJPER_HIGH_2"/>
	<reg32 offset="0x0150" name="SSC_CONTROL"/>
	<reg32 offset="0x0154" name="PLL_OUTDIV_RATE"/>
	<reg32 offset="0x0158" name="PLL_LOCKDET_RATE_1"/>
	<reg32 offset="0x015c" name="PLL_LOCKDET_RATE_2"/>
	<reg32 offset="0x0160" name="PLL_PROP_GAIN_RATE_1"/>
	<reg32 offset="0x0164" name="PLL_PROP_GAIN_RATE_2"/>
	<reg32 offset="0x0168" name="PLL_BAND_SEL_RATE_1"/>
	<reg32 offset="0x016c" name="PLL_BAND_SEL_RATE_2"/>
	<reg32 offset="0x0170" name="PLL_INT_GAIN_IFILT_BAND_1"/>
	<reg32 offset="0x0174" name="PLL_INT_GAIN_IFILT_BAND_2"/>
	<reg32 offset="0x0178" name="PLL_FL_INT_GAIN_PFILT_BAND_1"/>
	<reg32 offset="0x017c" name="PLL_FL_INT_GAIN_PFILT_BAND_2"/>
	<reg32 offset="0x0180" name="PLL_FASTLOCK_EN_BAND"/>
	<reg32 offset="0x0184" name="FREQ_TUNE_ACCUM_INIT_MID"/>
	<reg32 offset="0x0188" name="FREQ_TUNE_ACCUM_INIT_HIGH"/>
	<reg32 offset="0x018c" name="FREQ_TUNE_ACCUM_INIT_MUX"/>
	<reg32 offset="0x0190" name="PLL_LOCK_OVERRIDE"/>
	<reg32 offset="0x0194" name="PLL_LOCK_DELAY"/>
	<reg32 offset="0x0198" name="PLL_LOCK_MIN_DELAY"/>
	<reg32 offset="0x019c" name="CLOCK_INVERTERS"/>
	<reg32 offset="0x01a0" name="SPARE_AND_JPC_OVERRIDES"/>
	<reg32 offset="0x01a4" name="BIAS_CONTROL_1"/>
	<reg32 offset="0x01a8" name="BIAS_CONTROL_2"/>
	<reg32 offset="0x01ac" name="ALOG_OBSV_BUS_CTRL_1"/>
	<reg32 offset="0x01b0" name="COMMON_STATUS_ONE"/>
	<reg32 offset="0x01b4" name="COMMON_STATUS_TWO"/>
	<reg32 offset="0x01b8" name="BAND_SEL_CAL"/>
	<reg32 offset="0x01bc" name="ICODE_ACCUM_STATUS_LOW"/>
	<reg32 offset="0x01c0" name="ICODE_ACCUM_STATUS_HIGH"/>
	<reg32 offset="0x01c4" name="FD_OUT_LOW"/>
	<reg32 offset="0x01c8" name="FD_OUT_HIGH"/>
	<reg32 offset="0x01cc" name="ALOG_OBSV_BUS_STATUS_1"/>
	<reg32 offset="0x01d0" name="PLL_MISC_CONFIG"/>
	<reg32 offset="0x01d4" name="FLL_CONFIG"/>
	<reg32 offset="0x01d8" name="FLL_FREQ_ACQ_TIME"/>
	<reg32 offset="0x01dc" name="FLL_CODE0"/>
	<reg32 offset="0x01e0" name="FLL_CODE1"/>
	<reg32 offset="0x01e4" name="FLL_GAIN0"/>
	<reg32 offset="0x01e8" name="FLL_GAIN1"/>
	<reg32 offset="0x01ec" name="SW_RESET"/>
	<reg32 offset="0x01f0" name="FAST_PWRUP"/>
	<reg32 offset="0x01f4" name="LOCKTIME0"/>
	<reg32 offset="0x01f8" name="LOCKTIME1"/>
	<reg32 offset="0x01fc" name="DEBUG_BUS_SEL"/>
	<reg32 offset="0x0200" name="DEBUG_BUS0"/>
	<reg32 offset="0x0204" name="DEBUG_BUS1"/>
	<reg32 offset="0x0208" name="DEBUG_BUS2"/>
	<reg32 offset="0x020c" name="DEBUG_BUS3"/>
	<reg32 offset="0x0210" name="ANALOG_FLL_CONTROL_OVERRIDES"/>
	<reg32 offset="0x0214" name="VCO_CONFIG"/>
	<reg32 offset="0x0218" name="VCO_CAL_CODE1_MODE0_STATUS"/>
	<reg32 offset="0x021c" name="VCO_CAL_CODE1_MODE1_STATUS"/>
	<reg32 offset="0x0220" name="RESET_SM_STATUS"/>
	<reg32 offset="0x0224" name="TDC_OFFSET"/>
	<reg32 offset="0x0228" name="PS3_PWRDOWN_CONTROLS"/>
	<reg32 offset="0x022c" name="PS4_PWRDOWN_CONTROLS"/>
	<reg32 offset="0x0230" name="PLL_RST_CONTROLS"/>
	<reg32 offset="0x0234" name="GEAR_BAND_SELECT_CONTROLS"/>
	<reg32 offset="0x0238" name="PSM_CLK_CONTROLS"/>
	<reg32 offset="0x023c" name="SYSTEM_MUXES_2"/>
	<reg32 offset="0x0240" name="VCO_CONFIG_1"/>
	<reg32 offset="0x0244" name="VCO_CONFIG_2"/>
	<reg32 offset="0x0248" name="CLOCK_INVERTERS_1"/>
	<reg32 offset="0x024c" name="CLOCK_INVERTERS_2"/>
	<reg32 offset="0x0250" name="CMODE_1"/>
	<reg32 offset="0x0254" name="CMODE_2"/>
	<reg32 offset="0x0258" name="ANALOG_CONTROLS_FIVE_1"/>
	<reg32 offset="0x025c" name="ANALOG_CONTROLS_FIVE_2"/>
	<reg32 offset="0x0260" name="PERF_OPTIMIZE"/>
</domain>

</database>
