<?xml version="1.0" encoding="UTF-8"?>
<database xmlns="http://nouveau.freedesktop.org/"
xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
xsi:schemaLocation="https://gitlab.freedesktop.org/freedreno/ rules-fd.xsd">
<import file="freedreno_copyright.xml"/>

<domain name="EDP" width="32">
	<enum name="edp_color_depth">
		<value name="EDP_6BIT"  value="0"/>
		<value name="EDP_8BIT"  value="1"/>
		<value name="EDP_10BIT" value="2"/>
		<value name="EDP_12BIT" value="3"/>
		<value name="EDP_16BIT" value="4"/>
	</enum>

	<enum name="edp_component_format">
		<value name="EDP_RGB" value="0"/>
		<value name="EDP_YUV422" value="1"/>
		<value name="EDP_YUV444" value="2"/>
	</enum>

	<reg32 offset="0x0004" name="MAINLINK_CTRL">
		<bitfield name="ENABLE" pos="0" type="boolean"/>
		<bitfield name="RESET"  pos="1" type="boolean"/>
	</reg32>

	<reg32 offset="0x0008" name="STATE_CTRL">
		<bitfield name="TRAIN_PATTERN_1"       pos="0" type="boolean"/>
		<bitfield name="TRAIN_PATTERN_2"       pos="1" type="boolean"/>
		<bitfield name="TRAIN_PATTERN_3"       pos="2" type="boolean"/>
		<bitfield name="SYMBOL_ERR_RATE_MEAS"  pos="3" type="boolean"/>
		<bitfield name="PRBS7"                 pos="4" type="boolean"/>
		<bitfield name="CUSTOM_80_BIT_PATTERN" pos="5" type="boolean"/>
		<bitfield name="SEND_VIDEO"            pos="6" type="boolean"/>
		<bitfield name="PUSH_IDLE"             pos="7" type="boolean"/>
	</reg32>

	<reg32 offset="0x000c" name="CONFIGURATION_CTRL">
		<!-- next two may be swapped? -->
		<bitfield name="SYNC_CLK" pos="0" type="boolean"/>
		<bitfield name="STATIC_MVID" pos="1" type="boolean"/>
		<bitfield name="PROGRESSIVE" pos="2" type="boolean"/>
		<!-- # of lanes minus one: -->
		<bitfield name="LANES" low="4" high="5" type="uint"/>
		<bitfield name="ENHANCED_FRAMING" pos="6" type="boolean"/>
		<!--
		   NOTE: only 6bit and 8bit valid
		 -->
		<bitfield name="COLOR" pos="8" type="edp_color_depth"/>
	</reg32>

	<reg32 offset="0x0014" name="SOFTWARE_MVID" type="uint"/>
	<reg32 offset="0x0018" name="SOFTWARE_NVID" type="uint"/>

	<reg32 offset="0x001c" name="TOTAL_HOR_VER">
		<bitfield name="HORIZ" low="0" high="15" type="uint"/>
		<bitfield name="VERT"  low="16" high="31" type="uint"/>
	</reg32>

	<reg32 offset="0x0020" name="START_HOR_VER_FROM_SYNC">
		<bitfield name="HORIZ" low="0" high="15" type="uint"/>
		<bitfield name="VERT"  low="16" high="31" type="uint"/>
	</reg32>

	<reg32 offset="0x0024" name="HSYNC_VSYNC_WIDTH_POLARITY">
		<bitfield name="HORIZ"  low="0" high="14" type="uint"/>
		<bitfield name="NHSYNC" pos="15" type="boolean"/>
		<bitfield name="VERT"   low="16" high="30" type="uint"/>
		<bitfield name="NVSYNC" pos="31" type="boolean"/>
	</reg32>

	<reg32 offset="0x0028" name="ACTIVE_HOR_VER">
		<bitfield name="HORIZ" low="0" high="15" type="uint"/>
		<bitfield name="VERT"  low="16" high="31" type="uint"/>
	</reg32>

	<reg32 offset="0x002c" name="MISC1_MISC0">
		<!-- MISC0 from DisplayPort v1.2 spec: -->
		<bitfield name="MISC0" low="0" high="7"/>
		<!-- aliased MISC0 bitfields: -->
		<bitfield name="SYNC" pos="0" type="boolean"/>
		<bitfield name="COMPONENT_FORMAT" low="1" high="2" type="edp_component_format"/>
		<!-- CEA (vs VESA) color range: -->
		<bitfield name="CEA" pos="3" type="boolean"/>
		<!-- YCbCr Colorimetry ITU-R BT709-5 (vs ITU-R BT601-5): -->
		<bitfield name="BT709_5" pos="4" type="boolean"/>
		<bitfield name="COLOR" low="5" high="7" type="edp_color_depth"/>

		<!-- MISC1 from DisplayPort v1.2 spec: -->
		<bitfield name="MISC1" low="8" high="15"/>
		<!-- aliased MISC1 bitfields: -->
		<bitfield name="INTERLACED_ODD" pos="8" type="boolean"/>
		<bitfield name="STEREO" low="9" high="10" type="uint"/>
	</reg32>

	<reg32 offset="0x0074" name="PHY_CTRL">
		<bitfield name="SW_RESET_PLL" pos="0" type="boolean"/>
		<bitfield name="SW_RESET" pos="2" type="boolean"/>
	</reg32>
	<reg32 offset="0x0084" name="MAINLINK_READY">
		<bitfield name="TRAIN_PATTERN_1_READY" pos="3" type="boolean"/>
		<bitfield name="TRAIN_PATTERN_2_READY" pos="4" type="boolean"/>
		<bitfield name="TRAIN_PATTERN_3_READY" pos="5" type="boolean"/>
	</reg32>

	<reg32 offset="0x0300" name="AUX_CTRL">
		<bitfield name="ENABLE" pos="0" type="boolean"/>
		<bitfield name="RESET"  pos="1" type="boolean"/>
	</reg32>

	<!-- interrupt registers come in sets of 3 bits, status/ack/en -->
	<reg32 offset="0x0308" name="INTERRUPT_REG_1">
		<bitfield name="HPD"                    pos="0"  type="boolean"/>
		<bitfield name="HPD_ACK"                pos="1"  type="boolean"/>
		<bitfield name="HPD_EN"                 pos="2"  type="boolean"/>
		<bitfield name="AUX_I2C_DONE"           pos="3"  type="boolean"/>
		<bitfield name="AUX_I2C_DONE_ACK"       pos="4"  type="boolean"/>
		<bitfield name="AUX_I2C_DONE_EN"        pos="5"  type="boolean"/>
		<bitfield name="WRONG_ADDR"             pos="6"  type="boolean"/>
		<bitfield name="WRONG_ADDR_ACK"         pos="7"  type="boolean"/>
		<bitfield name="WRONG_ADDR_EN"          pos="8"  type="boolean"/>
		<bitfield name="TIMEOUT"                pos="9"  type="boolean"/>
		<bitfield name="TIMEOUT_ACK"            pos="10" type="boolean"/>
		<bitfield name="TIMEOUT_EN"             pos="11" type="boolean"/>
		<bitfield name="NACK_DEFER"             pos="12" type="boolean"/>
		<bitfield name="NACK_DEFER_ACK"         pos="13" type="boolean"/>
		<bitfield name="NACK_DEFER_EN"          pos="14" type="boolean"/>
		<bitfield name="WRONG_DATA_CNT"         pos="15" type="boolean"/>
		<bitfield name="WRONG_DATA_CNT_ACK"     pos="16" type="boolean"/>
		<bitfield name="WRONG_DATA_CNT_EN"      pos="17" type="boolean"/>
		<bitfield name="I2C_NACK"               pos="18" type="boolean"/>
		<bitfield name="I2C_NACK_ACK"           pos="19" type="boolean"/>
		<bitfield name="I2C_NACK_EN"            pos="20" type="boolean"/>
		<bitfield name="I2C_DEFER"              pos="21" type="boolean"/>
		<bitfield name="I2C_DEFER_ACK"          pos="22" type="boolean"/>
		<bitfield name="I2C_DEFER_EN"           pos="23" type="boolean"/>
		<bitfield name="PLL_UNLOCK"             pos="24" type="boolean"/>
		<bitfield name="PLL_UNLOCK_ACK"         pos="25" type="boolean"/>
		<bitfield name="PLL_UNLOCK_EN"          pos="26" type="boolean"/>
		<bitfield name="AUX_ERROR"              pos="27" type="boolean"/>
		<bitfield name="AUX_ERROR_ACK"          pos="28" type="boolean"/>
		<bitfield name="AUX_ERROR_EN"           pos="29" type="boolean"/>
	</reg32>

	<reg32 offset="0x030c" name="INTERRUPT_REG_2">
		<bitfield name="READY_FOR_VIDEO"        pos="0"  type="boolean"/>
		<bitfield name="READY_FOR_VIDEO_ACK"    pos="1"  type="boolean"/>
		<bitfield name="READY_FOR_VIDEO_EN"     pos="2"  type="boolean"/>
		<bitfield name="IDLE_PATTERNs_SENT"     pos="3"  type="boolean"/>
		<bitfield name="IDLE_PATTERNs_SENT_ACK" pos="4"  type="boolean"/>
		<bitfield name="IDLE_PATTERNs_SENT_EN"  pos="5"  type="boolean"/>
		<bitfield name="FRAME_END"              pos="9"  type="boolean"/>
		<bitfield name="FRAME_END_ACK"          pos="7"  type="boolean"/>
		<bitfield name="FRAME_END_EN"           pos="8"  type="boolean"/>
		<bitfield name="CRC_UPDATED"            pos="9"  type="boolean"/>
		<bitfield name="CRC_UPDATED_ACK"        pos="10" type="boolean"/>
		<bitfield name="CRC_UPDATED_EN"         pos="11" type="boolean"/>
	</reg32>

	<reg32 offset="0x0310" name="INTERRUPT_TRANS_NUM"/>
	<reg32 offset="0x0314" name="AUX_DATA">
		<bitfield name="READ" pos="0" type="boolean"/>
		<bitfield name="DATA" low="8" high="15"/>
		<bitfield name="INDEX" low="16" high="23"/>
		<bitfield name="INDEX_WRITE" pos="31" type="boolean"/>
	</reg32>

	<reg32 offset="0x0318" name="AUX_TRANS_CTRL">
		<bitfield name="I2C" pos="8" type="boolean"/>
		<bitfield name="GO"  pos="9" type="boolean"/>
	</reg32>

	<reg32 offset="0x0324" name="AUX_STATUS"/>
</domain>

<domain name="EDP_PHY" width="32">
	<array offset="0x0400" name="LN" length="4" stride="0x40">
		<reg32 offset="0x04" name="PD_CTL"/>
	</array>
	<reg32 offset="0x0510" name="GLB_VM_CFG0"/>
	<reg32 offset="0x0514" name="GLB_VM_CFG1"/>
	<reg32 offset="0x0518" name="GLB_MISC9"/>
	<reg32 offset="0x0528" name="GLB_CFG"/>
	<reg32 offset="0x052c" name="GLB_PD_CTL"/>
	<reg32 offset="0x0598" name="GLB_PHY_STATUS"/>
</domain>

<domain name="EDP_28nm_PHY_PLL" width="32">
	<reg32 offset="0x00000" name="REFCLK_CFG"/>
	<reg32 offset="0x00004" name="POSTDIV1_CFG"/>
	<reg32 offset="0x00008" name="CHGPUMP_CFG"/>
	<reg32 offset="0x0000C" name="VCOLPF_CFG"/>
	<reg32 offset="0x00010" name="VREG_CFG"/>
	<reg32 offset="0x00014" name="PWRGEN_CFG"/>
	<reg32 offset="0x00018" name="DMUX_CFG"/>
	<reg32 offset="0x0001C" name="AMUX_CFG"/>
	<reg32 offset="0x00020" name="GLB_CFG">
		<bitfield name="PLL_PWRDN_B" pos="0" type="boolean"/>
		<bitfield name="PLL_LDO_PWRDN_B" pos="1" type="boolean"/>
		<bitfield name="PLL_PWRGEN_PWRDN_B" pos="2" type="boolean"/>
		<bitfield name="PLL_ENABLE" pos="3" type="boolean"/>
	</reg32>
	<reg32 offset="0x00024" name="POSTDIV2_CFG"/>
	<reg32 offset="0x00028" name="POSTDIV3_CFG"/>
	<reg32 offset="0x0002C" name="LPFR_CFG"/>
	<reg32 offset="0x00030" name="LPFC1_CFG"/>
	<reg32 offset="0x00034" name="LPFC2_CFG"/>
	<reg32 offset="0x00038" name="SDM_CFG0"/>
	<reg32 offset="0x0003C" name="SDM_CFG1"/>
	<reg32 offset="0x00040" name="SDM_CFG2"/>
	<reg32 offset="0x00044" name="SDM_CFG3"/>
	<reg32 offset="0x00048" name="SDM_CFG4"/>
	<reg32 offset="0x0004C" name="SSC_CFG0"/>
	<reg32 offset="0x00050" name="SSC_CFG1"/>
	<reg32 offset="0x00054" name="SSC_CFG2"/>
	<reg32 offset="0x00058" name="SSC_CFG3"/>
	<reg32 offset="0x0005C" name="LKDET_CFG0"/>
	<reg32 offset="0x00060" name="LKDET_CFG1"/>
	<reg32 offset="0x00064" name="LKDET_CFG2"/>
	<reg32 offset="0x00068" name="TEST_CFG">
		<bitfield name="PLL_SW_RESET" pos="0" type="boolean"/>
	</reg32>
	<reg32 offset="0x0006C" name="CAL_CFG0"/>
	<reg32 offset="0x00070" name="CAL_CFG1"/>
	<reg32 offset="0x00074" name="CAL_CFG2"/>
	<reg32 offset="0x00078" name="CAL_CFG3"/>
	<reg32 offset="0x0007C" name="CAL_CFG4"/>
	<reg32 offset="0x00080" name="CAL_CFG5"/>
	<reg32 offset="0x00084" name="CAL_CFG6"/>
	<reg32 offset="0x00088" name="CAL_CFG7"/>
	<reg32 offset="0x0008C" name="CAL_CFG8"/>
	<reg32 offset="0x00090" name="CAL_CFG9"/>
	<reg32 offset="0x00094" name="CAL_CFG10"/>
	<reg32 offset="0x00098" name="CAL_CFG11"/>
	<reg32 offset="0x0009C" name="EFUSE_CFG"/>
	<reg32 offset="0x000A0" name="DEBUG_BUS_SEL"/>
</domain>

</database>
