<?xml version="1.0" encoding="UTF-8"?>
<database xmlns="http://nouveau.freedesktop.org/"
xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
xsi:schemaLocation="https://gitlab.freedesktop.org/freedreno/ rules-fd.xsd">
<import file="freedreno_copyright.xml"/>

<domain name="DSI_14nm_PHY_CMN" width="32">
	<reg32 offset="0x00000" name="REVISION_ID0"/>
	<reg32 offset="0x00004" name="REVISION_ID1"/>
	<reg32 offset="0x00008" name="REVISION_ID2"/>
	<reg32 offset="0x0000c" name="REVISION_ID3"/>
	<reg32 offset="0x00010" name="CLK_CFG0">
		<bitfield name="DIV_CTRL_3_0" low="4" high="7" type="uint"/>
		<bitfield name="DIV_CTRL_7_4" low="4" high="7" type="uint"/>
	</reg32>
	<reg32 offset="0x00014" name="CLK_CFG1">
		<bitfield name="DSICLK_SEL" pos="0" type="boolean"/>
	</reg32>
	<reg32 offset="0x00018" name="GLBL_TEST_CTRL">
		<bitfield name="BITCLK_HS_SEL" pos="2" type="boolean"/>
	</reg32>
	<reg32 offset="0x0001C" name="CTRL_0"/>
	<reg32 offset="0x00020" name="CTRL_1">
	</reg32>
	<reg32 offset="0x00024" name="HW_TRIGGER"/>
	<reg32 offset="0x00028" name="SW_CFG0"/>
	<reg32 offset="0x0002C" name="SW_CFG1"/>
	<reg32 offset="0x00030" name="SW_CFG2"/>
	<reg32 offset="0x00034" name="HW_CFG0"/>
	<reg32 offset="0x00038" name="HW_CFG1"/>
	<reg32 offset="0x0003C" name="HW_CFG2"/>
	<reg32 offset="0x00040" name="HW_CFG3"/>
	<reg32 offset="0x00044" name="HW_CFG4"/>
	<reg32 offset="0x00048" name="PLL_CNTRL">
		<bitfield name="PLL_START" pos="0" type="boolean"/>
	</reg32>
	<reg32 offset="0x0004C" name="LDO_CNTRL">
		<bitfield name="VREG_CTRL" low="0" high="5" type="uint"/>
	</reg32>
</domain>

<domain name="DSI_14nm_PHY" width="32">
	<array offset="0x00000" name="LN" length="5" stride="0x80">
		<reg32 offset="0x00" name="CFG0">
			<bitfield name="PREPARE_DLY" low="6" high="7" type="uint"/>
		</reg32>
		<reg32 offset="0x04" name="CFG1">
			<bitfield name="HALFBYTECLK_EN" pos="0" type="boolean"/>
		</reg32>
		<reg32 offset="0x08" name="CFG2"/>
		<reg32 offset="0x0c" name="CFG3"/>
		<reg32 offset="0x10" name="TEST_DATAPATH"/>
		<reg32 offset="0x14" name="TEST_STR"/>
		<reg32 offset="0x18" name="TIMING_CTRL_4">
			<bitfield name="HS_EXIT" low="0" high="7" type="uint"/>
		</reg32>
		<reg32 offset="0x1c" name="TIMING_CTRL_5">
			<bitfield name="HS_ZERO" low="0" high="7" type="uint"/>
		</reg32>
		<reg32 offset="0x20" name="TIMING_CTRL_6">
			<bitfield name="HS_PREPARE" low="0" high="7" type="uint"/>
		</reg32>
		<reg32 offset="0x24" name="TIMING_CTRL_7">
			<bitfield name="HS_TRAIL" low="0" high="7" type="uint"/>
		</reg32>
		<reg32 offset="0x28" name="TIMING_CTRL_8">
			<bitfield name="HS_RQST" low="0" high="7" type="uint"/>
		</reg32>
		<reg32 offset="0x2c" name="TIMING_CTRL_9">
			<bitfield name="TA_GO" low="0" high="2" type="uint"/>
			<bitfield name="TA_SURE" low="4" high="6" type="uint"/>
		</reg32>
		<reg32 offset="0x30" name="TIMING_CTRL_10">
			<bitfield name="TA_GET" low="0" high="2" type="uint"/>
		</reg32>
		<reg32 offset="0x34" name="TIMING_CTRL_11">
			<bitfield name="TRIG3_CMD" low="0" high="7" type="uint"/>
		</reg32>
		<reg32 offset="0x38" name="STRENGTH_CTRL_0"/>
		<reg32 offset="0x3c" name="STRENGTH_CTRL_1"/>
		<reg32 offset="0x64" name="VREG_CNTRL"/>
	</array>
</domain>

<domain name="DSI_14nm_PHY_PLL" width="32">
	<reg32 offset="0x000" name="IE_TRIM"/>
	<reg32 offset="0x004" name="IP_TRIM"/>
	<reg32 offset="0x010" name="IPTAT_TRIM"/>
	<reg32 offset="0x01c" name="CLKBUFLR_EN"/>
	<reg32 offset="0x028" name="SYSCLK_EN_RESET"/>
	<reg32 offset="0x02c" name="RESETSM_CNTRL"/>
	<reg32 offset="0x030" name="RESETSM_CNTRL2"/>
	<reg32 offset="0x034" name="RESETSM_CNTRL3"/>
	<reg32 offset="0x038" name="RESETSM_CNTRL4"/>
	<reg32 offset="0x03c" name="RESETSM_CNTRL5"/>
	<reg32 offset="0x040" name="KVCO_DIV_REF1"/>
	<reg32 offset="0x044" name="KVCO_DIV_REF2"/>
	<reg32 offset="0x048" name="KVCO_COUNT1"/>
	<reg32 offset="0x04c" name="KVCO_COUNT2"/>
	<reg32 offset="0x05c" name="VREF_CFG1"/>
	<reg32 offset="0x058" name="KVCO_CODE"/>
	<reg32 offset="0x06c" name="VCO_DIV_REF1"/>
	<reg32 offset="0x070" name="VCO_DIV_REF2"/>
	<reg32 offset="0x074" name="VCO_COUNT1"/>
	<reg32 offset="0x078" name="VCO_COUNT2"/>
	<reg32 offset="0x07c" name="PLLLOCK_CMP1"/>
	<reg32 offset="0x080" name="PLLLOCK_CMP2"/>
	<reg32 offset="0x084" name="PLLLOCK_CMP3"/>
	<reg32 offset="0x088" name="PLLLOCK_CMP_EN"/>
	<reg32 offset="0x08c" name="PLL_VCO_TUNE"/>
	<reg32 offset="0x090" name="DEC_START"/>
	<reg32 offset="0x094" name="SSC_EN_CENTER"/>
	<reg32 offset="0x098" name="SSC_ADJ_PER1"/>
	<reg32 offset="0x09c" name="SSC_ADJ_PER2"/>
	<reg32 offset="0x0a0" name="SSC_PER1"/>
	<reg32 offset="0x0a4" name="SSC_PER2"/>
	<reg32 offset="0x0a8" name="SSC_STEP_SIZE1"/>
	<reg32 offset="0x0ac" name="SSC_STEP_SIZE2"/>
	<reg32 offset="0x0b4" name="DIV_FRAC_START1"/>
	<reg32 offset="0x0b8" name="DIV_FRAC_START2"/>
	<reg32 offset="0x0bc" name="DIV_FRAC_START3"/>
	<reg32 offset="0x0c0" name="TXCLK_EN"/>
	<reg32 offset="0x0c4" name="PLL_CRCTRL"/>
	<reg32 offset="0x0cc" name="RESET_SM_READY_STATUS"/>
	<reg32 offset="0x0e8" name="PLL_MISC1"/>
	<reg32 offset="0x0f0" name="CP_SET_CUR"/>
	<reg32 offset="0x0f4" name="PLL_ICPMSET"/>
	<reg32 offset="0x0f8" name="PLL_ICPCSET"/>
	<reg32 offset="0x0fc" name="PLL_ICP_SET"/>
	<reg32 offset="0x100" name="PLL_LPF1"/>
	<reg32 offset="0x104" name="PLL_LPF2_POSTDIV"/>
	<reg32 offset="0x108" name="PLL_BANDGAP"/>
</domain>

</database>
