<?xml version="1.0" encoding="UTF-8"?>
<database xmlns="http://nouveau.freedesktop.org/"
xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
xsi:schemaLocation="https://gitlab.freedesktop.org/freedreno/ rules-fd.xsd">
<import file="freedreno_copyright.xml"/>

<!--
	NOTE: also see mdss_hdmi_util.h.. newer devices using MDSS appear
	to have the same HDMI block (or maybe a newer version?) but for
	some reason duplicate the code under drivers/video/msm/mdss
 -->

<domain name="HDMI" width="32">
	<enum name="hdmi_hdcp_key_state">
		<value name="HDCP_KEYS_STATE_NO_KEYS" value="0"/>
		<value name="HDCP_KEYS_STATE_NOT_CHECKED" value="1"/>
		<value name="HDCP_KEYS_STATE_CHECKING" value="2"/>
		<value name="HDCP_KEYS_STATE_VALID" value="3"/>
		<value name="HDCP_KEYS_STATE_AKSV_NOT_VALID" value="4"/>
		<value name="HDCP_KEYS_STATE_CHKSUM_MISMATCH" value="5"/>
		<value name="HDCP_KEYS_STATE_PROD_AKSV" value="6"/>
		<value name="HDCP_KEYS_STATE_RESERVED" value="7"/>
	</enum>
	<enum name="hdmi_ddc_read_write">
		<value name="DDC_WRITE" value="0"/>
		<value name="DDC_READ" value="1"/>
	</enum>
	<enum name="hdmi_acr_cts">
		<value name="ACR_NONE" value="0"/>
		<value name="ACR_32" value="1"/>
		<value name="ACR_44" value="2"/>
		<value name="ACR_48" value="3"/>
	</enum>

	<enum name="hdmi_cec_tx_status">
		<value name="CEC_TX_OK" value="0"/>
		<value name="CEC_TX_NACK" value="1"/>
		<value name="CEC_TX_ARB_LOSS" value="2"/>
		<value name="CEC_TX_MAX_RETRIES" value="3"/>
	</enum>

	<reg32 offset="0x00000" name="CTRL">
		<bitfield name="ENABLE" pos="0" type="boolean"/>
		<bitfield name="HDMI" pos="1" type="boolean"/>
		<bitfield name="ENCRYPTED" pos="2" type="boolean"/>
	</reg32>
	<reg32 offset="0x00020" name="AUDIO_PKT_CTRL1">
		<bitfield name="AUDIO_SAMPLE_SEND" pos="0" type="boolean"/>
	</reg32>
	<reg32 offset="0x00024" name="ACR_PKT_CTRL">
		<!--
			Guessing on order of bitfields from these comments:
				/* AUDIO_PRIORITY | SOURCE */
				acr_pck_ctrl_reg |= 0x80000100;
				/* N_MULTIPLE(multiplier) */
				acr_pck_ctrl_reg |= (multiplier & 7) << 16;
				/* SEND | CONT */
				acr_pck_ctrl_reg |= 0x00000003;
		 -->
		<bitfield name="CONT" pos="0" type="boolean"/>
		<bitfield name="SEND" pos="1" type="boolean"/>
		<bitfield name="SELECT" low="4" high="5" type="hdmi_acr_cts"/>
		<bitfield name="SOURCE" pos="8" type="boolean"/>
		<bitfield name="N_MULTIPLIER" low="16" high="18" type="uint"/>
		<bitfield name="AUDIO_PRIORITY" pos="31" type="boolean"/>
	</reg32>
	<reg32 offset="0x0028" name="VBI_PKT_CTRL">
		<!--
			Guessing on the order of bits from:
				/* GC packet enable (every frame) */
				/* HDMI_VBI_PKT_CTRL[0x0028] */
				hdmi_msm_rmw32or(0x0028, 3 << 4);
				/* HDMI_VBI_PKT_CTRL[0x0028] */
				/* ISRC Send + Continuous */
				hdmi_msm_rmw32or(0x0028, 3 << 8);
				/* HDMI_VBI_PKT_CTRL[0x0028] */
				/* ACP send, s/w source */
				hdmi_msm_rmw32or(0x0028, 3 << 12);
		 -->
		<bitfield name="GC_ENABLE" pos="4" type="boolean"/>
		<bitfield name="GC_EVERY_FRAME" pos="5" type="boolean"/>
		<bitfield name="ISRC_SEND" pos="8" type="boolean"/>
		<bitfield name="ISRC_CONTINUOUS" pos="9" type="boolean"/>
		<bitfield name="ACP_SEND" pos="12" type="boolean"/>
		<bitfield name="ACP_SRC_SW" pos="13" type="boolean"/>
	</reg32>
	<reg32 offset="0x0002c" name="INFOFRAME_CTRL0">
		<!--
			Guessing on the order of these flags, from this comment:
				/* Set these flags */
				/* AUDIO_INFO_UPDATE | AUDIO_INFO_SOURCE | AUDIO_INFO_CONT
				 | AUDIO_INFO_SEND */
				audio_info_ctrl_reg |= 0x000000F0;
				/* 0x3 for AVI InfFrame enable (every frame) */
				HDMI_OUTP(0x002C, HDMI_INP(0x002C) | 0x00000003L);
		 -->
		<bitfield name="AVI_SEND" pos="0" type="boolean"/>
		<bitfield name="AVI_CONT" pos="1" type="boolean"/>           <!-- every frame -->
		<bitfield name="AUDIO_INFO_SEND" pos="4" type="boolean"/>
		<bitfield name="AUDIO_INFO_CONT" pos="5" type="boolean"/>    <!-- every frame -->
		<bitfield name="AUDIO_INFO_SOURCE" pos="6" type="boolean"/>
		<bitfield name="AUDIO_INFO_UPDATE" pos="7" type="boolean"/>
	</reg32>
	<reg32 offset="0x00030" name="INFOFRAME_CTRL1">
		<bitfield name="AVI_INFO_LINE" low="0" high="5" type="uint"/>
		<bitfield name="AUDIO_INFO_LINE" low="8" high="13" type="uint"/>
		<bitfield name="MPEG_INFO_LINE" low="16" high="21" type="uint"/>
		<bitfield name="VENSPEC_INFO_LINE" low="24" high="29" type="uint"/>
	</reg32>
	<reg32 offset="0x00034" name="GEN_PKT_CTRL">
		<!--
			0x0034 GEN_PKT_CTRL
			  GENERIC0_SEND   0      0 = Disable Generic0 Packet Transmission
			                         1 = Enable Generic0 Packet Transmission
			  GENERIC0_CONT   1      0 = Send Generic0 Packet on next frame only
			                         1 = Send Generic0 Packet on every frame
			  GENERIC0_UPDATE 2      NUM
			  GENERIC1_SEND   4      0 = Disable Generic1 Packet Transmission
			                         1 = Enable Generic1 Packet Transmission
			  GENERIC1_CONT   5      0 = Send Generic1 Packet on next frame only
			                         1 = Send Generic1 Packet on every frame
			  GENERIC0_LINE   21:16  NUM
			  GENERIC1_LINE   29:24  NUM
			
			GENERIC0_LINE | GENERIC0_UPDATE | GENERIC0_CONT | GENERIC0_SEND
			Setup HDMI TX generic packet control
			Enable this packet to transmit every frame
			Enable this packet to transmit every frame
			Enable HDMI TX engine to transmit Generic packet 0
			  HDMI_OUTP(0x0034, (1 << 16) | (1 << 2) | BIT(1) | BIT(0));
		 -->
		<bitfield name="GENERIC0_SEND" pos="0" type="boolean"/>
		<bitfield name="GENERIC0_CONT" pos="1" type="boolean"/>
		<bitfield name="GENERIC0_UPDATE" low="2" high="3" type="uint"/> <!-- ??? -->
		<bitfield name="GENERIC1_SEND" pos="4" type="boolean"/>
		<bitfield name="GENERIC1_CONT" pos="5" type="boolean"/>
		<bitfield name="GENERIC0_LINE" low="16" high="21" type="uint"/>
		<bitfield name="GENERIC1_LINE" low="24" high="29" type="uint"/>
	</reg32>
	<reg32 offset="0x00040" name="GC">
		<bitfield name="MUTE" pos="0" type="boolean"/>
	</reg32>
	<reg32 offset="0x00044" name="AUDIO_PKT_CTRL2">
		<bitfield name="OVERRIDE" pos="0" type="boolean"/>
		<bitfield name="LAYOUT" pos="1" type="boolean"/> <!-- 1 for >2 channels -->
	</reg32>

	<!--
		AVI_INFO appears to be the infoframe in a slightly weird order..
		starts with PB0 (checksum), and ends with version..
	-->
	<reg32 offset="0x0006c" name="AVI_INFO" stride="4" length="4"/>

	<reg32 offset="0x00084" name="GENERIC0_HDR"/>
	<reg32 offset="0x00088" name="GENERIC0" stride="4" length="7"/>

	<reg32 offset="0x000a4" name="GENERIC1_HDR"/>
	<reg32 offset="0x000a8" name="GENERIC1" stride="4" length="7"/>

	<!--
		TODO add a way to show symbolic offsets into array: hdmi_acr_cts-1
	 -->
	<array offset="0x00c4" name="ACR" length="3" stride="8" index="hdmi_acr_cts">
		<reg32 offset="0" name="0">
			<bitfield name="CTS" low="12" high="31" type="uint"/>
		</reg32>
		<reg32 offset="4" name="1">
			<!-- not sure the actual # of bits.. -->
			<bitfield name="N" low="0" high="31" type="uint"/>
		</reg32>
	</array>

	<reg32 offset="0x000e4" name="AUDIO_INFO0">
		<bitfield name="CHECKSUM" low="0" high="7"/>
		<bitfield name="CC" low="8" high="10" type="uint"/> <!-- channel count -->
	</reg32>
	<reg32 offset="0x000e8" name="AUDIO_INFO1">
		<bitfield name="CA" low="0" high="7"/>        <!-- Channel Allocation -->
		<bitfield name="LSV" low="11" high="14"/>     <!-- Level Shift -->
		<bitfield name="DM_INH" pos="15" type="boolean"/>  <!-- down-mix inhibit flag -->
	</reg32>
	<reg32 offset="0x00110" name="HDCP_CTRL">
		<bitfield name="ENABLE" pos="0" type="boolean"/>
		<bitfield name="ENCRYPTION_ENABLE" pos="8" type="boolean"/>
	</reg32>
	<reg32 offset="0x00114" name="HDCP_DEBUG_CTRL">
		<bitfield name="RNG_CIPHER" pos="2" type="boolean"/>
	</reg32>
	<reg32 offset="0x00118" name="HDCP_INT_CTRL">
		<bitfield name="AUTH_SUCCESS_INT" pos="0" type="boolean"/>
		<bitfield name="AUTH_SUCCESS_ACK" pos="1" type="boolean"/>
		<bitfield name="AUTH_SUCCESS_MASK" pos="2" type="boolean"/>
		<bitfield name="AUTH_FAIL_INT" pos="4" type="boolean"/>
		<bitfield name="AUTH_FAIL_ACK" pos="5" type="boolean"/>
		<bitfield name="AUTH_FAIL_MASK" pos="6" type="boolean"/>
		<bitfield name="AUTH_FAIL_INFO_ACK" pos="7" type="boolean"/>
		<bitfield name="AUTH_XFER_REQ_INT" pos="8" type="boolean"/>
		<bitfield name="AUTH_XFER_REQ_ACK" pos="9" type="boolean"/>
		<bitfield name="AUTH_XFER_REQ_MASK" pos="10" type="boolean"/>
		<bitfield name="AUTH_XFER_DONE_INT" pos="12" type="boolean"/>
		<bitfield name="AUTH_XFER_DONE_ACK" pos="13" type="boolean"/>
		<bitfield name="AUTH_XFER_DONE_MASK" pos="14" type="boolean"/>
	</reg32>
	<reg32 offset="0x0011c" name="HDCP_LINK0_STATUS">
		<bitfield name="AN_0_READY" pos="8" type="boolean"/>
		<bitfield name="AN_1_READY" pos="9" type="boolean"/>
		<bitfield name="RI_MATCHES" pos="12" type="boolean"/>
		<bitfield name="V_MATCHES" pos="20" type="boolean"/>
		<bitfield name="KEY_STATE" low="28" high="30" type="hdmi_hdcp_key_state"/>
	</reg32>
	<reg32 offset="0x00120" name="HDCP_DDC_CTRL_0">
		<bitfield name="DISABLE" pos="0" type="boolean"/>
	</reg32>
	<reg32 offset="0x00124" name="HDCP_DDC_CTRL_1">
		<bitfield name="FAILED_ACK" pos="0" type="boolean"/>
	</reg32>
	<reg32 offset="0x00128" name="HDCP_DDC_STATUS">
		<bitfield name="XFER_REQ" pos="4" type="boolean"/>
		<bitfield name="XFER_DONE" pos="10" type="boolean"/>
		<bitfield name="ABORTED" pos="12" type="boolean"/>
		<bitfield name="TIMEOUT" pos="13" type="boolean"/>
		<bitfield name="NACK0" pos="14" type="boolean"/>
		<bitfield name="NACK1" pos="15" type="boolean"/>
		<bitfield name="FAILED" pos="16" type="boolean"/>
	</reg32>

	<reg32 offset="0x0012c" name="HDCP_ENTROPY_CTRL0"/>
	<reg32 offset="0x0025c" name="HDCP_ENTROPY_CTRL1"/>

	<reg32 offset="0x00130" name="HDCP_RESET">
		<bitfield name="LINK0_DEAUTHENTICATE" pos="0" type="boolean"/>
	</reg32>

	<reg32 offset="0x00134" name="HDCP_RCVPORT_DATA0"/>
	<reg32 offset="0x00138" name="HDCP_RCVPORT_DATA1"/>
	<reg32 offset="0x0013C" name="HDCP_RCVPORT_DATA2_0"/>
	<reg32 offset="0x00140" name="HDCP_RCVPORT_DATA2_1"/>
	<reg32 offset="0x00144" name="HDCP_RCVPORT_DATA3"/>
	<reg32 offset="0x00148" name="HDCP_RCVPORT_DATA4"/>
	<reg32 offset="0x0014c" name="HDCP_RCVPORT_DATA5"/>
	<reg32 offset="0x00150" name="HDCP_RCVPORT_DATA6"/>
	<reg32 offset="0x00154" name="HDCP_RCVPORT_DATA7"/>
	<reg32 offset="0x00158" name="HDCP_RCVPORT_DATA8"/>
	<reg32 offset="0x0015c" name="HDCP_RCVPORT_DATA9"/>
	<reg32 offset="0x00160" name="HDCP_RCVPORT_DATA10"/>
	<reg32 offset="0x00164" name="HDCP_RCVPORT_DATA11"/>
	<reg32 offset="0x00168" name="HDCP_RCVPORT_DATA12"/>

	<reg32 offset="0x0016c" name="VENSPEC_INFO0"/>
	<reg32 offset="0x00170" name="VENSPEC_INFO1"/>
	<reg32 offset="0x00174" name="VENSPEC_INFO2"/>
	<reg32 offset="0x00178" name="VENSPEC_INFO3"/>
	<reg32 offset="0x0017c" name="VENSPEC_INFO4"/>
	<reg32 offset="0x00180" name="VENSPEC_INFO5"/>
	<reg32 offset="0x00184" name="VENSPEC_INFO6"/>

	<reg32 offset="0x001d0" name="AUDIO_CFG">
		<bitfield name="ENGINE_ENABLE" pos="0" type="boolean"/>
		<bitfield name="FIFO_WATERMARK" low="4" high="7" type="uint"/>
	</reg32>

	<reg32 offset="0x00208" name="USEC_REFTIMER"/>
	<reg32 offset="0x0020c" name="DDC_CTRL">
		<!--
			 0x020C HDMI_DDC_CTRL
			[21:20] TRANSACTION_CNT
				Number of transactions to be done in current transfer.
				* 0x0: transaction0 only
				* 0x1: transaction0, transaction1
				* 0x2: transaction0, transaction1, transaction2
				* 0x3: transaction0, transaction1, transaction2, transaction3
			[3] SW_STATUS_RESET
				Write 1 to reset HDMI_DDC_SW_STATUS flags, will reset SW_DONE,
				ABORTED, TIMEOUT, SW_INTERRUPTED, BUFFER_OVERFLOW,
				STOPPED_ON_NACK, NACK0, NACK1, NACK2, NACK3
			[2] SEND_RESET Set to 1 to send reset sequence (9 clocks with no
				data) at start of transfer.  This sequence is sent after GO is
				written to 1, before the first transaction only.
			[1] SOFT_RESET Write 1 to reset DDC controller
			[0] GO WRITE ONLY. Write 1 to start DDC transfer.
		 -->
		<bitfield name="GO" pos="0" type="boolean"/>
		<bitfield name="SOFT_RESET" pos="1" type="boolean"/>
		<bitfield name="SEND_RESET" pos="2" type="boolean"/>
		<bitfield name="SW_STATUS_RESET" pos="3" type="boolean"/>
		<bitfield name="TRANSACTION_CNT" low="20" high="21" type="uint"/>
	</reg32>
	<reg32 offset="0x00210" name="DDC_ARBITRATION">
		<bitfield name="HW_ARBITRATION" pos="4" type="boolean"/>
	</reg32>
	<reg32 offset="0x00214" name="DDC_INT_CTRL">
		<!--
			HDMI_DDC_INT_CTRL[0x0214]
			   [2] SW_DONE_MK Mask bit for SW_DONE_INT. Set to 1 to enable
			       interrupt.
			   [1] SW_DONE_ACK WRITE ONLY. Acknowledge bit for SW_DONE_INT.
			       Write 1 to clear interrupt.
			   [0] SW_DONE_INT READ ONLY. SW_DONE interrupt status */
		 -->
		<bitfield name="SW_DONE_INT" pos="0" type="boolean"/>
		<bitfield name="SW_DONE_ACK" pos="1" type="boolean"/>
		<bitfield name="SW_DONE_MASK" pos="2" type="boolean"/>
	</reg32>
	<reg32 offset="0x00218" name="DDC_SW_STATUS">
		<bitfield name="NACK0" pos="12" type="boolean"/>
		<bitfield name="NACK1" pos="13" type="boolean"/>
		<bitfield name="NACK2" pos="14" type="boolean"/>
		<bitfield name="NACK3" pos="15" type="boolean"/>
	</reg32>
	<reg32 offset="0x0021c" name="DDC_HW_STATUS">
		<bitfield name="DONE" pos="3" type="boolean"/>
	</reg32>
	<reg32 offset="0x00220" name="DDC_SPEED">
		<!--
		   0x0220 HDMI_DDC_SPEED
		   [31:16] PRESCALE prescale = (m * xtal_frequency) /
			(desired_i2c_speed), where m is multiply
			factor, default: m = 1
		   [1:0]   THRESHOLD Select threshold to use to determine whether value
			sampled on SDA is a 1 or 0. Specified in terms of the ratio
			between the number of sampled ones and the total number of times
			SDA is sampled.
			* 0x0: >0
			* 0x1: 1/4 of total samples
			* 0x2: 1/2 of total samples
			* 0x3: 3/4 of total samples */
		 -->
		<bitfield name="THRESHOLD" low="0" high="1" type="uint"/>
		<bitfield name="PRESCALE" low="16" high="31" type="uint"/>
	</reg32>
	<reg32 offset="0x00224" name="DDC_SETUP">
		<!--
			 * 0x0224 HDMI_DDC_SETUP
			 * Setting 31:24 bits : Time units to wait before timeout
			 * when clock is being stalled by external sink device
		 -->
		<bitfield name="TIMEOUT" low="24" high="31" type="uint"/>
	</reg32>
	<!-- Guessing length is 4, as elsewhere the are references to trans0 thru trans3 -->
	<array offset="0x00228" name="I2C_TRANSACTION" length="4" stride="4">
		<reg32 offset="0" name="REG">
			<!--
				0x0228 HDMI_DDC_TRANS0
				[23:16] CNT0 Byte count for first transaction (excluding the first
					byte, which is usually the address).
				[13] STOP0 Determines whether a stop bit will be sent after the first
					transaction
					* 0: NO STOP
					* 1: STOP
				[12] START0 Determines whether a start bit will be sent before the
					first transaction
					* 0: NO START
					* 1: START
				[8] STOP_ON_NACK0 Determines whether the current transfer will stop
					if a NACK is received during the first transaction (current
					transaction always stops).
					* 0: STOP CURRENT TRANSACTION, GO TO NEXT TRANSACTION
					* 1: STOP ALL TRANSACTIONS, SEND STOP BIT
				[0] RW0 Read/write indicator for first transaction - set to 0 for
					write, 1 for read. This bit only controls HDMI_DDC behaviour -
					the R/W bit in the transaction is programmed into the DDC buffer
					as the LSB of the address byte.
					* 0: WRITE
					* 1: READ
			 -->
			<bitfield name="RW" pos="0" type="hdmi_ddc_read_write"/>
			<bitfield name="STOP_ON_NACK" pos="8" type="boolean"/>
			<bitfield name="START" pos="12" type="boolean"/>
			<bitfield name="STOP" pos="13" type="boolean"/>
			<bitfield name="CNT" low="16" high="23" type="uint"/>
		</reg32>
	</array>
	<reg32 offset="0x00238" name="DDC_DATA">
		<!--
			0x0238 HDMI_DDC_DATA
			[31] INDEX_WRITE WRITE ONLY. To write index field, set this bit to
				1 while writing HDMI_DDC_DATA.
			[23:16] INDEX Use to set index into DDC buffer for next read or
				current write, or to read index of current read or next write.
				Writable only when INDEX_WRITE=1.
			[15:8] DATA Use to fill or read the DDC buffer
			[0] DATA_RW Select whether buffer access will be a read or write.
				For writes, address auto-increments on write to HDMI_DDC_DATA.
				For reads, address autoincrements on reads to HDMI_DDC_DATA.
				* 0: Write
				* 1: Read
		 -->
		<bitfield name="DATA_RW" pos="0" type="hdmi_ddc_read_write"/>
		<bitfield name="DATA" low="8" high="15" type="uint"/>
		<bitfield name="INDEX" low="16" high="23" type="uint"/>
		<bitfield name="INDEX_WRITE" pos="31" type="boolean"/>
	</reg32>

	<reg32 offset="0x0023c" name="HDCP_SHA_CTRL"/>
	<reg32 offset="0x00240" name="HDCP_SHA_STATUS">
		<bitfield name="BLOCK_DONE" pos="0" type="boolean"/>
		<bitfield name="COMP_DONE" pos="4" type="boolean"/>
	</reg32>
	<reg32 offset="0x00244" name="HDCP_SHA_DATA">
		<bitfield name="DONE" pos="0" type="boolean"/>
	</reg32>

	<reg32 offset="0x00250" name="HPD_INT_STATUS">
		<bitfield name="INT" pos="0" type="boolean"/>  <!-- an irq has occurred -->
		<bitfield name="CABLE_DETECTED" pos="1" type="boolean"/>
	</reg32>
	<reg32 offset="0x00254" name="HPD_INT_CTRL">
		<!-- (this useful comment was removed in df6b645.. git archaeology is fun)
			HPD_INT_CTRL[0x0254]
			31:10 Reserved
			9     RCV_PLUGIN_DET_MASK  receiver plug in interrupt mask.
			                           When programmed to 1,
			                           RCV_PLUGIN_DET_INT will toggle
			                           the interrupt line
			8:6   Reserved
			5     RX_INT_EN            Panel RX interrupt enable
			      0: Disable
			      1: Enable
			4     RX_INT_ACK           WRITE ONLY. Panel RX interrupt
			                           ack
			3     Reserved
			2     INT_EN               Panel interrupt control
			      0: Disable
			      1: Enable
			1     INT_POLARITY         Panel interrupt polarity
			      0: generate interrupt on disconnect
			      1: generate interrupt on connect
			0     INT_ACK              WRITE ONLY. Panel interrupt ack
		 -->
		<bitfield name="INT_ACK" pos="0" type="boolean"/>
		<bitfield name="INT_CONNECT" pos="1" type="boolean"/>
		<bitfield name="INT_EN" pos="2" type="boolean"/>
		<bitfield name="RX_INT_ACK" pos="4" type="boolean"/>
		<bitfield name="RX_INT_EN" pos="5" type="boolean"/>
		<bitfield name="RCV_PLUGIN_DET_MASK" pos="9" type="boolean"/>
	</reg32>
	<reg32 offset="0x00258" name="HPD_CTRL">
		<bitfield name="TIMEOUT" low="0" high="12" type="uint"/>
		<bitfield name="ENABLE" pos="28" type="boolean"/>
	</reg32>
	<reg32 offset="0x0027c" name="DDC_REF">
		<!--
			0x027C HDMI_DDC_REF
			[16] REFTIMER_ENABLE	Enable the timer
				* 0: Disable
				* 1: Enable
			[15:0] REFTIMER	Value to set the register in order to generate
				DDC strobe. This register counts on HDCP application clock

			/* Enable reference timer
			 * 27 micro-seconds */
			HDMI_OUTP_ND(0x027C, (1 << 16) | (27 << 0));
		 -->
		<bitfield name="REFTIMER_ENABLE" pos="16" type="boolean"/>
		<bitfield name="REFTIMER" low="0" high="15" type="uint"/>
	</reg32>

	<reg32 offset="0x00284" name="HDCP_SW_UPPER_AKSV"/>
	<reg32 offset="0x00288" name="HDCP_SW_LOWER_AKSV"/>

	<reg32 offset="0x0028c" name="CEC_CTRL">
		<bitfield name="ENABLE" pos="0" type="boolean"/>
		<bitfield name="SEND_TRIGGER" pos="1" type="boolean"/>
		<bitfield name="FRAME_SIZE" low="4" high="8" type="uint"/>
		<bitfield name="LINE_OE" pos="9" type="boolean"/>
	</reg32>
	<reg32 offset="0x00290" name="CEC_WR_DATA">
		<bitfield name="BROADCAST" pos="0" type="boolean"/>
		<bitfield name="DATA" low="8" high="15" type="uint"/>
	</reg32>
	<reg32 offset="0x00294" name="CEC_RETRANSMIT">
		<bitfield name="ENABLE" pos="0" type="boolean"/>
		<bitfield name="COUNT" low="1" high="7" type="uint"/>
	</reg32>
	<reg32 offset="0x00298" name="CEC_STATUS">
		<bitfield name="BUSY" pos="0" type="boolean"/>
		<bitfield name="TX_FRAME_DONE" pos="3" type="boolean"/>
		<bitfield name="TX_STATUS" low="4" high="7" type="hdmi_cec_tx_status"/>
	</reg32>
	<reg32 offset="0x0029c" name="CEC_INT">
		<bitfield name="TX_DONE" pos="0" type="boolean"/>
		<bitfield name="TX_DONE_MASK" pos="1" type="boolean"/>
		<bitfield name="TX_ERROR" pos="2" type="boolean"/>
		<bitfield name="TX_ERROR_MASK" pos="3" type="boolean"/>
		<bitfield name="MONITOR" pos="4" type="boolean"/>
		<bitfield name="MONITOR_MASK" pos="5" type="boolean"/>
		<bitfield name="RX_DONE" pos="6" type="boolean"/>
		<bitfield name="RX_DONE_MASK" pos="7" type="boolean"/>
	</reg32>
	<reg32 offset="0x002a0" name="CEC_ADDR"/>
	<reg32 offset="0x002a4" name="CEC_TIME">
		<bitfield name="ENABLE" pos="0" type="boolean"/>
		<bitfield name="SIGNAL_FREE_TIME" low="7" high="15" type="uint"/>
	</reg32>
	<reg32 offset="0x002a8" name="CEC_REFTIMER">
		<bitfield name="REFTIMER" low="0" high="15" type="uint"/>
		<bitfield name="ENABLE" pos="16" type="boolean"/>
	</reg32>
	<reg32 offset="0x002ac" name="CEC_RD_DATA">
		<bitfield name="DATA" low="0" high="7" type="uint"/>
		<bitfield name="SIZE" low="8" high="12" type="uint"/>
	</reg32>
	<reg32 offset="0x002b0" name="CEC_RD_FILTER"/>

	<reg32 offset="0x002b4" name="ACTIVE_HSYNC">
		<bitfield name="START" low="0" high="12" type="uint"/>
		<bitfield name="END" low="16" high="27" type="uint"/>
	</reg32>
	<reg32 offset="0x002b8" name="ACTIVE_VSYNC">
		<bitfield name="START" low="0" high="12" type="uint"/>
		<bitfield name="END" low="16" high="28" type="uint"/>
	</reg32>
	<reg32 offset="0x002bc" name="VSYNC_ACTIVE_F2">
		<!-- interlaced, frame 2 -->
		<bitfield name="START" low="0" high="12" type="uint"/>
		<bitfield name="END" low="16" high="28" type="uint"/>
	</reg32>
	<reg32 offset="0x002c0" name="TOTAL">
		<bitfield name="H_TOTAL" low="0" high="12" type="uint"/>
		<bitfield name="V_TOTAL" low="16" high="28" type="uint"/>
	</reg32>
	<reg32 offset="0x002c4" name="VSYNC_TOTAL_F2">
		<!-- interlaced, frame 2 -->
		<bitfield name="V_TOTAL" low="0" high="12" type="uint"/>
	</reg32>
	<reg32 offset="0x002c8" name="FRAME_CTRL">
		<bitfield name="RGB_MUX_SEL_BGR" pos="12" type="boolean"/>
		<bitfield name="VSYNC_LOW" pos="28" type="boolean"/>
		<bitfield name="HSYNC_LOW" pos="29" type="boolean"/>
		<bitfield name="INTERLACED_EN" pos="31" type="boolean"/>
	</reg32>
	<reg32 offset="0x002cc" name="AUD_INT">
		<!--
			HDMI_AUD_INT[0x02CC]
			[3] AUD_SAM_DROP_MASK [R/W]
			[2] AUD_SAM_DROP_ACK [W], AUD_SAM_DROP_INT [R]
			[1] AUD_FIFO_URUN_MASK [R/W]
			[0] AUD_FIFO_URUN_ACK [W], AUD_FIFO_URUN_INT [R]
		 -->
		<bitfield name="AUD_FIFO_URUN_INT" pos="0" type="boolean"/>  <!-- write to ack irq -->
		<bitfield name="AUD_FIFO_URAN_MASK" pos="1" type="boolean"/> <!-- r/w, enables irq -->
		<bitfield name="AUD_SAM_DROP_INT" pos="2" type="boolean"/>   <!-- write to ack irq -->
		<bitfield name="AUD_SAM_DROP_MASK" pos="3" type="boolean"/>  <!-- r/w, enables irq -->
	</reg32>
	<reg32 offset="0x002d4" name="PHY_CTRL">
		<!--
			in hdmi_phy_reset() it appears to be toggling SW_RESET/
			SW_RESET_PLL based on the value of the bit above, so
			I'm guessing the bit above is a polarit bit
		 -->
		<bitfield name="SW_RESET_PLL" pos="0" type="boolean"/>
		<bitfield name="SW_RESET_PLL_LOW" pos="1" type="boolean"/>
		<bitfield name="SW_RESET" pos="2" type="boolean"/>
		<bitfield name="SW_RESET_LOW" pos="3" type="boolean"/>
	</reg32>
	<reg32 offset="0x002dc" name="CEC_WR_RANGE"/>
	<reg32 offset="0x002e0" name="CEC_RD_RANGE"/>
	<reg32 offset="0x002e4" name="VERSION"/>
	<reg32 offset="0x00360" name="CEC_COMPL_CTL"/>
	<reg32 offset="0x00364" name="CEC_RD_START_RANGE"/>
	<reg32 offset="0x00368" name="CEC_RD_TOTAL_RANGE"/>
	<reg32 offset="0x0036c" name="CEC_RD_ERR_RESP_LO"/>
	<reg32 offset="0x00370" name="CEC_WR_CHECK_CONFIG"/>

</domain>

<domain name="HDMI_8x60" width="32">
	<reg32 offset="0x00000" name="PHY_REG0">
		<bitfield name="DESER_DEL_CTRL" low="2" high="4" type="uint"/>
	</reg32>
	<reg32 offset="0x00004" name="PHY_REG1">
		<bitfield name="DTEST_MUX_SEL" low="4" high="7" type="uint"/>
		<bitfield name="OUTVOL_SWING_CTRL" low="0" high="3" type="uint"/>
	</reg32>
	<reg32 offset="0x00008" name="PHY_REG2">
		<bitfield name="PD_DESER" pos="0" type="boolean"/>
		<bitfield name="PD_DRIVE_1" pos="1" type="boolean"/>
		<bitfield name="PD_DRIVE_2" pos="2" type="boolean"/>
		<bitfield name="PD_DRIVE_3" pos="3" type="boolean"/>
		<bitfield name="PD_DRIVE_4" pos="4" type="boolean"/>
		<bitfield name="PD_PLL" pos="5" type="boolean"/>
		<bitfield name="PD_PWRGEN" pos="6" type="boolean"/>
		<bitfield name="RCV_SENSE_EN" pos="7" type="boolean"/>
	</reg32>
	<reg32 offset="0x0000c" name="PHY_REG3">
		<bitfield name="PLL_ENABLE" pos="0" type="boolean"/>
	</reg32>
	<reg32 offset="0x00010" name="PHY_REG4"/>
	<reg32 offset="0x00014" name="PHY_REG5"/>
	<reg32 offset="0x00018" name="PHY_REG6"/>
	<reg32 offset="0x0001c" name="PHY_REG7"/>
	<reg32 offset="0x00020" name="PHY_REG8"/>
	<reg32 offset="0x00024" name="PHY_REG9"/>
	<reg32 offset="0x00028" name="PHY_REG10"/>
	<reg32 offset="0x0002c" name="PHY_REG11"/>
	<reg32 offset="0x00030" name="PHY_REG12">
		<bitfield name="RETIMING_EN" pos="0" type="boolean"/>
		<bitfield name="PLL_LOCK_DETECT_EN" pos="1" type="boolean"/>
		<bitfield name="FORCE_LOCK" pos="4" type="boolean"/>
	</reg32>
</domain>

<domain name="HDMI_8960" width="32">
	<!--
		some of the bitfields may be same as 8x60.. but no helpful comments
		in msm_dss_io_8960.c
	 -->
	<reg32 offset="0x00000" name="PHY_REG0"/>
	<reg32 offset="0x00004" name="PHY_REG1"/>
	<reg32 offset="0x00008" name="PHY_REG2"/>
	<reg32 offset="0x0000c" name="PHY_REG3"/>
	<reg32 offset="0x00010" name="PHY_REG4"/>
	<reg32 offset="0x00014" name="PHY_REG5"/>
	<reg32 offset="0x00018" name="PHY_REG6"/>
	<reg32 offset="0x0001c" name="PHY_REG7"/>
	<reg32 offset="0x00020" name="PHY_REG8"/>
	<reg32 offset="0x00024" name="PHY_REG9"/>
	<reg32 offset="0x00028" name="PHY_REG10"/>
	<reg32 offset="0x0002c" name="PHY_REG11"/>
	<reg32 offset="0x00030" name="PHY_REG12">
		<bitfield name="SW_RESET" pos="5" type="boolean"/>
		<bitfield name="PWRDN_B" pos="7" type="boolean"/>
	</reg32>
	<reg32 offset="0x00034" name="PHY_REG_BIST_CFG"/>
	<reg32 offset="0x00038" name="PHY_DEBUG_BUS_SEL"/>
	<reg32 offset="0x0003c" name="PHY_REG_MISC0"/>
	<reg32 offset="0x00040" name="PHY_REG13"/>
	<reg32 offset="0x00044" name="PHY_REG14"/>
	<reg32 offset="0x00048" name="PHY_REG15"/>
</domain>

<domain name="HDMI_8960_PHY_PLL" width="32">
	<reg32 offset="0x00000" name="REFCLK_CFG"/>
	<reg32 offset="0x00004" name="CHRG_PUMP_CFG"/>
	<reg32 offset="0x00008" name="LOOP_FLT_CFG0"/>
	<reg32 offset="0x0000c" name="LOOP_FLT_CFG1"/>
	<reg32 offset="0x00010" name="IDAC_ADJ_CFG"/>
	<reg32 offset="0x00014" name="I_VI_KVCO_CFG"/>
	<reg32 offset="0x00018" name="PWRDN_B">
		<bitfield name="PD_PLL" pos="1" type="boolean"/>
		<bitfield name="PLL_PWRDN_B" pos="3" type="boolean"/>
	</reg32>
	<reg32 offset="0x0001c" name="SDM_CFG0"/>
	<reg32 offset="0x00020" name="SDM_CFG1"/>
	<reg32 offset="0x00024" name="SDM_CFG2"/>
	<reg32 offset="0x00028" name="SDM_CFG3"/>
	<reg32 offset="0x0002c" name="SDM_CFG4"/>
	<reg32 offset="0x00030" name="SSC_CFG0"/>
	<reg32 offset="0x00034" name="SSC_CFG1"/>
	<reg32 offset="0x00038" name="SSC_CFG2"/>
	<reg32 offset="0x0003c" name="SSC_CFG3"/>
	<reg32 offset="0x00040" name="LOCKDET_CFG0"/>
	<reg32 offset="0x00044" name="LOCKDET_CFG1"/>
	<reg32 offset="0x00048" name="LOCKDET_CFG2"/>
	<reg32 offset="0x0004c" name="VCOCAL_CFG0"/>
	<reg32 offset="0x00050" name="VCOCAL_CFG1"/>
	<reg32 offset="0x00054" name="VCOCAL_CFG2"/>
	<reg32 offset="0x00058" name="VCOCAL_CFG3"/>
	<reg32 offset="0x0005c" name="VCOCAL_CFG4"/>
	<reg32 offset="0x00060" name="VCOCAL_CFG5"/>
	<reg32 offset="0x00064" name="VCOCAL_CFG6"/>
	<reg32 offset="0x00068" name="VCOCAL_CFG7"/>
	<reg32 offset="0x0006c" name="DEBUG_SEL"/>
	<reg32 offset="0x00070" name="MISC0"/>
	<reg32 offset="0x00074" name="MISC1"/>
	<reg32 offset="0x00078" name="MISC2"/>
	<reg32 offset="0x0007c" name="MISC3"/>
	<reg32 offset="0x00080" name="MISC4"/>
	<reg32 offset="0x00084" name="MISC5"/>
	<reg32 offset="0x00088" name="MISC6"/>
	<reg32 offset="0x0008c" name="DEBUG_BUS0"/>
	<reg32 offset="0x00090" name="DEBUG_BUS1"/>
	<reg32 offset="0x00094" name="DEBUG_BUS2"/>
	<reg32 offset="0x00098" name="STATUS0">
		<bitfield name="PLL_LOCK" pos="0" type="boolean"/>
	</reg32>
	<reg32 offset="0x0009c" name="STATUS1"/>
</domain>

<domain name="HDMI_8x74" width="32">
	<!--
		seems to be all mdp5+ have same?
	 -->
	<reg32 offset="0x00000" name="ANA_CFG0"/>
	<reg32 offset="0x00004" name="ANA_CFG1"/>
	<reg32 offset="0x00008" name="ANA_CFG2"/>
	<reg32 offset="0x0000c" name="ANA_CFG3"/>
	<reg32 offset="0x00010" name="PD_CTRL0"/>
	<reg32 offset="0x00014" name="PD_CTRL1"/>
	<reg32 offset="0x00018" name="GLB_CFG"/>
	<reg32 offset="0x0001c" name="DCC_CFG0"/>
	<reg32 offset="0x00020" name="DCC_CFG1"/>
	<reg32 offset="0x00024" name="TXCAL_CFG0"/>
	<reg32 offset="0x00028" name="TXCAL_CFG1"/>
	<reg32 offset="0x0002c" name="TXCAL_CFG2"/>
	<reg32 offset="0x00030" name="TXCAL_CFG3"/>
	<reg32 offset="0x00034" name="BIST_CFG0"/>
	<reg32 offset="0x0003c" name="BIST_PATN0"/>
	<reg32 offset="0x00040" name="BIST_PATN1"/>
	<reg32 offset="0x00044" name="BIST_PATN2"/>
	<reg32 offset="0x00048" name="BIST_PATN3"/>
	<reg32 offset="0x0005c" name="STATUS"/>
</domain>

<domain name="HDMI_28nm_PHY_PLL" width="32">
	<reg32 offset="0x00000" name="REFCLK_CFG"/>
	<reg32 offset="0x00004" name="POSTDIV1_CFG"/>
	<reg32 offset="0x00008" name="CHGPUMP_CFG"/>
	<reg32 offset="0x0000C" name="VCOLPF_CFG"/>
	<reg32 offset="0x00010" name="VREG_CFG"/>
	<reg32 offset="0x00014" name="PWRGEN_CFG"/>
	<reg32 offset="0x00018" name="DMUX_CFG"/>
	<reg32 offset="0x0001C" name="AMUX_CFG"/>
	<reg32 offset="0x00020" name="GLB_CFG">
		<bitfield name="PLL_PWRDN_B" pos="0" type="boolean"/>
		<bitfield name="PLL_LDO_PWRDN_B" pos="1" type="boolean"/>
		<bitfield name="PLL_PWRGEN_PWRDN_B" pos="2" type="boolean"/>
		<bitfield name="PLL_ENABLE" pos="3" type="boolean"/>
	</reg32>
	<reg32 offset="0x00024" name="POSTDIV2_CFG"/>
	<reg32 offset="0x00028" name="POSTDIV3_CFG"/>
	<reg32 offset="0x0002C" name="LPFR_CFG"/>
	<reg32 offset="0x00030" name="LPFC1_CFG"/>
	<reg32 offset="0x00034" name="LPFC2_CFG"/>
	<reg32 offset="0x00038" name="SDM_CFG0"/>
	<reg32 offset="0x0003C" name="SDM_CFG1"/>
	<reg32 offset="0x00040" name="SDM_CFG2"/>
	<reg32 offset="0x00044" name="SDM_CFG3"/>
	<reg32 offset="0x00048" name="SDM_CFG4"/>
	<reg32 offset="0x0004C" name="SSC_CFG0"/>
	<reg32 offset="0x00050" name="SSC_CFG1"/>
	<reg32 offset="0x00054" name="SSC_CFG2"/>
	<reg32 offset="0x00058" name="SSC_CFG3"/>
	<reg32 offset="0x0005C" name="LKDET_CFG0"/>
	<reg32 offset="0x00060" name="LKDET_CFG1"/>
	<reg32 offset="0x00064" name="LKDET_CFG2"/>
	<reg32 offset="0x00068" name="TEST_CFG">
		<bitfield name="PLL_SW_RESET" pos="0" type="boolean"/>
	</reg32>
	<reg32 offset="0x0006C" name="CAL_CFG0"/>
	<reg32 offset="0x00070" name="CAL_CFG1"/>
	<reg32 offset="0x00074" name="CAL_CFG2"/>
	<reg32 offset="0x00078" name="CAL_CFG3"/>
	<reg32 offset="0x0007C" name="CAL_CFG4"/>
	<reg32 offset="0x00080" name="CAL_CFG5"/>
	<reg32 offset="0x00084" name="CAL_CFG6"/>
	<reg32 offset="0x00088" name="CAL_CFG7"/>
	<reg32 offset="0x0008C" name="CAL_CFG8"/>
	<reg32 offset="0x00090" name="CAL_CFG9"/>
	<reg32 offset="0x00094" name="CAL_CFG10"/>
	<reg32 offset="0x00098" name="CAL_CFG11"/>
	<reg32 offset="0x0009C" name="EFUSE_CFG"/>
	<reg32 offset="0x000A0" name="DEBUG_BUS_SEL"/>
	<reg32 offset="0x000C0" name="STATUS"/>
</domain>

<domain name="HDMI_8996_PHY" width="32">
	<reg32 offset="0x00000" name="CFG"/>
	<reg32 offset="0x00004" name="PD_CTL"/>
	<reg32 offset="0x00008" name="MODE"/>
	<reg32 offset="0x0000C" name="MISR_CLEAR"/>
	<reg32 offset="0x00010" name="TX0_TX1_BIST_CFG0"/>
	<reg32 offset="0x00014" name="TX0_TX1_BIST_CFG1"/>
	<reg32 offset="0x00018" name="TX0_TX1_PRBS_SEED_BYTE0"/>
	<reg32 offset="0x0001C" name="TX0_TX1_PRBS_SEED_BYTE1"/>
	<reg32 offset="0x00020" name="TX0_TX1_BIST_PATTERN0"/>
	<reg32 offset="0x00024" name="TX0_TX1_BIST_PATTERN1"/>
	<reg32 offset="0x00028" name="TX2_TX3_BIST_CFG0"/>
	<reg32 offset="0x0002C" name="TX2_TX3_BIST_CFG1"/>
	<reg32 offset="0x00030" name="TX2_TX3_PRBS_SEED_BYTE0"/>
	<reg32 offset="0x00034" name="TX2_TX3_PRBS_SEED_BYTE1"/>
	<reg32 offset="0x00038" name="TX2_TX3_BIST_PATTERN0"/>
	<reg32 offset="0x0003C" name="TX2_TX3_BIST_PATTERN1"/>
	<reg32 offset="0x00040" name="DEBUG_BUS_SEL"/>
	<reg32 offset="0x00044" name="TXCAL_CFG0"/>
	<reg32 offset="0x00048" name="TXCAL_CFG1"/>
	<reg32 offset="0x0004C" name="TX0_TX1_LANE_CTL"/>
	<reg32 offset="0x00050" name="TX2_TX3_LANE_CTL"/>
	<reg32 offset="0x00054" name="LANE_BIST_CONFIG"/>
	<reg32 offset="0x00058" name="CLOCK"/>
	<reg32 offset="0x0005C" name="MISC1"/>
	<reg32 offset="0x00060" name="MISC2"/>
	<reg32 offset="0x00064" name="TX0_TX1_BIST_STATUS0"/>
	<reg32 offset="0x00068" name="TX0_TX1_BIST_STATUS1"/>
	<reg32 offset="0x0006C" name="TX0_TX1_BIST_STATUS2"/>
	<reg32 offset="0x00070" name="TX2_TX3_BIST_STATUS0"/>
	<reg32 offset="0x00074" name="TX2_TX3_BIST_STATUS1"/>
	<reg32 offset="0x00078" name="TX2_TX3_BIST_STATUS2"/>
	<reg32 offset="0x0007C" name="PRE_MISR_STATUS0"/>
	<reg32 offset="0x00080" name="PRE_MISR_STATUS1"/>
	<reg32 offset="0x00084" name="PRE_MISR_STATUS2"/>
	<reg32 offset="0x00088" name="PRE_MISR_STATUS3"/>
	<reg32 offset="0x0008C" name="POST_MISR_STATUS0"/>
	<reg32 offset="0x00090" name="POST_MISR_STATUS1"/>
	<reg32 offset="0x00094" name="POST_MISR_STATUS2"/>
	<reg32 offset="0x00098" name="POST_MISR_STATUS3"/>
	<reg32 offset="0x0009C" name="STATUS"/>
	<reg32 offset="0x000A0" name="MISC3_STATUS"/>
	<reg32 offset="0x000A4" name="MISC4_STATUS"/>
	<reg32 offset="0x000A8" name="DEBUG_BUS0"/>
	<reg32 offset="0x000AC" name="DEBUG_BUS1"/>
	<reg32 offset="0x000B0" name="DEBUG_BUS2"/>
	<reg32 offset="0x000B4" name="DEBUG_BUS3"/>
	<reg32 offset="0x000B8" name="PHY_REVISION_ID0"/>
	<reg32 offset="0x000BC" name="PHY_REVISION_ID1"/>
	<reg32 offset="0x000C0" name="PHY_REVISION_ID2"/>
	<reg32 offset="0x000C4" name="PHY_REVISION_ID3"/>
</domain>

<domain name="HDMI_PHY_QSERDES_COM" width="32">
	<reg32 offset="0x00000" name="ATB_SEL1"/>
	<reg32 offset="0x00004" name="ATB_SEL2"/>
	<reg32 offset="0x00008" name="FREQ_UPDATE"/>
	<reg32 offset="0x0000C" name="BG_TIMER"/>
	<reg32 offset="0x00010" name="SSC_EN_CENTER"/>
	<reg32 offset="0x00014" name="SSC_ADJ_PER1"/>
	<reg32 offset="0x00018" name="SSC_ADJ_PER2"/>
	<reg32 offset="0x0001C" name="SSC_PER1"/>
	<reg32 offset="0x00020" name="SSC_PER2"/>
	<reg32 offset="0x00024" name="SSC_STEP_SIZE1"/>
	<reg32 offset="0x00028" name="SSC_STEP_SIZE2"/>
	<reg32 offset="0x0002C" name="POST_DIV"/>
	<reg32 offset="0x00030" name="POST_DIV_MUX"/>
	<reg32 offset="0x00034" name="BIAS_EN_CLKBUFLR_EN"/>
	<reg32 offset="0x00038" name="CLK_ENABLE1"/>
	<reg32 offset="0x0003C" name="SYS_CLK_CTRL"/>
	<reg32 offset="0x00040" name="SYSCLK_BUF_ENABLE"/>
	<reg32 offset="0x00044" name="PLL_EN"/>
	<reg32 offset="0x00048" name="PLL_IVCO"/>
	<reg32 offset="0x0004C" name="LOCK_CMP1_MODE0"/>
	<reg32 offset="0x00050" name="LOCK_CMP2_MODE0"/>
	<reg32 offset="0x00054" name="LOCK_CMP3_MODE0"/>
	<reg32 offset="0x00058" name="LOCK_CMP1_MODE1"/>
	<reg32 offset="0x0005C" name="LOCK_CMP2_MODE1"/>
	<reg32 offset="0x00060" name="LOCK_CMP3_MODE1"/>
	<reg32 offset="0x00064" name="LOCK_CMP1_MODE2"/>
	<reg32 offset="0x00064" name="CMN_RSVD0"/>
	<reg32 offset="0x00068" name="LOCK_CMP2_MODE2"/>
	<reg32 offset="0x00068" name="EP_CLOCK_DETECT_CTRL"/>
	<reg32 offset="0x0006C" name="LOCK_CMP3_MODE2"/>
	<reg32 offset="0x0006C" name="SYSCLK_DET_COMP_STATUS"/>
	<reg32 offset="0x00070" name="BG_TRIM"/>
	<reg32 offset="0x00074" name="CLK_EP_DIV"/>
	<reg32 offset="0x00078" name="CP_CTRL_MODE0"/>
	<reg32 offset="0x0007C" name="CP_CTRL_MODE1"/>
	<reg32 offset="0x00080" name="CP_CTRL_MODE2"/>
	<reg32 offset="0x00080" name="CMN_RSVD1"/>
	<reg32 offset="0x00084" name="PLL_RCTRL_MODE0"/>
	<reg32 offset="0x00088" name="PLL_RCTRL_MODE1"/>
	<reg32 offset="0x0008C" name="PLL_RCTRL_MODE2"/>
	<reg32 offset="0x0008C" name="CMN_RSVD2"/>
	<reg32 offset="0x00090" name="PLL_CCTRL_MODE0"/>
	<reg32 offset="0x00094" name="PLL_CCTRL_MODE1"/>
	<reg32 offset="0x00098" name="PLL_CCTRL_MODE2"/>
	<reg32 offset="0x00098" name="CMN_RSVD3"/>
	<reg32 offset="0x0009C" name="PLL_CNTRL"/>
	<reg32 offset="0x000A0" name="PHASE_SEL_CTRL"/>
	<reg32 offset="0x000A4" name="PHASE_SEL_DC"/>
	<reg32 offset="0x000A8" name="CORE_CLK_IN_SYNC_SEL"/>
	<reg32 offset="0x000A8" name="BIAS_EN_CTRL_BY_PSM"/>
	<reg32 offset="0x000AC" name="SYSCLK_EN_SEL"/>
	<reg32 offset="0x000B0" name="CML_SYSCLK_SEL"/>
	<reg32 offset="0x000B4" name="RESETSM_CNTRL"/>
	<reg32 offset="0x000B8" name="RESETSM_CNTRL2"/>
	<reg32 offset="0x000BC" name="RESTRIM_CTRL"/>
	<reg32 offset="0x000C0" name="RESTRIM_CTRL2"/>
	<reg32 offset="0x000C4" name="RESCODE_DIV_NUM"/>
	<reg32 offset="0x000C8" name="LOCK_CMP_EN"/>
	<reg32 offset="0x000CC" name="LOCK_CMP_CFG"/>
	<reg32 offset="0x000D0" name="DEC_START_MODE0"/>
	<reg32 offset="0x000D4" name="DEC_START_MODE1"/>
	<reg32 offset="0x000D8" name="DEC_START_MODE2"/>
	<reg32 offset="0x000D8" name="VCOCAL_DEADMAN_CTRL"/>
	<reg32 offset="0x000DC" name="DIV_FRAC_START1_MODE0"/>
	<reg32 offset="0x000E0" name="DIV_FRAC_START2_MODE0"/>
	<reg32 offset="0x000E4" name="DIV_FRAC_START3_MODE0"/>
	<reg32 offset="0x000E8" name="DIV_FRAC_START1_MODE1"/>
	<reg32 offset="0x000EC" name="DIV_FRAC_START2_MODE1"/>
	<reg32 offset="0x000F0" name="DIV_FRAC_START3_MODE1"/>
	<reg32 offset="0x000F4" name="DIV_FRAC_START1_MODE2"/>
	<reg32 offset="0x000F4" name="VCO_TUNE_MINVAL1"/>
	<reg32 offset="0x000F8" name="DIV_FRAC_START2_MODE2"/>
	<reg32 offset="0x000F8" name="VCO_TUNE_MINVAL2"/>
	<reg32 offset="0x000FC" name="DIV_FRAC_START3_MODE2"/>
	<reg32 offset="0x000FC" name="CMN_RSVD4"/>
	<reg32 offset="0x00100" name="INTEGLOOP_INITVAL"/>
	<reg32 offset="0x00104" name="INTEGLOOP_EN"/>
	<reg32 offset="0x00108" name="INTEGLOOP_GAIN0_MODE0"/>
	<reg32 offset="0x0010C" name="INTEGLOOP_GAIN1_MODE0"/>
	<reg32 offset="0x00110" name="INTEGLOOP_GAIN0_MODE1"/>
	<reg32 offset="0x00114" name="INTEGLOOP_GAIN1_MODE1"/>
	<reg32 offset="0x00118" name="INTEGLOOP_GAIN0_MODE2"/>
	<reg32 offset="0x00118" name="VCO_TUNE_MAXVAL1"/>
	<reg32 offset="0x0011C" name="INTEGLOOP_GAIN1_MODE2"/>
	<reg32 offset="0x0011C" name="VCO_TUNE_MAXVAL2"/>
	<reg32 offset="0x00120" name="RES_TRIM_CONTROL2"/>
	<reg32 offset="0x00124" name="VCO_TUNE_CTRL"/>
	<reg32 offset="0x00128" name="VCO_TUNE_MAP"/>
	<reg32 offset="0x0012C" name="VCO_TUNE1_MODE0"/>
	<reg32 offset="0x00130" name="VCO_TUNE2_MODE0"/>
	<reg32 offset="0x00134" name="VCO_TUNE1_MODE1"/>
	<reg32 offset="0x00138" name="VCO_TUNE2_MODE1"/>
	<reg32 offset="0x0013C" name="VCO_TUNE1_MODE2"/>
	<reg32 offset="0x0013C" name="VCO_TUNE_INITVAL1"/>
	<reg32 offset="0x00140" name="VCO_TUNE2_MODE2"/>
	<reg32 offset="0x00140" name="VCO_TUNE_INITVAL2"/>
	<reg32 offset="0x00144" name="VCO_TUNE_TIMER1"/>
	<reg32 offset="0x00148" name="VCO_TUNE_TIMER2"/>
	<reg32 offset="0x0014C" name="SAR"/>
	<reg32 offset="0x00150" name="SAR_CLK"/>
	<reg32 offset="0x00154" name="SAR_CODE_OUT_STATUS"/>
	<reg32 offset="0x00158" name="SAR_CODE_READY_STATUS"/>
	<reg32 offset="0x0015C" name="CMN_STATUS"/>
	<reg32 offset="0x00160" name="RESET_SM_STATUS"/>
	<reg32 offset="0x00164" name="RESTRIM_CODE_STATUS"/>
	<reg32 offset="0x00168" name="PLLCAL_CODE1_STATUS"/>
	<reg32 offset="0x0016C" name="PLLCAL_CODE2_STATUS"/>
	<reg32 offset="0x00170" name="BG_CTRL"/>
	<reg32 offset="0x00174" name="CLK_SELECT"/>
	<reg32 offset="0x00178" name="HSCLK_SEL"/>
	<reg32 offset="0x0017C" name="INTEGLOOP_BINCODE_STATUS"/>
	<reg32 offset="0x00180" name="PLL_ANALOG"/>
	<reg32 offset="0x00184" name="CORECLK_DIV"/>
	<reg32 offset="0x00188" name="SW_RESET"/>
	<reg32 offset="0x0018C" name="CORE_CLK_EN"/>
	<reg32 offset="0x00190" name="C_READY_STATUS"/>
	<reg32 offset="0x00194" name="CMN_CONFIG"/>
	<reg32 offset="0x00198" name="CMN_RATE_OVERRIDE"/>
	<reg32 offset="0x0019C" name="SVS_MODE_CLK_SEL"/>
	<reg32 offset="0x001A0" name="DEBUG_BUS0"/>
	<reg32 offset="0x001A4" name="DEBUG_BUS1"/>
	<reg32 offset="0x001A8" name="DEBUG_BUS2"/>
	<reg32 offset="0x001AC" name="DEBUG_BUS3"/>
	<reg32 offset="0x001B0" name="DEBUG_BUS_SEL"/>
	<reg32 offset="0x001B4" name="CMN_MISC1"/>
	<reg32 offset="0x001B8" name="CMN_MISC2"/>
	<reg32 offset="0x001BC" name="CORECLK_DIV_MODE1"/>
	<reg32 offset="0x001C0" name="CORECLK_DIV_MODE2"/>
	<reg32 offset="0x001C4" name="CMN_RSVD5"/>
</domain>


<domain name="HDMI_PHY_QSERDES_TX_LX" width="32">
		<reg32 offset="0x00000" name="BIST_MODE_LANENO"/>
		<reg32 offset="0x00004" name="BIST_INVERT"/>
		<reg32 offset="0x00008" name="CLKBUF_ENABLE"/>
		<reg32 offset="0x0000C" name="CMN_CONTROL_ONE"/>
		<reg32 offset="0x00010" name="CMN_CONTROL_TWO"/>
		<reg32 offset="0x00014" name="CMN_CONTROL_THREE"/>
		<reg32 offset="0x00018" name="TX_EMP_POST1_LVL"/>
		<reg32 offset="0x0001C" name="TX_POST2_EMPH"/>
		<reg32 offset="0x00020" name="TX_BOOST_LVL_UP_DN"/>
		<reg32 offset="0x00024" name="HP_PD_ENABLES"/>
		<reg32 offset="0x00028" name="TX_IDLE_LVL_LARGE_AMP"/>
		<reg32 offset="0x0002C" name="TX_DRV_LVL"/>
		<reg32 offset="0x00030" name="TX_DRV_LVL_OFFSET"/>
		<reg32 offset="0x00034" name="RESET_TSYNC_EN"/>
		<reg32 offset="0x00038" name="PRE_STALL_LDO_BOOST_EN"/>
		<reg32 offset="0x0003C" name="TX_BAND"/>
		<reg32 offset="0x00040" name="SLEW_CNTL"/>
		<reg32 offset="0x00044" name="INTERFACE_SELECT"/>
		<reg32 offset="0x00048" name="LPB_EN"/>
		<reg32 offset="0x0004C" name="RES_CODE_LANE_TX"/>
		<reg32 offset="0x00050" name="RES_CODE_LANE_RX"/>
		<reg32 offset="0x00054" name="RES_CODE_LANE_OFFSET"/>
		<reg32 offset="0x00058" name="PERL_LENGTH1"/>
		<reg32 offset="0x0005C" name="PERL_LENGTH2"/>
		<reg32 offset="0x00060" name="SERDES_BYP_EN_OUT"/>
		<reg32 offset="0x00064" name="DEBUG_BUS_SEL"/>
		<reg32 offset="0x00068" name="HIGHZ_TRANSCEIVEREN_BIAS_DRVR_EN"/>
		<reg32 offset="0x0006C" name="TX_POL_INV"/>
		<reg32 offset="0x00070" name="PARRATE_REC_DETECT_IDLE_EN"/>
		<reg32 offset="0x00074" name="BIST_PATTERN1"/>
		<reg32 offset="0x00078" name="BIST_PATTERN2"/>
		<reg32 offset="0x0007C" name="BIST_PATTERN3"/>
		<reg32 offset="0x00080" name="BIST_PATTERN4"/>
		<reg32 offset="0x00084" name="BIST_PATTERN5"/>
		<reg32 offset="0x00088" name="BIST_PATTERN6"/>
		<reg32 offset="0x0008C" name="BIST_PATTERN7"/>
		<reg32 offset="0x00090" name="BIST_PATTERN8"/>
		<reg32 offset="0x00094" name="LANE_MODE"/>
		<reg32 offset="0x00098" name="IDAC_CAL_LANE_MODE"/>
		<reg32 offset="0x0009C" name="IDAC_CAL_LANE_MODE_CONFIGURATION"/>
		<reg32 offset="0x000A0" name="ATB_SEL1"/>
		<reg32 offset="0x000A4" name="ATB_SEL2"/>
		<reg32 offset="0x000A8" name="RCV_DETECT_LVL"/>
		<reg32 offset="0x000AC" name="RCV_DETECT_LVL_2"/>
		<reg32 offset="0x000B0" name="PRBS_SEED1"/>
		<reg32 offset="0x000B4" name="PRBS_SEED2"/>
		<reg32 offset="0x000B8" name="PRBS_SEED3"/>
		<reg32 offset="0x000BC" name="PRBS_SEED4"/>
		<reg32 offset="0x000C0" name="RESET_GEN"/>
		<reg32 offset="0x000C4" name="RESET_GEN_MUXES"/>
		<reg32 offset="0x000C8" name="TRAN_DRVR_EMP_EN"/>
		<reg32 offset="0x000CC" name="TX_INTERFACE_MODE"/>
		<reg32 offset="0x000D0" name="PWM_CTRL"/>
		<reg32 offset="0x000D4" name="PWM_ENCODED_OR_DATA"/>
		<reg32 offset="0x000D8" name="PWM_GEAR_1_DIVIDER_BAND2"/>
		<reg32 offset="0x000DC" name="PWM_GEAR_2_DIVIDER_BAND2"/>
		<reg32 offset="0x000E0" name="PWM_GEAR_3_DIVIDER_BAND2"/>
		<reg32 offset="0x000E4" name="PWM_GEAR_4_DIVIDER_BAND2"/>
		<reg32 offset="0x000E8" name="PWM_GEAR_1_DIVIDER_BAND0_1"/>
		<reg32 offset="0x000EC" name="PWM_GEAR_2_DIVIDER_BAND0_1"/>
		<reg32 offset="0x000F0" name="PWM_GEAR_3_DIVIDER_BAND0_1"/>
		<reg32 offset="0x000F4" name="PWM_GEAR_4_DIVIDER_BAND0_1"/>
		<reg32 offset="0x000F8" name="VMODE_CTRL1"/>
		<reg32 offset="0x000FC" name="VMODE_CTRL2"/>
		<reg32 offset="0x00100" name="TX_ALOG_INTF_OBSV_CNTL"/>
		<reg32 offset="0x00104" name="BIST_STATUS"/>
		<reg32 offset="0x00108" name="BIST_ERROR_COUNT1"/>
		<reg32 offset="0x0010C" name="BIST_ERROR_COUNT2"/>
		<reg32 offset="0x00110" name="TX_ALOG_INTF_OBSV"/>
</domain>

<domain name="HDMI_8998_PHY" width="32">
	<reg32 offset="0x00000" name="CFG"/>
	<reg32 offset="0x00004" name="PD_CTL"/>
	<reg32 offset="0x00010" name="MODE"/>
	<reg32 offset="0x0005C" name="CLOCK"/>
	<reg32 offset="0x00068" name="CMN_CTRL"/>
	<reg32 offset="0x000B4" name="STATUS"/>
</domain>

<domain name="HDMI_8998_PHY_QSERDES_COM" width="32">
	<reg32 offset="0x0000" name="ATB_SEL1"/>
	<reg32 offset="0x0004" name="ATB_SEL2"/>
	<reg32 offset="0x0008" name="FREQ_UPDATE"/>
	<reg32 offset="0x000C" name="BG_TIMER"/>
	<reg32 offset="0x0010" name="SSC_EN_CENTER"/>
	<reg32 offset="0x0014" name="SSC_ADJ_PER1"/>
	<reg32 offset="0x0018" name="SSC_ADJ_PER2"/>
	<reg32 offset="0x001C" name="SSC_PER1"/>
	<reg32 offset="0x0020" name="SSC_PER2"/>
	<reg32 offset="0x0024" name="SSC_STEP_SIZE1"/>
	<reg32 offset="0x0028" name="SSC_STEP_SIZE2"/>
	<reg32 offset="0x002C" name="POST_DIV"/>
	<reg32 offset="0x0030" name="POST_DIV_MUX"/>
	<reg32 offset="0x0034" name="BIAS_EN_CLKBUFLR_EN"/>
	<reg32 offset="0x0038" name="CLK_ENABLE1"/>
	<reg32 offset="0x003C" name="SYS_CLK_CTRL"/>
	<reg32 offset="0x0040" name="SYSCLK_BUF_ENABLE"/>
	<reg32 offset="0x0044" name="PLL_EN"/>
	<reg32 offset="0x0048" name="PLL_IVCO"/>
	<reg32 offset="0x004C" name="CMN_IETRIM"/>
	<reg32 offset="0x0050" name="CMN_IPTRIM"/>
	<reg32 offset="0x0060" name="CP_CTRL_MODE0"/>
	<reg32 offset="0x0064" name="CP_CTRL_MODE1"/>
	<reg32 offset="0x0068" name="PLL_RCTRL_MODE0"/>
	<reg32 offset="0x006C" name="PLL_RCTRL_MODE1"/>
	<reg32 offset="0x0070" name="PLL_CCTRL_MODE0"/>
	<reg32 offset="0x0074" name="PLL_CCTRL_MODE1"/>
	<reg32 offset="0x0078" name="PLL_CNTRL"/>
	<reg32 offset="0x007C" name="BIAS_EN_CTRL_BY_PSM"/>
	<reg32 offset="0x0080" name="SYSCLK_EN_SEL"/>
	<reg32 offset="0x0084" name="CML_SYSCLK_SEL"/>
	<reg32 offset="0x0088" name="RESETSM_CNTRL"/>
	<reg32 offset="0x008C" name="RESETSM_CNTRL2"/>
	<reg32 offset="0x0090" name="LOCK_CMP_EN"/>
	<reg32 offset="0x0094" name="LOCK_CMP_CFG"/>
	<reg32 offset="0x0098" name="LOCK_CMP1_MODE0"/>
	<reg32 offset="0x009C" name="LOCK_CMP2_MODE0"/>
	<reg32 offset="0x00A0" name="LOCK_CMP3_MODE0"/>
	<reg32 offset="0x00B0" name="DEC_START_MODE0"/>
	<reg32 offset="0x00B4" name="DEC_START_MODE1"/>
	<reg32 offset="0x00B8" name="DIV_FRAC_START1_MODE0"/>
	<reg32 offset="0x00BC" name="DIV_FRAC_START2_MODE0"/>
	<reg32 offset="0x00C0" name="DIV_FRAC_START3_MODE0"/>
	<reg32 offset="0x00C4" name="DIV_FRAC_START1_MODE1"/>
	<reg32 offset="0x00C8" name="DIV_FRAC_START2_MODE1"/>
	<reg32 offset="0x00CC" name="DIV_FRAC_START3_MODE1"/>
	<reg32 offset="0x00D0" name="INTEGLOOP_INITVAL"/>
	<reg32 offset="0x00D4" name="INTEGLOOP_EN"/>
	<reg32 offset="0x00D8" name="INTEGLOOP_GAIN0_MODE0"/>
	<reg32 offset="0x00DC" name="INTEGLOOP_GAIN1_MODE0"/>
	<reg32 offset="0x00E0" name="INTEGLOOP_GAIN0_MODE1"/>
	<reg32 offset="0x00E4" name="INTEGLOOP_GAIN1_MODE1"/>
	<reg32 offset="0x00E8" name="VCOCAL_DEADMAN_CTRL"/>
	<reg32 offset="0x00EC" name="VCO_TUNE_CTRL"/>
	<reg32 offset="0x00F0" name="VCO_TUNE_MAP"/>
	<reg32 offset="0x0124" name="CMN_STATUS"/>
	<reg32 offset="0x0128" name="RESET_SM_STATUS"/>
	<reg32 offset="0x0138" name="CLK_SEL"/>
	<reg32 offset="0x013C" name="HSCLK_SEL"/>
	<reg32 offset="0x0148" name="CORECLK_DIV_MODE0"/>
	<reg32 offset="0x0150" name="SW_RESET"/>
	<reg32 offset="0x0154" name="CORE_CLK_EN"/>
	<reg32 offset="0x0158" name="C_READY_STATUS"/>
	<reg32 offset="0x015C" name="CMN_CONFIG"/>
	<reg32 offset="0x0164" name="SVS_MODE_CLK_SEL"/>
</domain>

<domain name="HDMI_8998_PHY_TXn" width="32">
	<reg32 offset="0x0000" name="EMP_POST1_LVL"/>
	<reg32 offset="0x0008" name="INTERFACE_SELECT_TX_BAND"/>
	<reg32 offset="0x000C" name="CLKBUF_TERM_ENABLE"/>
	<reg32 offset="0x0014" name="DRV_LVL_RES_CODE_OFFSET"/>
	<reg32 offset="0x0018" name="DRV_LVL"/>
	<reg32 offset="0x001C" name="LANE_CONFIG"/>
	<reg32 offset="0x0024" name="PRE_DRIVER_1"/>
	<reg32 offset="0x0028" name="PRE_DRIVER_2"/>
	<reg32 offset="0x002C" name="LANE_MODE"/>
</domain>

</database>
