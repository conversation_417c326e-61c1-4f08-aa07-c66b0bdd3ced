<?xml version="1.0" encoding="UTF-8"?>
<database xmlns="http://nouveau.freedesktop.org/"
xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
xsi:schemaLocation="https://gitlab.freedesktop.org/freedreno/ rules-fd.xsd">
<import file="freedreno_copyright.xml"/>
<import file="adreno/adreno_common.xml"/>
<import file="adreno/adreno_pm4.xml"/>


<enum name="a2xx_rb_dither_type">
	<value name="DITHER_PIXEL" value="0"/>
	<value name="DITHER_SUBPIXEL" value="1"/>
</enum>

<enum name="a2xx_colorformatx">
	<value name="COLORX_4_4_4_4" value="0"/>
	<value name="COLORX_1_5_5_5" value="1"/>
	<value name="COLORX_5_6_5" value="2"/>
	<value name="COLORX_8" value="3"/>
	<value name="COLORX_8_8" value="4"/>
	<value name="COLORX_8_8_8_8" value="5"/>
	<value name="COLORX_S8_8_8_8" value="6"/>
	<value name="COLORX_16_FLOAT" value="7"/>
	<value name="COLORX_16_16_FLOAT" value="8"/>
	<value name="COLORX_16_16_16_16_FLOAT" value="9"/>
	<value name="COLORX_32_FLOAT" value="10"/>
	<value name="COLORX_32_32_FLOAT" value="11"/>
	<value name="COLORX_32_32_32_32_FLOAT" value="12"/>
	<value name="COLORX_2_3_3" value="13"/>
	<value name="COLORX_8_8_8" value="14"/>
</enum>

<enum name="a2xx_sq_surfaceformat">
	<value name="FMT_1_REVERSE" value="0"/>
	<value name="FMT_1" value="1"/>
	<value name="FMT_8" value="2"/>
	<value name="FMT_1_5_5_5" value="3"/>
	<value name="FMT_5_6_5" value="4"/>
	<value name="FMT_6_5_5" value="5"/>
	<value name="FMT_8_8_8_8" value="6"/>
	<value name="FMT_2_10_10_10" value="7"/>
	<value name="FMT_8_A" value="8"/>
	<value name="FMT_8_B" value="9"/>
	<value name="FMT_8_8" value="10"/>
	<value name="FMT_Cr_Y1_Cb_Y0" value="11"/>
	<value name="FMT_Y1_Cr_Y0_Cb" value="12"/>
	<value name="FMT_5_5_5_1" value="13"/>
	<value name="FMT_8_8_8_8_A" value="14"/>
	<value name="FMT_4_4_4_4" value="15"/>
	<value name="FMT_8_8_8" value="16"/>
	<value name="FMT_DXT1" value="18"/>
	<value name="FMT_DXT2_3" value="19"/>
	<value name="FMT_DXT4_5" value="20"/>
	<value name="FMT_10_10_10_2" value="21"/>
	<value name="FMT_24_8" value="22"/>
	<value name="FMT_16" value="24"/>
	<value name="FMT_16_16" value="25"/>
	<value name="FMT_16_16_16_16" value="26"/>
	<value name="FMT_16_EXPAND" value="27"/>
	<value name="FMT_16_16_EXPAND" value="28"/>
	<value name="FMT_16_16_16_16_EXPAND" value="29"/>
	<value name="FMT_16_FLOAT" value="30"/>
	<value name="FMT_16_16_FLOAT" value="31"/>
	<value name="FMT_16_16_16_16_FLOAT" value="32"/>
	<value name="FMT_32" value="33"/>
	<value name="FMT_32_32" value="34"/>
	<value name="FMT_32_32_32_32" value="35"/>
	<value name="FMT_32_FLOAT" value="36"/>
	<value name="FMT_32_32_FLOAT" value="37"/>
	<value name="FMT_32_32_32_32_FLOAT" value="38"/>
	<value name="FMT_ATI_TC_RGB" value="39"/>
	<value name="FMT_ATI_TC_RGBA" value="40"/>
	<value name="FMT_ATI_TC_555_565_RGB" value="41"/>
	<value name="FMT_ATI_TC_555_565_RGBA" value="42"/>
	<value name="FMT_ATI_TC_RGBA_INTERP" value="43"/>
	<value name="FMT_ATI_TC_555_565_RGBA_INTERP" value="44"/>
	<value name="FMT_ETC1_RGBA_INTERP" value="46"/>
	<value name="FMT_ETC1_RGB" value="47"/>
	<value name="FMT_ETC1_RGBA" value="48"/>
	<value name="FMT_DXN" value="49"/>
	<value name="FMT_2_3_3" value="51"/>
	<value name="FMT_2_10_10_10_AS_16_16_16_16" value="54"/>
	<value name="FMT_10_10_10_2_AS_16_16_16_16" value="55"/>
	<value name="FMT_32_32_32_FLOAT" value="57"/>
	<value name="FMT_DXT3A" value="58"/>
	<value name="FMT_DXT5A" value="59"/>
	<value name="FMT_CTX1" value="60"/>
</enum>

<enum name="a2xx_sq_ps_vtx_mode">
	<value name="POSITION_1_VECTOR" value="0"/>
	<value name="POSITION_2_VECTORS_UNUSED" value="1"/>
	<value name="POSITION_2_VECTORS_SPRITE" value="2"/>
	<value name="POSITION_2_VECTORS_EDGE" value="3"/>
	<value name="POSITION_2_VECTORS_KILL" value="4"/>
	<value name="POSITION_2_VECTORS_SPRITE_KILL" value="5"/>
	<value name="POSITION_2_VECTORS_EDGE_KILL" value="6"/>
	<value name="MULTIPASS" value="7"/>
</enum>

<enum name="a2xx_sq_sample_cntl">
	<value name="CENTROIDS_ONLY" value="0"/>
	<value name="CENTERS_ONLY" value="1"/>
	<value name="CENTROIDS_AND_CENTERS" value="2"/>
</enum>

<enum name="a2xx_dx_clip_space">
	<value name="DXCLIP_OPENGL" value="0"/>
	<value name="DXCLIP_DIRECTX" value="1"/>
</enum>

<enum name="a2xx_pa_su_sc_polymode">
	<value name="POLY_DISABLED" value="0"/>
	<value name="POLY_DUALMODE" value="1"/>
</enum>

<enum name="a2xx_rb_edram_mode">
	<value name="EDRAM_NOP" value="0"/>
	<value name="COLOR_DEPTH" value="4"/>
	<value name="DEPTH_ONLY" value="5"/>
	<value name="EDRAM_COPY" value="6"/>
</enum>

<enum name="a2xx_pa_sc_pattern_bit_order">
	<value name="LITTLE" value="0"/>
	<value name="BIG" value="1"/>
</enum>

<enum name="a2xx_pa_sc_auto_reset_cntl">
	<value name="NEVER" value="0"/>
	<value name="EACH_PRIMITIVE" value="1"/>
	<value name="EACH_PACKET" value="2"/>
</enum>

<enum name="a2xx_pa_pixcenter">
	<value name="PIXCENTER_D3D" value="0"/>
	<value name="PIXCENTER_OGL" value="1"/>
</enum>

<enum name="a2xx_pa_roundmode">
	<value name="TRUNCATE" value="0"/>
	<value name="ROUND" value="1"/>
	<value name="ROUNDTOEVEN" value="2"/>
	<value name="ROUNDTOODD" value="3"/>
</enum>

<enum name="a2xx_pa_quantmode">
	<value name="ONE_SIXTEENTH" value="0"/>
	<value name="ONE_EIGTH" value="1"/>
	<value name="ONE_QUARTER" value="2"/>
	<value name="ONE_HALF" value="3"/>
	<value name="ONE" value="4"/>
</enum>

<enum name="a2xx_rb_copy_sample_select">
	<value name="SAMPLE_0" value="0"/>
	<value name="SAMPLE_1" value="1"/>
	<value name="SAMPLE_2" value="2"/>
	<value name="SAMPLE_3" value="3"/>
	<value name="SAMPLE_01" value="4"/>
	<value name="SAMPLE_23" value="5"/>
	<value name="SAMPLE_0123" value="6"/>
</enum>

<enum name="a2xx_rb_blend_opcode">
	<value name="BLEND2_DST_PLUS_SRC" value="0"/>
	<value name="BLEND2_SRC_MINUS_DST" value="1"/>
	<value name="BLEND2_MIN_DST_SRC" value="2"/>
	<value name="BLEND2_MAX_DST_SRC" value="3"/>
	<value name="BLEND2_DST_MINUS_SRC" value="4"/>
	<value name="BLEND2_DST_PLUS_SRC_BIAS" value="5"/>
</enum>

<enum name="a2xx_su_perfcnt_select">
	<value value="0" name="PERF_PAPC_PASX_REQ"/>
	<value value="2" name="PERF_PAPC_PASX_FIRST_VECTOR"/>
	<value value="3" name="PERF_PAPC_PASX_SECOND_VECTOR"/>
	<value value="4" name="PERF_PAPC_PASX_FIRST_DEAD"/>
	<value value="5" name="PERF_PAPC_PASX_SECOND_DEAD"/>
	<value value="6" name="PERF_PAPC_PASX_VTX_KILL_DISCARD"/>
	<value value="7" name="PERF_PAPC_PASX_VTX_NAN_DISCARD"/>
	<value value="8" name="PERF_PAPC_PA_INPUT_PRIM"/>
	<value value="9" name="PERF_PAPC_PA_INPUT_NULL_PRIM"/>
	<value value="10" name="PERF_PAPC_PA_INPUT_EVENT_FLAG"/>
	<value value="11" name="PERF_PAPC_PA_INPUT_FIRST_PRIM_SLOT"/>
	<value value="12" name="PERF_PAPC_PA_INPUT_END_OF_PACKET"/>
	<value value="13" name="PERF_PAPC_CLPR_CULL_PRIM"/>
	<value value="15" name="PERF_PAPC_CLPR_VV_CULL_PRIM"/>
	<value value="17" name="PERF_PAPC_CLPR_VTX_KILL_CULL_PRIM"/>
	<value value="18" name="PERF_PAPC_CLPR_VTX_NAN_CULL_PRIM"/>
	<value value="19" name="PERF_PAPC_CLPR_CULL_TO_NULL_PRIM"/>
	<value value="21" name="PERF_PAPC_CLPR_VV_CLIP_PRIM"/>
	<value value="23" name="PERF_PAPC_CLPR_POINT_CLIP_CANDIDATE"/>
	<value value="24" name="PERF_PAPC_CLPR_CLIP_PLANE_CNT_1"/>
	<value value="25" name="PERF_PAPC_CLPR_CLIP_PLANE_CNT_2"/>
	<value value="26" name="PERF_PAPC_CLPR_CLIP_PLANE_CNT_3"/>
	<value value="27" name="PERF_PAPC_CLPR_CLIP_PLANE_CNT_4"/>
	<value value="28" name="PERF_PAPC_CLPR_CLIP_PLANE_CNT_5"/>
	<value value="29" name="PERF_PAPC_CLPR_CLIP_PLANE_CNT_6"/>
	<value value="30" name="PERF_PAPC_CLPR_CLIP_PLANE_NEAR"/>
	<value value="31" name="PERF_PAPC_CLPR_CLIP_PLANE_FAR"/>
	<value value="32" name="PERF_PAPC_CLPR_CLIP_PLANE_LEFT"/>
	<value value="33" name="PERF_PAPC_CLPR_CLIP_PLANE_RIGHT"/>
	<value value="34" name="PERF_PAPC_CLPR_CLIP_PLANE_TOP"/>
	<value value="35" name="PERF_PAPC_CLPR_CLIP_PLANE_BOTTOM"/>
	<value value="36" name="PERF_PAPC_CLSM_NULL_PRIM"/>
	<value value="37" name="PERF_PAPC_CLSM_TOTALLY_VISIBLE_PRIM"/>
	<value value="38" name="PERF_PAPC_CLSM_CLIP_PRIM"/>
	<value value="39" name="PERF_PAPC_CLSM_CULL_TO_NULL_PRIM"/>
	<value value="40" name="PERF_PAPC_CLSM_OUT_PRIM_CNT_1"/>
	<value value="41" name="PERF_PAPC_CLSM_OUT_PRIM_CNT_2"/>
	<value value="42" name="PERF_PAPC_CLSM_OUT_PRIM_CNT_3"/>
	<value value="43" name="PERF_PAPC_CLSM_OUT_PRIM_CNT_4"/>
	<value value="44" name="PERF_PAPC_CLSM_OUT_PRIM_CNT_5"/>
	<value value="45" name="PERF_PAPC_CLSM_OUT_PRIM_CNT_6_7"/>
	<value value="46" name="PERF_PAPC_CLSM_NON_TRIVIAL_CULL"/>
	<value value="47" name="PERF_PAPC_SU_INPUT_PRIM"/>
	<value value="48" name="PERF_PAPC_SU_INPUT_CLIP_PRIM"/>
	<value value="49" name="PERF_PAPC_SU_INPUT_NULL_PRIM"/>
	<value value="50" name="PERF_PAPC_SU_ZERO_AREA_CULL_PRIM"/>
	<value value="51" name="PERF_PAPC_SU_BACK_FACE_CULL_PRIM"/>
	<value value="52" name="PERF_PAPC_SU_FRONT_FACE_CULL_PRIM"/>
	<value value="53" name="PERF_PAPC_SU_POLYMODE_FACE_CULL"/>
	<value value="54" name="PERF_PAPC_SU_POLYMODE_BACK_CULL"/>
	<value value="55" name="PERF_PAPC_SU_POLYMODE_FRONT_CULL"/>
	<value value="56" name="PERF_PAPC_SU_POLYMODE_INVALID_FILL"/>
	<value value="57" name="PERF_PAPC_SU_OUTPUT_PRIM"/>
	<value value="58" name="PERF_PAPC_SU_OUTPUT_CLIP_PRIM"/>
	<value value="59" name="PERF_PAPC_SU_OUTPUT_NULL_PRIM"/>
	<value value="60" name="PERF_PAPC_SU_OUTPUT_EVENT_FLAG"/>
	<value value="61" name="PERF_PAPC_SU_OUTPUT_FIRST_PRIM_SLOT"/>
	<value value="62" name="PERF_PAPC_SU_OUTPUT_END_OF_PACKET"/>
	<value value="63" name="PERF_PAPC_SU_OUTPUT_POLYMODE_FACE"/>
	<value value="64" name="PERF_PAPC_SU_OUTPUT_POLYMODE_BACK"/>
	<value value="65" name="PERF_PAPC_SU_OUTPUT_POLYMODE_FRONT"/>
	<value value="66" name="PERF_PAPC_SU_OUT_CLIP_POLYMODE_FACE"/>
	<value value="67" name="PERF_PAPC_SU_OUT_CLIP_POLYMODE_BACK"/>
	<value value="68" name="PERF_PAPC_SU_OUT_CLIP_POLYMODE_FRONT"/>
	<value value="69" name="PERF_PAPC_PASX_REQ_IDLE"/>
	<value value="70" name="PERF_PAPC_PASX_REQ_BUSY"/>
	<value value="71" name="PERF_PAPC_PASX_REQ_STALLED"/>
	<value value="72" name="PERF_PAPC_PASX_REC_IDLE"/>
	<value value="73" name="PERF_PAPC_PASX_REC_BUSY"/>
	<value value="74" name="PERF_PAPC_PASX_REC_STARVED_SX"/>
	<value value="75" name="PERF_PAPC_PASX_REC_STALLED"/>
	<value value="76" name="PERF_PAPC_PASX_REC_STALLED_POS_MEM"/>
	<value value="77" name="PERF_PAPC_PASX_REC_STALLED_CCGSM_IN"/>
	<value value="78" name="PERF_PAPC_CCGSM_IDLE"/>
	<value value="79" name="PERF_PAPC_CCGSM_BUSY"/>
	<value value="80" name="PERF_PAPC_CCGSM_STALLED"/>
	<value value="81" name="PERF_PAPC_CLPRIM_IDLE"/>
	<value value="82" name="PERF_PAPC_CLPRIM_BUSY"/>
	<value value="83" name="PERF_PAPC_CLPRIM_STALLED"/>
	<value value="84" name="PERF_PAPC_CLPRIM_STARVED_CCGSM"/>
	<value value="85" name="PERF_PAPC_CLIPSM_IDLE"/>
	<value value="86" name="PERF_PAPC_CLIPSM_BUSY"/>
	<value value="87" name="PERF_PAPC_CLIPSM_WAIT_CLIP_VERT_ENGH"/>
	<value value="88" name="PERF_PAPC_CLIPSM_WAIT_HIGH_PRI_SEQ"/>
	<value value="89" name="PERF_PAPC_CLIPSM_WAIT_CLIPGA"/>
	<value value="90" name="PERF_PAPC_CLIPSM_WAIT_AVAIL_VTE_CLIP"/>
	<value value="91" name="PERF_PAPC_CLIPSM_WAIT_CLIP_OUTSM"/>
	<value value="92" name="PERF_PAPC_CLIPGA_IDLE"/>
	<value value="93" name="PERF_PAPC_CLIPGA_BUSY"/>
	<value value="94" name="PERF_PAPC_CLIPGA_STARVED_VTE_CLIP"/>
	<value value="95" name="PERF_PAPC_CLIPGA_STALLED"/>
	<value value="96" name="PERF_PAPC_CLIP_IDLE"/>
	<value value="97" name="PERF_PAPC_CLIP_BUSY"/>
	<value value="98" name="PERF_PAPC_SU_IDLE"/>
	<value value="99" name="PERF_PAPC_SU_BUSY"/>
	<value value="100" name="PERF_PAPC_SU_STARVED_CLIP"/>
	<value value="101" name="PERF_PAPC_SU_STALLED_SC"/>
	<value value="102" name="PERF_PAPC_SU_FACENESS_CULL"/>
</enum>

<enum name="a2xx_sc_perfcnt_select">
	<value value="0" name="SC_SR_WINDOW_VALID"/>
	<value value="1" name="SC_CW_WINDOW_VALID"/>
	<value value="2" name="SC_QM_WINDOW_VALID"/>
	<value value="3" name="SC_FW_WINDOW_VALID"/>
	<value value="4" name="SC_EZ_WINDOW_VALID"/>
	<value value="5" name="SC_IT_WINDOW_VALID"/>
	<value value="6" name="SC_STARVED_BY_PA"/>
	<value value="7" name="SC_STALLED_BY_RB_TILE"/>
	<value value="8" name="SC_STALLED_BY_RB_SAMP"/>
	<value value="9" name="SC_STARVED_BY_RB_EZ"/>
	<value value="10" name="SC_STALLED_BY_SAMPLE_FF"/>
	<value value="11" name="SC_STALLED_BY_SQ"/>
	<value value="12" name="SC_STALLED_BY_SP"/>
	<value value="13" name="SC_TOTAL_NO_PRIMS"/>
	<value value="14" name="SC_NON_EMPTY_PRIMS"/>
	<value value="15" name="SC_NO_TILES_PASSING_QM"/>
	<value value="16" name="SC_NO_PIXELS_PRE_EZ"/>
	<value value="17" name="SC_NO_PIXELS_POST_EZ"/>
</enum>

<enum name="a2xx_vgt_perfcount_select">
	<value value="0" name="VGT_SQ_EVENT_WINDOW_ACTIVE"/>
	<value value="1" name="VGT_SQ_SEND"/>
	<value value="2" name="VGT_SQ_STALLED"/>
	<value value="3" name="VGT_SQ_STARVED_BUSY"/>
	<value value="4" name="VGT_SQ_STARVED_IDLE"/>
	<value value="5" name="VGT_SQ_STATIC"/>
	<value value="6" name="VGT_PA_EVENT_WINDOW_ACTIVE"/>
	<value value="7" name="VGT_PA_CLIP_V_SEND"/>
	<value value="8" name="VGT_PA_CLIP_V_STALLED"/>
	<value value="9" name="VGT_PA_CLIP_V_STARVED_BUSY"/>
	<value value="10" name="VGT_PA_CLIP_V_STARVED_IDLE"/>
	<value value="11" name="VGT_PA_CLIP_V_STATIC"/>
	<value value="12" name="VGT_PA_CLIP_P_SEND"/>
	<value value="13" name="VGT_PA_CLIP_P_STALLED"/>
	<value value="14" name="VGT_PA_CLIP_P_STARVED_BUSY"/>
	<value value="15" name="VGT_PA_CLIP_P_STARVED_IDLE"/>
	<value value="16" name="VGT_PA_CLIP_P_STATIC"/>
	<value value="17" name="VGT_PA_CLIP_S_SEND"/>
	<value value="18" name="VGT_PA_CLIP_S_STALLED"/>
	<value value="19" name="VGT_PA_CLIP_S_STARVED_BUSY"/>
	<value value="20" name="VGT_PA_CLIP_S_STARVED_IDLE"/>
	<value value="21" name="VGT_PA_CLIP_S_STATIC"/>
	<value value="22" name="RBIU_FIFOS_EVENT_WINDOW_ACTIVE"/>
	<value value="23" name="RBIU_IMMED_DATA_FIFO_STARVED"/>
	<value value="24" name="RBIU_IMMED_DATA_FIFO_STALLED"/>
	<value value="25" name="RBIU_DMA_REQUEST_FIFO_STARVED"/>
	<value value="26" name="RBIU_DMA_REQUEST_FIFO_STALLED"/>
	<value value="27" name="RBIU_DRAW_INITIATOR_FIFO_STARVED"/>
	<value value="28" name="RBIU_DRAW_INITIATOR_FIFO_STALLED"/>
	<value value="29" name="BIN_PRIM_NEAR_CULL"/>
	<value value="30" name="BIN_PRIM_ZERO_CULL"/>
	<value value="31" name="BIN_PRIM_FAR_CULL"/>
	<value value="32" name="BIN_PRIM_BIN_CULL"/>
	<value value="33" name="BIN_PRIM_FACE_CULL"/>
	<value value="34" name="SPARE34"/>
	<value value="35" name="SPARE35"/>
	<value value="36" name="SPARE36"/>
	<value value="37" name="SPARE37"/>
	<value value="38" name="SPARE38"/>
	<value value="39" name="SPARE39"/>
	<value value="40" name="TE_SU_IN_VALID"/>
	<value value="41" name="TE_SU_IN_READ"/>
	<value value="42" name="TE_SU_IN_PRIM"/>
	<value value="43" name="TE_SU_IN_EOP"/>
	<value value="44" name="TE_SU_IN_NULL_PRIM"/>
	<value value="45" name="TE_WK_IN_VALID"/>
	<value value="46" name="TE_WK_IN_READ"/>
	<value value="47" name="TE_OUT_PRIM_VALID"/>
	<value value="48" name="TE_OUT_PRIM_READ"/>
</enum>

<enum name="a2xx_tcr_perfcount_select">
	<value value="0" name="DGMMPD_IPMUX0_STALL"/>
	<value value="4" name="DGMMPD_IPMUX_ALL_STALL"/>
	<value value="5" name="OPMUX0_L2_WRITES"/>
</enum>

<enum name="a2xx_tp_perfcount_select">
	<value value="0" name="POINT_QUADS"/>
	<value value="1" name="BILIN_QUADS"/>
	<value value="2" name="ANISO_QUADS"/>
	<value value="3" name="MIP_QUADS"/>
	<value value="4" name="VOL_QUADS"/>
	<value value="5" name="MIP_VOL_QUADS"/>
	<value value="6" name="MIP_ANISO_QUADS"/>
	<value value="7" name="VOL_ANISO_QUADS"/>
	<value value="8" name="ANISO_2_1_QUADS"/>
	<value value="9" name="ANISO_4_1_QUADS"/>
	<value value="10" name="ANISO_6_1_QUADS"/>
	<value value="11" name="ANISO_8_1_QUADS"/>
	<value value="12" name="ANISO_10_1_QUADS"/>
	<value value="13" name="ANISO_12_1_QUADS"/>
	<value value="14" name="ANISO_14_1_QUADS"/>
	<value value="15" name="ANISO_16_1_QUADS"/>
	<value value="16" name="MIP_VOL_ANISO_QUADS"/>
	<value value="17" name="ALIGN_2_QUADS"/>
	<value value="18" name="ALIGN_4_QUADS"/>
	<value value="19" name="PIX_0_QUAD"/>
	<value value="20" name="PIX_1_QUAD"/>
	<value value="21" name="PIX_2_QUAD"/>
	<value value="22" name="PIX_3_QUAD"/>
	<value value="23" name="PIX_4_QUAD"/>
	<value value="24" name="TP_MIPMAP_LOD0"/>
	<value value="25" name="TP_MIPMAP_LOD1"/>
	<value value="26" name="TP_MIPMAP_LOD2"/>
	<value value="27" name="TP_MIPMAP_LOD3"/>
	<value value="28" name="TP_MIPMAP_LOD4"/>
	<value value="29" name="TP_MIPMAP_LOD5"/>
	<value value="30" name="TP_MIPMAP_LOD6"/>
	<value value="31" name="TP_MIPMAP_LOD7"/>
	<value value="32" name="TP_MIPMAP_LOD8"/>
	<value value="33" name="TP_MIPMAP_LOD9"/>
	<value value="34" name="TP_MIPMAP_LOD10"/>
	<value value="35" name="TP_MIPMAP_LOD11"/>
	<value value="36" name="TP_MIPMAP_LOD12"/>
	<value value="37" name="TP_MIPMAP_LOD13"/>
	<value value="38" name="TP_MIPMAP_LOD14"/>
</enum>

<enum name="a2xx_tcm_perfcount_select">
	<value value="0" name="QUAD0_RD_LAT_FIFO_EMPTY"/>
	<value value="3" name="QUAD0_RD_LAT_FIFO_4TH_FULL"/>
	<value value="4" name="QUAD0_RD_LAT_FIFO_HALF_FULL"/>
	<value value="5" name="QUAD0_RD_LAT_FIFO_FULL"/>
	<value value="6" name="QUAD0_RD_LAT_FIFO_LT_4TH_FULL"/>
	<value value="28" name="READ_STARVED_QUAD0"/>
	<value value="32" name="READ_STARVED"/>
	<value value="33" name="READ_STALLED_QUAD0"/>
	<value value="37" name="READ_STALLED"/>
	<value value="38" name="VALID_READ_QUAD0"/>
	<value value="42" name="TC_TP_STARVED_QUAD0"/>
	<value value="46" name="TC_TP_STARVED"/>
</enum>

<enum name="a2xx_tcf_perfcount_select">
	<value value="0" name="VALID_CYCLES"/>
	<value value="1" name="SINGLE_PHASES"/>
	<value value="2" name="ANISO_PHASES"/>
	<value value="3" name="MIP_PHASES"/>
	<value value="4" name="VOL_PHASES"/>
	<value value="5" name="MIP_VOL_PHASES"/>
	<value value="6" name="MIP_ANISO_PHASES"/>
	<value value="7" name="VOL_ANISO_PHASES"/>
	<value value="8" name="ANISO_2_1_PHASES"/>
	<value value="9" name="ANISO_4_1_PHASES"/>
	<value value="10" name="ANISO_6_1_PHASES"/>
	<value value="11" name="ANISO_8_1_PHASES"/>
	<value value="12" name="ANISO_10_1_PHASES"/>
	<value value="13" name="ANISO_12_1_PHASES"/>
	<value value="14" name="ANISO_14_1_PHASES"/>
	<value value="15" name="ANISO_16_1_PHASES"/>
	<value value="16" name="MIP_VOL_ANISO_PHASES"/>
	<value value="17" name="ALIGN_2_PHASES"/>
	<value value="18" name="ALIGN_4_PHASES"/>
	<value value="19" name="TPC_BUSY"/>
	<value value="20" name="TPC_STALLED"/>
	<value value="21" name="TPC_STARVED"/>
	<value value="22" name="TPC_WORKING"/>
	<value value="23" name="TPC_WALKER_BUSY"/>
	<value value="24" name="TPC_WALKER_STALLED"/>
	<value value="25" name="TPC_WALKER_WORKING"/>
	<value value="26" name="TPC_ALIGNER_BUSY"/>
	<value value="27" name="TPC_ALIGNER_STALLED"/>
	<value value="28" name="TPC_ALIGNER_STALLED_BY_BLEND"/>
	<value value="29" name="TPC_ALIGNER_STALLED_BY_CACHE"/>
	<value value="30" name="TPC_ALIGNER_WORKING"/>
	<value value="31" name="TPC_BLEND_BUSY"/>
	<value value="32" name="TPC_BLEND_SYNC"/>
	<value value="33" name="TPC_BLEND_STARVED"/>
	<value value="34" name="TPC_BLEND_WORKING"/>
	<value value="35" name="OPCODE_0x00"/>
	<value value="36" name="OPCODE_0x01"/>
	<value value="37" name="OPCODE_0x04"/>
	<value value="38" name="OPCODE_0x10"/>
	<value value="39" name="OPCODE_0x11"/>
	<value value="40" name="OPCODE_0x12"/>
	<value value="41" name="OPCODE_0x13"/>
	<value value="42" name="OPCODE_0x18"/>
	<value value="43" name="OPCODE_0x19"/>
	<value value="44" name="OPCODE_0x1A"/>
	<value value="45" name="OPCODE_OTHER"/>
	<value value="56" name="IN_FIFO_0_EMPTY"/>
	<value value="57" name="IN_FIFO_0_LT_HALF_FULL"/>
	<value value="58" name="IN_FIFO_0_HALF_FULL"/>
	<value value="59" name="IN_FIFO_0_FULL"/>
	<value value="72" name="IN_FIFO_TPC_EMPTY"/>
	<value value="73" name="IN_FIFO_TPC_LT_HALF_FULL"/>
	<value value="74" name="IN_FIFO_TPC_HALF_FULL"/>
	<value value="75" name="IN_FIFO_TPC_FULL"/>
	<value value="76" name="TPC_TC_XFC"/>
	<value value="77" name="TPC_TC_STATE"/>
	<value value="78" name="TC_STALL"/>
	<value value="79" name="QUAD0_TAPS"/>
	<value value="83" name="QUADS"/>
	<value value="84" name="TCA_SYNC_STALL"/>
	<value value="85" name="TAG_STALL"/>
	<value value="88" name="TCB_SYNC_STALL"/>
	<value value="89" name="TCA_VALID"/>
	<value value="90" name="PROBES_VALID"/>
	<value value="91" name="MISS_STALL"/>
	<value value="92" name="FETCH_FIFO_STALL"/>
	<value value="93" name="TCO_STALL"/>
	<value value="94" name="ANY_STALL"/>
	<value value="95" name="TAG_MISSES"/>
	<value value="96" name="TAG_HITS"/>
	<value value="97" name="SUB_TAG_MISSES"/>
	<value value="98" name="SET0_INVALIDATES"/>
	<value value="99" name="SET1_INVALIDATES"/>
	<value value="100" name="SET2_INVALIDATES"/>
	<value value="101" name="SET3_INVALIDATES"/>
	<value value="102" name="SET0_TAG_MISSES"/>
	<value value="103" name="SET1_TAG_MISSES"/>
	<value value="104" name="SET2_TAG_MISSES"/>
	<value value="105" name="SET3_TAG_MISSES"/>
	<value value="106" name="SET0_TAG_HITS"/>
	<value value="107" name="SET1_TAG_HITS"/>
	<value value="108" name="SET2_TAG_HITS"/>
	<value value="109" name="SET3_TAG_HITS"/>
	<value value="110" name="SET0_SUB_TAG_MISSES"/>
	<value value="111" name="SET1_SUB_TAG_MISSES"/>
	<value value="112" name="SET2_SUB_TAG_MISSES"/>
	<value value="113" name="SET3_SUB_TAG_MISSES"/>
	<value value="114" name="SET0_EVICT1"/>
	<value value="115" name="SET0_EVICT2"/>
	<value value="116" name="SET0_EVICT3"/>
	<value value="117" name="SET0_EVICT4"/>
	<value value="118" name="SET0_EVICT5"/>
	<value value="119" name="SET0_EVICT6"/>
	<value value="120" name="SET0_EVICT7"/>
	<value value="121" name="SET0_EVICT8"/>
	<value value="130" name="SET1_EVICT1"/>
	<value value="131" name="SET1_EVICT2"/>
	<value value="132" name="SET1_EVICT3"/>
	<value value="133" name="SET1_EVICT4"/>
	<value value="134" name="SET1_EVICT5"/>
	<value value="135" name="SET1_EVICT6"/>
	<value value="136" name="SET1_EVICT7"/>
	<value value="137" name="SET1_EVICT8"/>
	<value value="146" name="SET2_EVICT1"/>
	<value value="147" name="SET2_EVICT2"/>
	<value value="148" name="SET2_EVICT3"/>
	<value value="149" name="SET2_EVICT4"/>
	<value value="150" name="SET2_EVICT5"/>
	<value value="151" name="SET2_EVICT6"/>
	<value value="152" name="SET2_EVICT7"/>
	<value value="153" name="SET2_EVICT8"/>
	<value value="162" name="SET3_EVICT1"/>
	<value value="163" name="SET3_EVICT2"/>
	<value value="164" name="SET3_EVICT3"/>
	<value value="165" name="SET3_EVICT4"/>
	<value value="166" name="SET3_EVICT5"/>
	<value value="167" name="SET3_EVICT6"/>
	<value value="168" name="SET3_EVICT7"/>
	<value value="169" name="SET3_EVICT8"/>
	<value value="178" name="FF_EMPTY"/>
	<value value="179" name="FF_LT_HALF_FULL"/>
	<value value="180" name="FF_HALF_FULL"/>
	<value value="181" name="FF_FULL"/>
	<value value="182" name="FF_XFC"/>
	<value value="183" name="FF_STALLED"/>
	<value value="184" name="FG_MASKS"/>
	<value value="185" name="FG_LEFT_MASKS"/>
	<value value="186" name="FG_LEFT_MASK_STALLED"/>
	<value value="187" name="FG_LEFT_NOT_DONE_STALL"/>
	<value value="188" name="FG_LEFT_FG_STALL"/>
	<value value="189" name="FG_LEFT_SECTORS"/>
	<value value="195" name="FG0_REQUESTS"/>
	<value value="196" name="FG0_STALLED"/>
	<value value="199" name="MEM_REQ512"/>
	<value value="200" name="MEM_REQ_SENT"/>
	<value value="202" name="MEM_LOCAL_READ_REQ"/>
	<value value="203" name="TC0_MH_STALLED"/>
</enum>

<enum name="a2xx_sq_perfcnt_select">
	<value value="0" name="SQ_PIXEL_VECTORS_SUB"/>
	<value value="1" name="SQ_VERTEX_VECTORS_SUB"/>
	<value value="2" name="SQ_ALU0_ACTIVE_VTX_SIMD0"/>
	<value value="3" name="SQ_ALU1_ACTIVE_VTX_SIMD0"/>
	<value value="4" name="SQ_ALU0_ACTIVE_PIX_SIMD0"/>
	<value value="5" name="SQ_ALU1_ACTIVE_PIX_SIMD0"/>
	<value value="6" name="SQ_ALU0_ACTIVE_VTX_SIMD1"/>
	<value value="7" name="SQ_ALU1_ACTIVE_VTX_SIMD1"/>
	<value value="8" name="SQ_ALU0_ACTIVE_PIX_SIMD1"/>
	<value value="9" name="SQ_ALU1_ACTIVE_PIX_SIMD1"/>
	<value value="10" name="SQ_EXPORT_CYCLES"/>
	<value value="11" name="SQ_ALU_CST_WRITTEN"/>
	<value value="12" name="SQ_TEX_CST_WRITTEN"/>
	<value value="13" name="SQ_ALU_CST_STALL"/>
	<value value="14" name="SQ_ALU_TEX_STALL"/>
	<value value="15" name="SQ_INST_WRITTEN"/>
	<value value="16" name="SQ_BOOLEAN_WRITTEN"/>
	<value value="17" name="SQ_LOOPS_WRITTEN"/>
	<value value="18" name="SQ_PIXEL_SWAP_IN"/>
	<value value="19" name="SQ_PIXEL_SWAP_OUT"/>
	<value value="20" name="SQ_VERTEX_SWAP_IN"/>
	<value value="21" name="SQ_VERTEX_SWAP_OUT"/>
	<value value="22" name="SQ_ALU_VTX_INST_ISSUED"/>
	<value value="23" name="SQ_TEX_VTX_INST_ISSUED"/>
	<value value="24" name="SQ_VC_VTX_INST_ISSUED"/>
	<value value="25" name="SQ_CF_VTX_INST_ISSUED"/>
	<value value="26" name="SQ_ALU_PIX_INST_ISSUED"/>
	<value value="27" name="SQ_TEX_PIX_INST_ISSUED"/>
	<value value="28" name="SQ_VC_PIX_INST_ISSUED"/>
	<value value="29" name="SQ_CF_PIX_INST_ISSUED"/>
	<value value="30" name="SQ_ALU0_FIFO_EMPTY_SIMD0"/>
	<value value="31" name="SQ_ALU1_FIFO_EMPTY_SIMD0"/>
	<value value="32" name="SQ_ALU0_FIFO_EMPTY_SIMD1"/>
	<value value="33" name="SQ_ALU1_FIFO_EMPTY_SIMD1"/>
	<value value="34" name="SQ_ALU_NOPS"/>
	<value value="35" name="SQ_PRED_SKIP"/>
	<value value="36" name="SQ_SYNC_ALU_STALL_SIMD0_VTX"/>
	<value value="37" name="SQ_SYNC_ALU_STALL_SIMD1_VTX"/>
	<value value="38" name="SQ_SYNC_TEX_STALL_VTX"/>
	<value value="39" name="SQ_SYNC_VC_STALL_VTX"/>
	<value value="40" name="SQ_CONSTANTS_USED_SIMD0"/>
	<value value="41" name="SQ_CONSTANTS_SENT_SP_SIMD0"/>
	<value value="42" name="SQ_GPR_STALL_VTX"/>
	<value value="43" name="SQ_GPR_STALL_PIX"/>
	<value value="44" name="SQ_VTX_RS_STALL"/>
	<value value="45" name="SQ_PIX_RS_STALL"/>
	<value value="46" name="SQ_SX_PC_FULL"/>
	<value value="47" name="SQ_SX_EXP_BUFF_FULL"/>
	<value value="48" name="SQ_SX_POS_BUFF_FULL"/>
	<value value="49" name="SQ_INTERP_QUADS"/>
	<value value="50" name="SQ_INTERP_ACTIVE"/>
	<value value="51" name="SQ_IN_PIXEL_STALL"/>
	<value value="52" name="SQ_IN_VTX_STALL"/>
	<value value="53" name="SQ_VTX_CNT"/>
	<value value="54" name="SQ_VTX_VECTOR2"/>
	<value value="55" name="SQ_VTX_VECTOR3"/>
	<value value="56" name="SQ_VTX_VECTOR4"/>
	<value value="57" name="SQ_PIXEL_VECTOR1"/>
	<value value="58" name="SQ_PIXEL_VECTOR23"/>
	<value value="59" name="SQ_PIXEL_VECTOR4"/>
	<value value="60" name="SQ_CONSTANTS_USED_SIMD1"/>
	<value value="61" name="SQ_CONSTANTS_SENT_SP_SIMD1"/>
	<value value="62" name="SQ_SX_MEM_EXP_FULL"/>
	<value value="63" name="SQ_ALU0_ACTIVE_VTX_SIMD2"/>
	<value value="64" name="SQ_ALU1_ACTIVE_VTX_SIMD2"/>
	<value value="65" name="SQ_ALU0_ACTIVE_PIX_SIMD2"/>
	<value value="66" name="SQ_ALU1_ACTIVE_PIX_SIMD2"/>
	<value value="67" name="SQ_ALU0_ACTIVE_VTX_SIMD3"/>
	<value value="68" name="SQ_PERFCOUNT_VTX_QUAL_TP_DONE"/>
	<value value="69" name="SQ_ALU0_ACTIVE_PIX_SIMD3"/>
	<value value="70" name="SQ_PERFCOUNT_PIX_QUAL_TP_DONE"/>
	<value value="71" name="SQ_ALU0_FIFO_EMPTY_SIMD2"/>
	<value value="72" name="SQ_ALU1_FIFO_EMPTY_SIMD2"/>
	<value value="73" name="SQ_ALU0_FIFO_EMPTY_SIMD3"/>
	<value value="74" name="SQ_ALU1_FIFO_EMPTY_SIMD3"/>
	<value value="75" name="SQ_SYNC_ALU_STALL_SIMD2_VTX"/>
	<value value="76" name="SQ_PERFCOUNT_VTX_POP_THREAD"/>
	<value value="77" name="SQ_SYNC_ALU_STALL_SIMD0_PIX"/>
	<value value="78" name="SQ_SYNC_ALU_STALL_SIMD1_PIX"/>
	<value value="79" name="SQ_SYNC_ALU_STALL_SIMD2_PIX"/>
	<value value="80" name="SQ_PERFCOUNT_PIX_POP_THREAD"/>
	<value value="81" name="SQ_SYNC_TEX_STALL_PIX"/>
	<value value="82" name="SQ_SYNC_VC_STALL_PIX"/>
	<value value="83" name="SQ_CONSTANTS_USED_SIMD2"/>
	<value value="84" name="SQ_CONSTANTS_SENT_SP_SIMD2"/>
	<value value="85" name="SQ_PERFCOUNT_VTX_DEALLOC_ACK"/>
	<value value="86" name="SQ_PERFCOUNT_PIX_DEALLOC_ACK"/>
	<value value="87" name="SQ_ALU0_FIFO_FULL_SIMD0"/>
	<value value="88" name="SQ_ALU1_FIFO_FULL_SIMD0"/>
	<value value="89" name="SQ_ALU0_FIFO_FULL_SIMD1"/>
	<value value="90" name="SQ_ALU1_FIFO_FULL_SIMD1"/>
	<value value="91" name="SQ_ALU0_FIFO_FULL_SIMD2"/>
	<value value="92" name="SQ_ALU1_FIFO_FULL_SIMD2"/>
	<value value="93" name="SQ_ALU0_FIFO_FULL_SIMD3"/>
	<value value="94" name="SQ_ALU1_FIFO_FULL_SIMD3"/>
	<value value="95" name="VC_PERF_STATIC"/>
	<value value="96" name="VC_PERF_STALLED"/>
	<value value="97" name="VC_PERF_STARVED"/>
	<value value="98" name="VC_PERF_SEND"/>
	<value value="99" name="VC_PERF_ACTUAL_STARVED"/>
	<value value="100" name="PIXEL_THREAD_0_ACTIVE"/>
	<value value="101" name="VERTEX_THREAD_0_ACTIVE"/>
	<value value="102" name="PIXEL_THREAD_0_NUMBER"/>
	<value value="103" name="VERTEX_THREAD_0_NUMBER"/>
	<value value="104" name="VERTEX_EVENT_NUMBER"/>
	<value value="105" name="PIXEL_EVENT_NUMBER"/>
	<value value="106" name="PTRBUFF_EF_PUSH"/>
	<value value="107" name="PTRBUFF_EF_POP_EVENT"/>
	<value value="108" name="PTRBUFF_EF_POP_NEW_VTX"/>
	<value value="109" name="PTRBUFF_EF_POP_DEALLOC"/>
	<value value="110" name="PTRBUFF_EF_POP_PVECTOR"/>
	<value value="111" name="PTRBUFF_EF_POP_PVECTOR_X"/>
	<value value="112" name="PTRBUFF_EF_POP_PVECTOR_VNZ"/>
	<value value="113" name="PTRBUFF_PB_DEALLOC"/>
	<value value="114" name="PTRBUFF_PI_STATE_PPB_POP"/>
	<value value="115" name="PTRBUFF_PI_RTR"/>
	<value value="116" name="PTRBUFF_PI_READ_EN"/>
	<value value="117" name="PTRBUFF_PI_BUFF_SWAP"/>
	<value value="118" name="PTRBUFF_SQ_FREE_BUFF"/>
	<value value="119" name="PTRBUFF_SQ_DEC"/>
	<value value="120" name="PTRBUFF_SC_VALID_CNTL_EVENT"/>
	<value value="121" name="PTRBUFF_SC_VALID_IJ_XFER"/>
	<value value="122" name="PTRBUFF_SC_NEW_VECTOR_1_Q"/>
	<value value="123" name="PTRBUFF_QUAL_NEW_VECTOR"/>
	<value value="124" name="PTRBUFF_QUAL_EVENT"/>
	<value value="125" name="PTRBUFF_END_BUFFER"/>
	<value value="126" name="PTRBUFF_FILL_QUAD"/>
	<value value="127" name="VERTS_WRITTEN_SPI"/>
	<value value="128" name="TP_FETCH_INSTR_EXEC"/>
	<value value="129" name="TP_FETCH_INSTR_REQ"/>
	<value value="130" name="TP_DATA_RETURN"/>
	<value value="131" name="SPI_WRITE_CYCLES_SP"/>
	<value value="132" name="SPI_WRITES_SP"/>
	<value value="133" name="SP_ALU_INSTR_EXEC"/>
	<value value="134" name="SP_CONST_ADDR_TO_SQ"/>
	<value value="135" name="SP_PRED_KILLS_TO_SQ"/>
	<value value="136" name="SP_EXPORT_CYCLES_TO_SX"/>
	<value value="137" name="SP_EXPORTS_TO_SX"/>
	<value value="138" name="SQ_CYCLES_ELAPSED"/>
	<value value="139" name="SQ_TCFS_OPT_ALLOC_EXEC"/>
	<value value="140" name="SQ_TCFS_NO_OPT_ALLOC"/>
	<value value="141" name="SQ_ALU0_NO_OPT_ALLOC"/>
	<value value="142" name="SQ_ALU1_NO_OPT_ALLOC"/>
	<value value="143" name="SQ_TCFS_ARB_XFC_CNT"/>
	<value value="144" name="SQ_ALU0_ARB_XFC_CNT"/>
	<value value="145" name="SQ_ALU1_ARB_XFC_CNT"/>
	<value value="146" name="SQ_TCFS_CFS_UPDATE_CNT"/>
	<value value="147" name="SQ_ALU0_CFS_UPDATE_CNT"/>
	<value value="148" name="SQ_ALU1_CFS_UPDATE_CNT"/>
	<value value="149" name="SQ_VTX_PUSH_THREAD_CNT"/>
	<value value="150" name="SQ_VTX_POP_THREAD_CNT"/>
	<value value="151" name="SQ_PIX_PUSH_THREAD_CNT"/>
	<value value="152" name="SQ_PIX_POP_THREAD_CNT"/>
	<value value="153" name="SQ_PIX_TOTAL"/>
	<value value="154" name="SQ_PIX_KILLED"/>
</enum>

<enum name="a2xx_sx_perfcnt_select">
	<value value="0" name="SX_EXPORT_VECTORS"/>
	<value value="1" name="SX_DUMMY_QUADS"/>
	<value value="2" name="SX_ALPHA_FAIL"/>
	<value value="3" name="SX_RB_QUAD_BUSY"/>
	<value value="4" name="SX_RB_COLOR_BUSY"/>
	<value value="5" name="SX_RB_QUAD_STALL"/>
	<value value="6" name="SX_RB_COLOR_STALL"/>
</enum>

<enum name="a2xx_rbbm_perfcount1_sel">
	<value value="0" name="RBBM1_COUNT"/>
	<value value="1" name="RBBM1_NRT_BUSY"/>
	<value value="2" name="RBBM1_RB_BUSY"/>
	<value value="3" name="RBBM1_SQ_CNTX0_BUSY"/>
	<value value="4" name="RBBM1_SQ_CNTX17_BUSY"/>
	<value value="5" name="RBBM1_VGT_BUSY"/>
	<value value="6" name="RBBM1_VGT_NODMA_BUSY"/>
	<value value="7" name="RBBM1_PA_BUSY"/>
	<value value="8" name="RBBM1_SC_CNTX_BUSY"/>
	<value value="9" name="RBBM1_TPC_BUSY"/>
	<value value="10" name="RBBM1_TC_BUSY"/>
	<value value="11" name="RBBM1_SX_BUSY"/>
	<value value="12" name="RBBM1_CP_COHER_BUSY"/>
	<value value="13" name="RBBM1_CP_NRT_BUSY"/>
	<value value="14" name="RBBM1_GFX_IDLE_STALL"/>
	<value value="15" name="RBBM1_INTERRUPT"/>
</enum>

<enum name="a2xx_cp_perfcount_sel">
	<value value="0" name="ALWAYS_COUNT"/>
	<value value="1" name="TRANS_FIFO_FULL"/>
	<value value="2" name="TRANS_FIFO_AF"/>
	<value value="3" name="RCIU_PFPTRANS_WAIT"/>
	<value value="6" name="RCIU_NRTTRANS_WAIT"/>
	<value value="8" name="CSF_NRT_READ_WAIT"/>
	<value value="9" name="CSF_I1_FIFO_FULL"/>
	<value value="10" name="CSF_I2_FIFO_FULL"/>
	<value value="11" name="CSF_ST_FIFO_FULL"/>
	<value value="13" name="CSF_RING_ROQ_FULL"/>
	<value value="14" name="CSF_I1_ROQ_FULL"/>
	<value value="15" name="CSF_I2_ROQ_FULL"/>
	<value value="16" name="CSF_ST_ROQ_FULL"/>
	<value value="18" name="MIU_TAG_MEM_FULL"/>
	<value value="19" name="MIU_WRITECLEAN"/>
	<value value="22" name="MIU_NRT_WRITE_STALLED"/>
	<value value="23" name="MIU_NRT_READ_STALLED"/>
	<value value="24" name="ME_WRITE_CONFIRM_FIFO_FULL"/>
	<value value="25" name="ME_VS_DEALLOC_FIFO_FULL"/>
	<value value="26" name="ME_PS_DEALLOC_FIFO_FULL"/>
	<value value="27" name="ME_REGS_VS_EVENT_FIFO_FULL"/>
	<value value="28" name="ME_REGS_PS_EVENT_FIFO_FULL"/>
	<value value="29" name="ME_REGS_CF_EVENT_FIFO_FULL"/>
	<value value="30" name="ME_MICRO_RB_STARVED"/>
	<value value="31" name="ME_MICRO_I1_STARVED"/>
	<value value="32" name="ME_MICRO_I2_STARVED"/>
	<value value="33" name="ME_MICRO_ST_STARVED"/>
	<value value="40" name="RCIU_RBBM_DWORD_SENT"/>
	<value value="41" name="ME_BUSY_CLOCKS"/>
	<value value="42" name="ME_WAIT_CONTEXT_AVAIL"/>
	<value value="43" name="PFP_TYPE0_PACKET"/>
	<value value="44" name="PFP_TYPE3_PACKET"/>
	<value value="45" name="CSF_RB_WPTR_NEQ_RPTR"/>
	<value value="46" name="CSF_I1_SIZE_NEQ_ZERO"/>
	<value value="47" name="CSF_I2_SIZE_NEQ_ZERO"/>
	<value value="48" name="CSF_RBI1I2_FETCHING"/>
</enum>

<enum name="a2xx_rb_perfcnt_select">
	<value value="0" name="RBPERF_CNTX_BUSY"/>
	<value value="1" name="RBPERF_CNTX_BUSY_MAX"/>
	<value value="2" name="RBPERF_SX_QUAD_STARVED"/>
	<value value="3" name="RBPERF_SX_QUAD_STARVED_MAX"/>
	<value value="4" name="RBPERF_GA_GC_CH0_SYS_REQ"/>
	<value value="5" name="RBPERF_GA_GC_CH0_SYS_REQ_MAX"/>
	<value value="6" name="RBPERF_GA_GC_CH1_SYS_REQ"/>
	<value value="7" name="RBPERF_GA_GC_CH1_SYS_REQ_MAX"/>
	<value value="8" name="RBPERF_MH_STARVED"/>
	<value value="9" name="RBPERF_MH_STARVED_MAX"/>
	<value value="10" name="RBPERF_AZ_BC_COLOR_BUSY"/>
	<value value="11" name="RBPERF_AZ_BC_COLOR_BUSY_MAX"/>
	<value value="12" name="RBPERF_AZ_BC_Z_BUSY"/>
	<value value="13" name="RBPERF_AZ_BC_Z_BUSY_MAX"/>
	<value value="14" name="RBPERF_RB_SC_TILE_RTR_N"/>
	<value value="15" name="RBPERF_RB_SC_TILE_RTR_N_MAX"/>
	<value value="16" name="RBPERF_RB_SC_SAMP_RTR_N"/>
	<value value="17" name="RBPERF_RB_SC_SAMP_RTR_N_MAX"/>
	<value value="18" name="RBPERF_RB_SX_QUAD_RTR_N"/>
	<value value="19" name="RBPERF_RB_SX_QUAD_RTR_N_MAX"/>
	<value value="20" name="RBPERF_RB_SX_COLOR_RTR_N"/>
	<value value="21" name="RBPERF_RB_SX_COLOR_RTR_N_MAX"/>
	<value value="22" name="RBPERF_RB_SC_SAMP_LZ_BUSY"/>
	<value value="23" name="RBPERF_RB_SC_SAMP_LZ_BUSY_MAX"/>
	<value value="24" name="RBPERF_ZXP_STALL"/>
	<value value="25" name="RBPERF_ZXP_STALL_MAX"/>
	<value value="26" name="RBPERF_EVENT_PENDING"/>
	<value value="27" name="RBPERF_EVENT_PENDING_MAX"/>
	<value value="28" name="RBPERF_RB_MH_VALID"/>
	<value value="29" name="RBPERF_RB_MH_VALID_MAX"/>
	<value value="30" name="RBPERF_SX_RB_QUAD_SEND"/>
	<value value="31" name="RBPERF_SX_RB_COLOR_SEND"/>
	<value value="32" name="RBPERF_SC_RB_TILE_SEND"/>
	<value value="33" name="RBPERF_SC_RB_SAMPLE_SEND"/>
	<value value="34" name="RBPERF_SX_RB_MEM_EXPORT"/>
	<value value="35" name="RBPERF_SX_RB_QUAD_EVENT"/>
	<value value="36" name="RBPERF_SC_RB_TILE_EVENT_FILTERED"/>
	<value value="37" name="RBPERF_SC_RB_TILE_EVENT_ALL"/>
	<value value="38" name="RBPERF_RB_SC_EZ_SEND"/>
	<value value="39" name="RBPERF_RB_SX_INDEX_SEND"/>
	<value value="40" name="RBPERF_GMEM_INTFO_RD"/>
	<value value="41" name="RBPERF_GMEM_INTF1_RD"/>
	<value value="42" name="RBPERF_GMEM_INTFO_WR"/>
	<value value="43" name="RBPERF_GMEM_INTF1_WR"/>
	<value value="44" name="RBPERF_RB_CP_CONTEXT_DONE"/>
	<value value="45" name="RBPERF_RB_CP_CACHE_FLUSH"/>
	<value value="46" name="RBPERF_ZPASS_DONE"/>
	<value value="47" name="RBPERF_ZCMD_VALID"/>
	<value value="48" name="RBPERF_CCMD_VALID"/>
	<value value="49" name="RBPERF_ACCUM_GRANT"/>
	<value value="50" name="RBPERF_ACCUM_C0_GRANT"/>
	<value value="51" name="RBPERF_ACCUM_C1_GRANT"/>
	<value value="52" name="RBPERF_ACCUM_FULL_BE_WR"/>
	<value value="53" name="RBPERF_ACCUM_REQUEST_NO_GRANT"/>
	<value value="54" name="RBPERF_ACCUM_TIMEOUT_PULSE"/>
	<value value="55" name="RBPERF_ACCUM_LIN_TIMEOUT_PULSE"/>
	<value value="56" name="RBPERF_ACCUM_CAM_HIT_FLUSHING"/>
</enum>

<enum name="a2xx_mh_perfcnt_select">
	<value value="0" name="CP_R0_REQUESTS"/>
	<value value="1" name="CP_R1_REQUESTS"/>
	<value value="2" name="CP_R2_REQUESTS"/>
	<value value="3" name="CP_R3_REQUESTS"/>
	<value value="4" name="CP_R4_REQUESTS"/>
	<value value="5" name="CP_TOTAL_READ_REQUESTS"/>
	<value value="6" name="CP_TOTAL_WRITE_REQUESTS"/>
	<value value="7" name="CP_TOTAL_REQUESTS"/>
	<value value="8" name="CP_DATA_BYTES_WRITTEN"/>
	<value value="9" name="CP_WRITE_CLEAN_RESPONSES"/>
	<value value="10" name="CP_R0_READ_BURSTS_RECEIVED"/>
	<value value="11" name="CP_R1_READ_BURSTS_RECEIVED"/>
	<value value="12" name="CP_R2_READ_BURSTS_RECEIVED"/>
	<value value="13" name="CP_R3_READ_BURSTS_RECEIVED"/>
	<value value="14" name="CP_R4_READ_BURSTS_RECEIVED"/>
	<value value="15" name="CP_TOTAL_READ_BURSTS_RECEIVED"/>
	<value value="16" name="CP_R0_DATA_BEATS_READ"/>
	<value value="17" name="CP_R1_DATA_BEATS_READ"/>
	<value value="18" name="CP_R2_DATA_BEATS_READ"/>
	<value value="19" name="CP_R3_DATA_BEATS_READ"/>
	<value value="20" name="CP_R4_DATA_BEATS_READ"/>
	<value value="21" name="CP_TOTAL_DATA_BEATS_READ"/>
	<value value="22" name="VGT_R0_REQUESTS"/>
	<value value="23" name="VGT_R1_REQUESTS"/>
	<value value="24" name="VGT_TOTAL_REQUESTS"/>
	<value value="25" name="VGT_R0_READ_BURSTS_RECEIVED"/>
	<value value="26" name="VGT_R1_READ_BURSTS_RECEIVED"/>
	<value value="27" name="VGT_TOTAL_READ_BURSTS_RECEIVED"/>
	<value value="28" name="VGT_R0_DATA_BEATS_READ"/>
	<value value="29" name="VGT_R1_DATA_BEATS_READ"/>
	<value value="30" name="VGT_TOTAL_DATA_BEATS_READ"/>
	<value value="31" name="TC_TOTAL_REQUESTS"/>
	<value value="32" name="TC_ROQ_REQUESTS"/>
	<value value="33" name="TC_INFO_SENT"/>
	<value value="34" name="TC_READ_BURSTS_RECEIVED"/>
	<value value="35" name="TC_DATA_BEATS_READ"/>
	<value value="36" name="TCD_BURSTS_READ"/>
	<value value="37" name="RB_REQUESTS"/>
	<value value="38" name="RB_DATA_BYTES_WRITTEN"/>
	<value value="39" name="RB_WRITE_CLEAN_RESPONSES"/>
	<value value="40" name="AXI_READ_REQUESTS_ID_0"/>
	<value value="41" name="AXI_READ_REQUESTS_ID_1"/>
	<value value="42" name="AXI_READ_REQUESTS_ID_2"/>
	<value value="43" name="AXI_READ_REQUESTS_ID_3"/>
	<value value="44" name="AXI_READ_REQUESTS_ID_4"/>
	<value value="45" name="AXI_READ_REQUESTS_ID_5"/>
	<value value="46" name="AXI_READ_REQUESTS_ID_6"/>
	<value value="47" name="AXI_READ_REQUESTS_ID_7"/>
	<value value="48" name="AXI_TOTAL_READ_REQUESTS"/>
	<value value="49" name="AXI_WRITE_REQUESTS_ID_0"/>
	<value value="50" name="AXI_WRITE_REQUESTS_ID_1"/>
	<value value="51" name="AXI_WRITE_REQUESTS_ID_2"/>
	<value value="52" name="AXI_WRITE_REQUESTS_ID_3"/>
	<value value="53" name="AXI_WRITE_REQUESTS_ID_4"/>
	<value value="54" name="AXI_WRITE_REQUESTS_ID_5"/>
	<value value="55" name="AXI_WRITE_REQUESTS_ID_6"/>
	<value value="56" name="AXI_WRITE_REQUESTS_ID_7"/>
	<value value="57" name="AXI_TOTAL_WRITE_REQUESTS"/>
	<value value="58" name="AXI_TOTAL_REQUESTS_ID_0"/>
	<value value="59" name="AXI_TOTAL_REQUESTS_ID_1"/>
	<value value="60" name="AXI_TOTAL_REQUESTS_ID_2"/>
	<value value="61" name="AXI_TOTAL_REQUESTS_ID_3"/>
	<value value="62" name="AXI_TOTAL_REQUESTS_ID_4"/>
	<value value="63" name="AXI_TOTAL_REQUESTS_ID_5"/>
	<value value="64" name="AXI_TOTAL_REQUESTS_ID_6"/>
	<value value="65" name="AXI_TOTAL_REQUESTS_ID_7"/>
	<value value="66" name="AXI_TOTAL_REQUESTS"/>
	<value value="67" name="AXI_READ_CHANNEL_BURSTS_ID_0"/>
	<value value="68" name="AXI_READ_CHANNEL_BURSTS_ID_1"/>
	<value value="69" name="AXI_READ_CHANNEL_BURSTS_ID_2"/>
	<value value="70" name="AXI_READ_CHANNEL_BURSTS_ID_3"/>
	<value value="71" name="AXI_READ_CHANNEL_BURSTS_ID_4"/>
	<value value="72" name="AXI_READ_CHANNEL_BURSTS_ID_5"/>
	<value value="73" name="AXI_READ_CHANNEL_BURSTS_ID_6"/>
	<value value="74" name="AXI_READ_CHANNEL_BURSTS_ID_7"/>
	<value value="75" name="AXI_READ_CHANNEL_TOTAL_BURSTS"/>
	<value value="76" name="AXI_READ_CHANNEL_DATA_BEATS_READ_ID_0"/>
	<value value="77" name="AXI_READ_CHANNEL_DATA_BEATS_READ_ID_1"/>
	<value value="78" name="AXI_READ_CHANNEL_DATA_BEATS_READ_ID_2"/>
	<value value="79" name="AXI_READ_CHANNEL_DATA_BEATS_READ_ID_3"/>
	<value value="80" name="AXI_READ_CHANNEL_DATA_BEATS_READ_ID_4"/>
	<value value="81" name="AXI_READ_CHANNEL_DATA_BEATS_READ_ID_5"/>
	<value value="82" name="AXI_READ_CHANNEL_DATA_BEATS_READ_ID_6"/>
	<value value="83" name="AXI_READ_CHANNEL_DATA_BEATS_READ_ID_7"/>
	<value value="84" name="AXI_READ_CHANNEL_TOTAL_DATA_BEATS_READ"/>
	<value value="85" name="AXI_WRITE_CHANNEL_BURSTS_ID_0"/>
	<value value="86" name="AXI_WRITE_CHANNEL_BURSTS_ID_1"/>
	<value value="87" name="AXI_WRITE_CHANNEL_BURSTS_ID_2"/>
	<value value="88" name="AXI_WRITE_CHANNEL_BURSTS_ID_3"/>
	<value value="89" name="AXI_WRITE_CHANNEL_BURSTS_ID_4"/>
	<value value="90" name="AXI_WRITE_CHANNEL_BURSTS_ID_5"/>
	<value value="91" name="AXI_WRITE_CHANNEL_BURSTS_ID_6"/>
	<value value="92" name="AXI_WRITE_CHANNEL_BURSTS_ID_7"/>
	<value value="93" name="AXI_WRITE_CHANNEL_TOTAL_BURSTS"/>
	<value value="94" name="AXI_WRITE_CHANNEL_DATA_BYTES_WRITTEN_ID_0"/>
	<value value="95" name="AXI_WRITE_CHANNEL_DATA_BYTES_WRITTEN_ID_1"/>
	<value value="96" name="AXI_WRITE_CHANNEL_DATA_BYTES_WRITTEN_ID_2"/>
	<value value="97" name="AXI_WRITE_CHANNEL_DATA_BYTES_WRITTEN_ID_3"/>
	<value value="98" name="AXI_WRITE_CHANNEL_DATA_BYTES_WRITTEN_ID_4"/>
	<value value="99" name="AXI_WRITE_CHANNEL_DATA_BYTES_WRITTEN_ID_5"/>
	<value value="100" name="AXI_WRITE_CHANNEL_DATA_BYTES_WRITTEN_ID_6"/>
	<value value="101" name="AXI_WRITE_CHANNEL_DATA_BYTES_WRITTEN_ID_7"/>
	<value value="102" name="AXI_WRITE_CHANNEL_TOTAL_DATA_BYTES_WRITTEN"/>
	<value value="103" name="AXI_WRITE_RESPONSE_CHANNEL_RESPONSES_ID_0"/>
	<value value="104" name="AXI_WRITE_RESPONSE_CHANNEL_RESPONSES_ID_1"/>
	<value value="105" name="AXI_WRITE_RESPONSE_CHANNEL_RESPONSES_ID_2"/>
	<value value="106" name="AXI_WRITE_RESPONSE_CHANNEL_RESPONSES_ID_3"/>
	<value value="107" name="AXI_WRITE_RESPONSE_CHANNEL_RESPONSES_ID_4"/>
	<value value="108" name="AXI_WRITE_RESPONSE_CHANNEL_RESPONSES_ID_5"/>
	<value value="109" name="AXI_WRITE_RESPONSE_CHANNEL_RESPONSES_ID_6"/>
	<value value="110" name="AXI_WRITE_RESPONSE_CHANNEL_RESPONSES_ID_7"/>
	<value value="111" name="AXI_WRITE_RESPONSE_CHANNEL_TOTAL_RESPONSES"/>
	<value value="112" name="TOTAL_MMU_MISSES"/>
	<value value="113" name="MMU_READ_MISSES"/>
	<value value="114" name="MMU_WRITE_MISSES"/>
	<value value="115" name="TOTAL_MMU_HITS"/>
	<value value="116" name="MMU_READ_HITS"/>
	<value value="117" name="MMU_WRITE_HITS"/>
	<value value="118" name="SPLIT_MODE_TC_HITS"/>
	<value value="119" name="SPLIT_MODE_TC_MISSES"/>
	<value value="120" name="SPLIT_MODE_NON_TC_HITS"/>
	<value value="121" name="SPLIT_MODE_NON_TC_MISSES"/>
	<value value="122" name="STALL_AWAITING_TLB_MISS_FETCH"/>
	<value value="123" name="MMU_TLB_MISS_READ_BURSTS_RECEIVED"/>
	<value value="124" name="MMU_TLB_MISS_DATA_BEATS_READ"/>
	<value value="125" name="CP_CYCLES_HELD_OFF"/>
	<value value="126" name="VGT_CYCLES_HELD_OFF"/>
	<value value="127" name="TC_CYCLES_HELD_OFF"/>
	<value value="128" name="TC_ROQ_CYCLES_HELD_OFF"/>
	<value value="129" name="TC_CYCLES_HELD_OFF_TCD_FULL"/>
	<value value="130" name="RB_CYCLES_HELD_OFF"/>
	<value value="131" name="TOTAL_CYCLES_ANY_CLNT_HELD_OFF"/>
	<value value="132" name="TLB_MISS_CYCLES_HELD_OFF"/>
	<value value="133" name="AXI_READ_REQUEST_HELD_OFF"/>
	<value value="134" name="AXI_WRITE_REQUEST_HELD_OFF"/>
	<value value="135" name="AXI_REQUEST_HELD_OFF"/>
	<value value="136" name="AXI_REQUEST_HELD_OFF_INFLIGHT_LIMIT"/>
	<value value="137" name="AXI_WRITE_DATA_HELD_OFF"/>
	<value value="138" name="CP_SAME_PAGE_BANK_REQUESTS"/>
	<value value="139" name="VGT_SAME_PAGE_BANK_REQUESTS"/>
	<value value="140" name="TC_SAME_PAGE_BANK_REQUESTS"/>
	<value value="141" name="TC_ARB_HOLD_SAME_PAGE_BANK_REQUESTS"/>
	<value value="142" name="RB_SAME_PAGE_BANK_REQUESTS"/>
	<value value="143" name="TOTAL_SAME_PAGE_BANK_REQUESTS"/>
	<value value="144" name="CP_SAME_PAGE_BANK_REQUESTS_KILLED_FAIRNESS_LIMIT"/>
	<value value="145" name="VGT_SAME_PAGE_BANK_REQUESTS_KILLED_FAIRNESS_LIMIT"/>
	<value value="146" name="TC_SAME_PAGE_BANK_REQUESTS_KILLED_FAIRNESS_LIMIT"/>
	<value value="147" name="RB_SAME_PAGE_BANK_REQUESTS_KILLED_FAIRNESS_LIMIT"/>
	<value value="148" name="TOTAL_SAME_PAGE_BANK_KILLED_FAIRNESS_LIMIT"/>
	<value value="149" name="TOTAL_MH_READ_REQUESTS"/>
	<value value="150" name="TOTAL_MH_WRITE_REQUESTS"/>
	<value value="151" name="TOTAL_MH_REQUESTS"/>
	<value value="152" name="MH_BUSY"/>
	<value value="153" name="CP_NTH_ACCESS_SAME_PAGE_BANK_SEQUENCE"/>
	<value value="154" name="VGT_NTH_ACCESS_SAME_PAGE_BANK_SEQUENCE"/>
	<value value="155" name="TC_NTH_ACCESS_SAME_PAGE_BANK_SEQUENCE"/>
	<value value="156" name="RB_NTH_ACCESS_SAME_PAGE_BANK_SEQUENCE"/>
	<value value="157" name="TC_ROQ_N_VALID_ENTRIES"/>
	<value value="158" name="ARQ_N_ENTRIES"/>
	<value value="159" name="WDB_N_ENTRIES"/>
	<value value="160" name="MH_READ_LATENCY_OUTST_REQ_SUM"/>
	<value value="161" name="MC_READ_LATENCY_OUTST_REQ_SUM"/>
	<value value="162" name="MC_TOTAL_READ_REQUESTS"/>
	<value value="163" name="ELAPSED_CYCLES_MH_GATED_CLK"/>
	<value value="164" name="ELAPSED_CLK_CYCLES"/>
	<value value="165" name="CP_W_16B_REQUESTS"/>
	<value value="166" name="CP_W_32B_REQUESTS"/>
	<value value="167" name="TC_16B_REQUESTS"/>
	<value value="168" name="TC_32B_REQUESTS"/>
	<value value="169" name="PA_REQUESTS"/>
	<value value="170" name="PA_DATA_BYTES_WRITTEN"/>
	<value value="171" name="PA_WRITE_CLEAN_RESPONSES"/>
	<value value="172" name="PA_CYCLES_HELD_OFF"/>
	<value value="173" name="AXI_READ_REQUEST_DATA_BEATS_ID_0"/>
	<value value="174" name="AXI_READ_REQUEST_DATA_BEATS_ID_1"/>
	<value value="175" name="AXI_READ_REQUEST_DATA_BEATS_ID_2"/>
	<value value="176" name="AXI_READ_REQUEST_DATA_BEATS_ID_3"/>
	<value value="177" name="AXI_READ_REQUEST_DATA_BEATS_ID_4"/>
	<value value="178" name="AXI_READ_REQUEST_DATA_BEATS_ID_5"/>
	<value value="179" name="AXI_READ_REQUEST_DATA_BEATS_ID_6"/>
	<value value="180" name="AXI_READ_REQUEST_DATA_BEATS_ID_7"/>
	<value value="181" name="AXI_TOTAL_READ_REQUEST_DATA_BEATS"/>
</enum>

<enum name="perf_mode_cnt">
	<value name="PERF_STATE_RESET" value="0"/>
	<value name="PERF_STATE_ENABLE" value="1"/>
	<value name="PERF_STATE_FREEZE" value="2"/>
</enum>

<domain name="A2XX" width="32">

	<bitset name="a2xx_vgt_current_bin_id_min_max" inline="yes">
		<bitfield name="COLUMN" low="0" high="2" type="uint"/>
		<bitfield name="ROW" low="3" high="5" type="uint"/>
		<bitfield name="GUARD_BAND_MASK" low="6" high="8" type="uint"/>
	</bitset>

	<reg32 offset="0x0001" name="RBBM_PATCH_RELEASE"/>
	<reg32 offset="0x003b" name="RBBM_CNTL"/>
	<reg32 offset="0x003c" name="RBBM_SOFT_RESET"/>
	<reg32 offset="0x00c0" name="CP_PFP_UCODE_ADDR"/>
	<reg32 offset="0x00c1" name="CP_PFP_UCODE_DATA"/>

	<enum name="adreno_mmu_clnt_beh">
		<value name="BEH_NEVR" value="0"/>
		<value name="BEH_TRAN_RNG" value="1"/>
		<value name="BEH_TRAN_FLT" value="2"/>
	</enum>

	<!--
		Note: these seem applicable only for a2xx devices with gpummu?  At
		any rate, MH_MMU_CONFIG shows up in places in a3xx firmware where
		it doesn't make sense, so I think offset 0x40 must be a different
		register on a3xx.. so moving this back into A2XX domain:
	 -->
	<reg32 offset="0x0040" name="MH_MMU_CONFIG">
		<bitfield name="MMU_ENABLE" pos="0" type="boolean"/>
		<bitfield name="SPLIT_MODE_ENABLE" pos="1" type="boolean"/>
		<bitfield name="RB_W_CLNT_BEHAVIOR" low="4" high="5" type="adreno_mmu_clnt_beh"/>
		<bitfield name="CP_W_CLNT_BEHAVIOR" low="6" high="7" type="adreno_mmu_clnt_beh"/>
		<bitfield name="CP_R0_CLNT_BEHAVIOR" low="8" high="9" type="adreno_mmu_clnt_beh"/>
		<bitfield name="CP_R1_CLNT_BEHAVIOR" low="10" high="11" type="adreno_mmu_clnt_beh"/>
		<bitfield name="CP_R2_CLNT_BEHAVIOR" low="12" high="13" type="adreno_mmu_clnt_beh"/>
		<bitfield name="CP_R3_CLNT_BEHAVIOR" low="14" high="15" type="adreno_mmu_clnt_beh"/>
		<bitfield name="CP_R4_CLNT_BEHAVIOR" low="16" high="17" type="adreno_mmu_clnt_beh"/>
		<bitfield name="VGT_R0_CLNT_BEHAVIOR" low="18" high="19" type="adreno_mmu_clnt_beh"/>
		<bitfield name="VGT_R1_CLNT_BEHAVIOR" low="20" high="21" type="adreno_mmu_clnt_beh"/>
		<bitfield name="TC_R_CLNT_BEHAVIOR" low="22" high="23" type="adreno_mmu_clnt_beh"/>
		<bitfield name="PA_W_CLNT_BEHAVIOR" low="24" high="25" type="adreno_mmu_clnt_beh"/>
	</reg32>
	<reg32 offset="0x0041" name="MH_MMU_VA_RANGE">
		<bitfield name="NUM_64KB_REGIONS" low="0" high="11" type="uint"/>
		<bitfield name="VA_BASE" low="12" high="31" type="uint"/>
	</reg32>
	<reg32 offset="0x0042" name="MH_MMU_PT_BASE"/>
	<reg32 offset="0x0043" name="MH_MMU_PAGE_FAULT"/>
	<reg32 offset="0x0044" name="MH_MMU_TRAN_ERROR"/>
	<reg32 offset="0x0045" name="MH_MMU_INVALIDATE">
		<bitfield name="INVALIDATE_ALL" pos="0" type="boolean"/>
		<bitfield name="INVALIDATE_TC" pos="1" type="boolean"/>
	</reg32>
	<reg32 offset="0x0046" name="MH_MMU_MPU_BASE"/>
	<reg32 offset="0x0047" name="MH_MMU_MPU_END"/>

	<reg32 offset="0x0394" name="NQWAIT_UNTIL"/>
	<reg32 offset="0x0395" name="RBBM_PERFCOUNTER0_SELECT"/>
	<reg32 offset="0x0396" name="RBBM_PERFCOUNTER1_SELECT"/>
	<reg32 offset="0x0397" name="RBBM_PERFCOUNTER0_LO"/>
	<reg32 offset="0x0398" name="RBBM_PERFCOUNTER0_HI"/>
	<reg32 offset="0x0399" name="RBBM_PERFCOUNTER1_LO"/>
	<reg32 offset="0x039a" name="RBBM_PERFCOUNTER1_HI"/>
	<reg32 offset="0x039b" name="RBBM_DEBUG"/>
	<reg32 offset="0x039c" name="RBBM_PM_OVERRIDE1">
		<bitfield name="RBBM_AHBCLK_PM_OVERRIDE" pos="0" type="boolean"/>
		<bitfield name="SC_REG_SCLK_PM_OVERRIDE" pos="1" type="boolean"/>
		<bitfield name="SC_SCLK_PM_OVERRIDE" pos="2" type="boolean"/>
		<bitfield name="SP_TOP_SCLK_PM_OVERRIDE" pos="3" type="boolean"/>
		<bitfield name="SP_V0_SCLK_PM_OVERRIDE" pos="4" type="boolean"/>
		<bitfield name="SQ_REG_SCLK_PM_OVERRIDE" pos="5" type="boolean"/>
		<bitfield name="SQ_REG_FIFOS_SCLK_PM_OVERRIDE" pos="6" type="boolean"/>
		<bitfield name="SQ_CONST_MEM_SCLK_PM_OVERRIDE" pos="7" type="boolean"/>
		<bitfield name="SQ_SQ_SCLK_PM_OVERRIDE" pos="8" type="boolean"/>
		<bitfield name="SX_SCLK_PM_OVERRIDE" pos="9" type="boolean"/>
		<bitfield name="SX_REG_SCLK_PM_OVERRIDE" pos="10" type="boolean"/>
		<bitfield name="TCM_TCO_SCLK_PM_OVERRIDE" pos="11" type="boolean"/>
		<bitfield name="TCM_TCM_SCLK_PM_OVERRIDE" pos="12" type="boolean"/>
		<bitfield name="TCM_TCD_SCLK_PM_OVERRIDE" pos="13" type="boolean"/>
		<bitfield name="TCM_REG_SCLK_PM_OVERRIDE" pos="14" type="boolean"/>
		<bitfield name="TPC_TPC_SCLK_PM_OVERRIDE" pos="15" type="boolean"/>
		<bitfield name="TPC_REG_SCLK_PM_OVERRIDE" pos="16" type="boolean"/>
		<bitfield name="TCF_TCA_SCLK_PM_OVERRIDE" pos="17" type="boolean"/>
		<bitfield name="TCF_TCB_SCLK_PM_OVERRIDE" pos="18" type="boolean"/>
		<bitfield name="TCF_TCB_READ_SCLK_PM_OVERRIDE" pos="19" type="boolean"/>
		<bitfield name="TP_TP_SCLK_PM_OVERRIDE" pos="20" type="boolean"/>
		<bitfield name="TP_REG_SCLK_PM_OVERRIDE" pos="21" type="boolean"/>
		<bitfield name="CP_G_SCLK_PM_OVERRIDE" pos="22" type="boolean"/>
		<bitfield name="CP_REG_SCLK_PM_OVERRIDE" pos="23" type="boolean"/>
		<bitfield name="CP_G_REG_SCLK_PM_OVERRIDE" pos="24" type="boolean"/>
		<bitfield name="SPI_SCLK_PM_OVERRIDE" pos="25" type="boolean"/>
		<bitfield name="RB_REG_SCLK_PM_OVERRIDE" pos="26" type="boolean"/>
		<bitfield name="RB_SCLK_PM_OVERRIDE" pos="27" type="boolean"/>
		<bitfield name="MH_MH_SCLK_PM_OVERRIDE" pos="28" type="boolean"/>
		<bitfield name="MH_REG_SCLK_PM_OVERRIDE" pos="29" type="boolean"/>
		<bitfield name="MH_MMU_SCLK_PM_OVERRIDE" pos="30" type="boolean"/>
		<bitfield name="MH_TCROQ_SCLK_PM_OVERRIDE" pos="31" type="boolean"/>
	</reg32>
	<reg32 offset="0x039d" name="RBBM_PM_OVERRIDE2">
		<bitfield name="PA_REG_SCLK_PM_OVERRIDE" pos="0" type="boolean"/>
		<bitfield name="PA_PA_SCLK_PM_OVERRIDE" pos="1" type="boolean"/>
		<bitfield name="PA_AG_SCLK_PM_OVERRIDE" pos="2" type="boolean"/>
		<bitfield name="VGT_REG_SCLK_PM_OVERRIDE" pos="3" type="boolean"/>
		<bitfield name="VGT_FIFOS_SCLK_PM_OVERRIDE" pos="4" type="boolean"/>
		<bitfield name="VGT_VGT_SCLK_PM_OVERRIDE" pos="5" type="boolean"/>
		<bitfield name="DEBUG_PERF_SCLK_PM_OVERRIDE" pos="6" type="boolean"/>
		<bitfield name="PERM_SCLK_PM_OVERRIDE" pos="7" type="boolean"/>
		<bitfield name="GC_GA_GMEM0_PM_OVERRIDE" pos="8" type="boolean"/>
		<bitfield name="GC_GA_GMEM1_PM_OVERRIDE" pos="9" type="boolean"/>
		<bitfield name="GC_GA_GMEM2_PM_OVERRIDE" pos="10" type="boolean"/>
		<bitfield name="GC_GA_GMEM3_PM_OVERRIDE" pos="11" type="boolean"/>
	</reg32>
	<reg32 offset="0x03a0" name="RBBM_DEBUG_OUT"/>
	<reg32 offset="0x03a1" name="RBBM_DEBUG_CNTL"/>
	<reg32 offset="0x03b3" name="RBBM_READ_ERROR"/>
	<reg32 offset="0x03b4" name="RBBM_INT_CNTL">
		<bitfield name="RDERR_INT_MASK" pos="0" type="boolean"/>
		<bitfield name="DISPLAY_UPDATE_INT_MASK" pos="1" type="boolean"/>
		<bitfield name="GUI_IDLE_INT_MASK" pos="19" type="boolean"/>
	</reg32>
	<reg32 offset="0x03b5" name="RBBM_INT_STATUS"/>
	<reg32 offset="0x03b6" name="RBBM_INT_ACK"/>
	<reg32 offset="0x03b7" name="MASTER_INT_SIGNAL">
		<bitfield name="MH_INT_STAT" pos="5" type="boolean"/>
		<bitfield name="SQ_INT_STAT" pos="26" type="boolean"/>
		<bitfield name="CP_INT_STAT" pos="30" type="boolean"/>
		<bitfield name="RBBM_INT_STAT" pos="31" type="boolean"/>
	</reg32>
	<reg32 offset="0x03f9" name="RBBM_PERIPHID1"/>
	<reg32 offset="0x03fa" name="RBBM_PERIPHID2"/>
	<reg32 offset="0x0444" name="CP_PERFMON_CNTL">
		<!-- The width is uncertain -->
		<bitfield name="PERF_MODE_CNT" low="0" high="2" type="perf_mode_cnt"/>
	</reg32>
	<reg32 offset="0x0445" name="CP_PERFCOUNTER_SELECT"/>
	<reg32 offset="0x0446" name="CP_PERFCOUNTER_LO"/>
	<reg32 offset="0x0447" name="CP_PERFCOUNTER_HI"/>
	<reg32 offset="0x05d0" name="RBBM_STATUS">
		<bitfield name="CMDFIFO_AVAIL" low="0" high="4" type="uint"/>
		<bitfield name="TC_BUSY" pos="5" type="boolean"/>
		<bitfield name="HIRQ_PENDING" pos="8" type="boolean"/>
		<bitfield name="CPRQ_PENDING" pos="9" type="boolean"/>
		<bitfield name="CFRQ_PENDING" pos="10" type="boolean"/>
		<bitfield name="PFRQ_PENDING" pos="11" type="boolean"/>
		<bitfield name="VGT_BUSY_NO_DMA" pos="12" type="boolean"/>
		<bitfield name="RBBM_WU_BUSY" pos="14" type="boolean"/>
		<bitfield name="CP_NRT_BUSY" pos="16" type="boolean"/>
		<bitfield name="MH_BUSY" pos="18" type="boolean"/>
		<bitfield name="MH_COHERENCY_BUSY" pos="19" type="boolean"/>
		<bitfield name="SX_BUSY" pos="21" type="boolean"/>
		<bitfield name="TPC_BUSY" pos="22" type="boolean"/>
		<bitfield name="SC_CNTX_BUSY" pos="24" type="boolean"/>
		<bitfield name="PA_BUSY" pos="25" type="boolean"/>
		<bitfield name="VGT_BUSY" pos="26" type="boolean"/>
		<bitfield name="SQ_CNTX17_BUSY" pos="27" type="boolean"/>
		<bitfield name="SQ_CNTX0_BUSY" pos="28" type="boolean"/>
		<bitfield name="RB_CNTX_BUSY" pos="30" type="boolean"/>
		<bitfield name="GUI_ACTIVE" pos="31" type="boolean"/>
	</reg32>
	<reg32 offset="0x0a40" name="MH_ARBITER_CONFIG">
		<bitfield name="SAME_PAGE_LIMIT" low="0" high="5" type="uint"/>
		<bitfield name="SAME_PAGE_GRANULARITY" pos="6" type="boolean"/>
		<bitfield name="L1_ARB_ENABLE" pos="7" type="boolean"/>
		<bitfield name="L1_ARB_HOLD_ENABLE" pos="8" type="boolean"/>
		<bitfield name="L2_ARB_CONTROL" pos="9" type="boolean"/>
		<bitfield name="PAGE_SIZE" low="10" high="12" type="uint"/>
		<bitfield name="TC_REORDER_ENABLE" pos="13" type="boolean"/>
		<bitfield name="TC_ARB_HOLD_ENABLE" pos="14" type="boolean"/>
		<bitfield name="IN_FLIGHT_LIMIT_ENABLE" pos="15" type="boolean"/>
		<bitfield name="IN_FLIGHT_LIMIT" low="16" high="21" type="uint"/>
		<bitfield name="CP_CLNT_ENABLE" pos="22" type="boolean"/>
		<bitfield name="VGT_CLNT_ENABLE" pos="23" type="boolean"/>
		<bitfield name="TC_CLNT_ENABLE" pos="24" type="boolean"/>
		<bitfield name="RB_CLNT_ENABLE" pos="25" type="boolean"/>
		<bitfield name="PA_CLNT_ENABLE" pos="26" type="boolean"/>
	</reg32>
	<reg32 offset="0x0a42" name="MH_INTERRUPT_MASK">
		<bitfield name="AXI_READ_ERROR" pos="0" type="boolean"/>
		<bitfield name="AXI_WRITE_ERROR" pos="1" type="boolean"/>
		<bitfield name="MMU_PAGE_FAULT" pos="2" type="boolean"/>
	</reg32>
	<reg32 offset="0x0a43" name="MH_INTERRUPT_STATUS"/>
	<reg32 offset="0x0a44" name="MH_INTERRUPT_CLEAR"/>
	<reg32 offset="0x0a54" name="MH_CLNT_INTF_CTRL_CONFIG1"/>
	<reg32 offset="0x0a55" name="MH_CLNT_INTF_CTRL_CONFIG2"/>
	<reg32 offset="0x0c01" name="A220_VSC_BIN_SIZE">
		<bitfield name="WIDTH" low="0" high="4" shr="5" type="uint"/>
		<bitfield name="HEIGHT" low="5" high="9" shr="5" type="uint"/>
	</reg32>
	<array offset="0x0c06" name="VSC_PIPE" stride="3" length="8">
		<reg32 offset="0x0" name="CONFIG"/>
		<reg32 offset="0x1" name="DATA_ADDRESS"/>
		<reg32 offset="0x2" name="DATA_LENGTH"/>
	</array>
	<reg32 offset="0x0c38" name="PC_DEBUG_CNTL"/>
	<reg32 offset="0x0c39" name="PC_DEBUG_DATA"/>
	<reg32 offset="0x0c44" name="PA_SC_VIZ_QUERY_STATUS"/>
	<reg32 offset="0x0c80" name="GRAS_DEBUG_CNTL"/>
	<reg32 offset="0x0c80" name="PA_SU_DEBUG_CNTL"/>
	<reg32 offset="0x0c81" name="GRAS_DEBUG_DATA"/>
	<reg32 offset="0x0c81" name="PA_SU_DEBUG_DATA"/>
	<reg32 offset="0x0c86" name="PA_SU_FACE_DATA">
		<bitfield name="BASE_ADDR" low="5" high="31" type="uint"/>
	</reg32>
	<reg32 offset="0x0d00" name="SQ_GPR_MANAGEMENT">
		<bitfield name="REG_DYNAMIC" pos="0" type="boolean"/>
		<bitfield name="REG_SIZE_PIX" low="4" high="11" type="uint"/>
		<bitfield name="REG_SIZE_VTX" low="12" high="19" type="uint"/>
	</reg32>
	<reg32 offset="0x0d01" name="SQ_FLOW_CONTROL"/>
	<reg32 offset="0x0d02" name="SQ_INST_STORE_MANAGMENT">
		<bitfield name="INST_BASE_PIX" low="0" high="11" type="uint"/>
		<bitfield name="INST_BASE_VTX" low="16" high="27" type="uint"/>
	</reg32>
	<reg32 offset="0x0d05" name="SQ_DEBUG_MISC"/>
	<reg32 offset="0x0d34" name="SQ_INT_CNTL"/>
	<reg32 offset="0x0d35" name="SQ_INT_STATUS"/>
	<reg32 offset="0x0d36" name="SQ_INT_ACK"/>
	<reg32 offset="0x0dae" name="SQ_DEBUG_INPUT_FSM"/>
	<reg32 offset="0x0daf" name="SQ_DEBUG_CONST_MGR_FSM"/>
	<reg32 offset="0x0db0" name="SQ_DEBUG_TP_FSM"/>
	<reg32 offset="0x0db1" name="SQ_DEBUG_FSM_ALU_0"/>
	<reg32 offset="0x0db2" name="SQ_DEBUG_FSM_ALU_1"/>
	<reg32 offset="0x0db3" name="SQ_DEBUG_EXP_ALLOC"/>
	<reg32 offset="0x0db4" name="SQ_DEBUG_PTR_BUFF"/>
	<reg32 offset="0x0db5" name="SQ_DEBUG_GPR_VTX"/>
	<reg32 offset="0x0db6" name="SQ_DEBUG_GPR_PIX"/>
	<reg32 offset="0x0db7" name="SQ_DEBUG_TB_STATUS_SEL"/>
	<reg32 offset="0x0db8" name="SQ_DEBUG_VTX_TB_0"/>
	<reg32 offset="0x0db9" name="SQ_DEBUG_VTX_TB_1"/>
	<reg32 offset="0x0dba" name="SQ_DEBUG_VTX_TB_STATUS_REG"/>
	<reg32 offset="0x0dbb" name="SQ_DEBUG_VTX_TB_STATE_MEM"/>
	<reg32 offset="0x0dbc" name="SQ_DEBUG_PIX_TB_0"/>
	<reg32 offset="0x0dbd" name="SQ_DEBUG_PIX_TB_STATUS_REG_0"/>
	<reg32 offset="0x0dbe" name="SQ_DEBUG_PIX_TB_STATUS_REG_1"/>
	<reg32 offset="0x0dbf" name="SQ_DEBUG_PIX_TB_STATUS_REG_2"/>
	<reg32 offset="0x0dc0" name="SQ_DEBUG_PIX_TB_STATUS_REG_3"/>
	<reg32 offset="0x0dc1" name="SQ_DEBUG_PIX_TB_STATE_MEM"/>
	<reg32 offset="0x0e00" name="TC_CNTL_STATUS">
		<bitfield name="L2_INVALIDATE" pos="0" type="boolean"/>
	</reg32>
	<reg32 offset="0x0e1e" name="TP0_CHICKEN"/>
	<reg32 offset="0x0f01" name="RB_BC_CONTROL">
		<bitfield name="ACCUM_LINEAR_MODE_ENABLE" pos="0" type="boolean"/>
		<bitfield name="ACCUM_TIMEOUT_SELECT" low="1" high="2" type="uint"/>
		<bitfield name="DISABLE_EDRAM_CAM" pos="3" type="boolean"/>
		<bitfield name="DISABLE_EZ_FAST_CONTEXT_SWITCH" pos="4" type="boolean"/>
		<bitfield name="DISABLE_EZ_NULL_ZCMD_DROP" pos="5" type="boolean"/>
		<bitfield name="DISABLE_LZ_NULL_ZCMD_DROP" pos="6" type="boolean"/>
		<bitfield name="ENABLE_AZ_THROTTLE" pos="7" type="boolean"/>
		<bitfield name="AZ_THROTTLE_COUNT" low="8" high="12" type="uint"/>
		<bitfield name="ENABLE_CRC_UPDATE" pos="14" type="boolean"/>
		<bitfield name="CRC_MODE" pos="15" type="boolean"/>
		<bitfield name="DISABLE_SAMPLE_COUNTERS" pos="16" type="boolean"/>
		<bitfield name="DISABLE_ACCUM" pos="17" type="boolean"/>
		<bitfield name="ACCUM_ALLOC_MASK" low="18" high="21" type="uint"/>
		<bitfield name="LINEAR_PERFORMANCE_ENABLE" pos="22" type="boolean"/>
		<bitfield name="ACCUM_DATA_FIFO_LIMIT" low="23" high="26" type="uint"/>
		<bitfield name="MEM_EXPORT_TIMEOUT_SELECT" low="27" high="28" type="uint"/>
		<bitfield name="MEM_EXPORT_LINEAR_MODE_ENABLE" pos="29" type="boolean"/>
		<bitfield name="CRC_SYSTEM" pos="30" type="boolean"/>
		<bitfield name="RESERVED6" pos="31" type="boolean"/>
	</reg32>
	<reg32 offset="0x0f02" name="RB_EDRAM_INFO"/>
	<reg32 offset="0x0f26" name="RB_DEBUG_CNTL"/>
	<reg32 offset="0x0f27" name="RB_DEBUG_DATA"/>
	<reg32 offset="0x2000" name="RB_SURFACE_INFO">
		<bitfield name="SURFACE_PITCH" low="0" high="13" type="uint"/>
		<bitfield name="MSAA_SAMPLES" low="14" high="15" type="uint"/>
	</reg32>
	<reg32 offset="0x2001" name="RB_COLOR_INFO">
		<bitfield name="FORMAT" low="0" high="3" type="a2xx_colorformatx"/>
		<bitfield name="ROUND_MODE" low="4" high="5" type="uint"/>
		<bitfield name="LINEAR" pos="6" type="boolean"/>
		<bitfield name="ENDIAN" low="7" high="8" type="uint"/>
		<bitfield name="SWAP" low="9" high="10" type="uint"/>
		<bitfield name="BASE" low="12" high="31" shr="12"/>
	</reg32>
	<reg32 offset="0x2002" name="RB_DEPTH_INFO">
		<bitfield name="DEPTH_FORMAT" pos="0" type="adreno_rb_depth_format"/>
		<bitfield name="DEPTH_BASE" low="12" high="31" type="uint" shr="12"/>
	</reg32>
	<reg32 offset="0x2005" name="A225_RB_COLOR_INFO3"/>
	<reg32 offset="0x2006" name="COHER_DEST_BASE_0"/>
	<reg32 offset="0x200e" name="PA_SC_SCREEN_SCISSOR_TL" type="adreno_reg_xy"/>
	<reg32 offset="0x200f" name="PA_SC_SCREEN_SCISSOR_BR" type="adreno_reg_xy"/>
	<reg32 offset="0x2080" name="PA_SC_WINDOW_OFFSET">
		<bitfield name="X" low="0" high="14" type="int"/>
		<bitfield name="Y" low="16" high="30" type="int"/>
		<bitfield name="DISABLE" pos="31" type="boolean"/>
	</reg32>
	<reg32 offset="0x2081" name="PA_SC_WINDOW_SCISSOR_TL" type="adreno_reg_xy"/>
	<reg32 offset="0x2082" name="PA_SC_WINDOW_SCISSOR_BR" type="adreno_reg_xy"/>
	<reg32 offset="0x2010" name="UNKNOWN_2010"/>
	<reg32 offset="0x2100" name="VGT_MAX_VTX_INDX"/>
	<reg32 offset="0x2101" name="VGT_MIN_VTX_INDX"/>
	<reg32 offset="0x2102" name="VGT_INDX_OFFSET"/>
	<reg32 offset="0x2103" name="A225_PC_MULTI_PRIM_IB_RESET_INDX"/>
	<reg32 offset="0x2104" name="RB_COLOR_MASK">
		<bitfield name="WRITE_RED" pos="0" type="boolean"/>
		<bitfield name="WRITE_GREEN" pos="1" type="boolean"/>
		<bitfield name="WRITE_BLUE" pos="2" type="boolean"/>
		<bitfield name="WRITE_ALPHA" pos="3" type="boolean"/>
	</reg32>
	<reg32 offset="0x2105" name="RB_BLEND_RED"/>
	<reg32 offset="0x2106" name="RB_BLEND_GREEN"/>
	<reg32 offset="0x2107" name="RB_BLEND_BLUE"/>
	<reg32 offset="0x2108" name="RB_BLEND_ALPHA"/>
	<reg32 offset="0x2109" name="RB_FOG_COLOR">
		<bitfield name="FOG_RED" low="0" high="7" type="uint"/>
		<bitfield name="FOG_GREEN" low="8" high="15" type="uint"/>
		<bitfield name="FOG_BLUE" low="16" high="23" type="uint"/>
	</reg32>
	<reg32 offset="0x210c" name="RB_STENCILREFMASK_BF" type="adreno_rb_stencilrefmask"/>
	<reg32 offset="0x210d" name="RB_STENCILREFMASK" type="adreno_rb_stencilrefmask"/>
	<reg32 offset="0x210e" name="RB_ALPHA_REF"/>
	<reg32 offset="0x210f" name="PA_CL_VPORT_XSCALE" type="float"/>
	<reg32 offset="0x2110" name="PA_CL_VPORT_XOFFSET" type="float"/>
	<reg32 offset="0x2111" name="PA_CL_VPORT_YSCALE" type="float"/>
	<reg32 offset="0x2112" name="PA_CL_VPORT_YOFFSET" type="float"/>
	<reg32 offset="0x2113" name="PA_CL_VPORT_ZSCALE" type="float"/>
	<reg32 offset="0x2114" name="PA_CL_VPORT_ZOFFSET" type="float"/>
	<reg32 offset="0x2180" name="SQ_PROGRAM_CNTL">
		<doc>
			note: only 0x3f worth of valid register values for VS_REGS and
			PS_REGS, but high bit is set to indicate '0 registers used':
		</doc>
		<bitfield name="VS_REGS" low="0" high="7" type="uint"/>
		<bitfield name="PS_REGS" low="8" high="15" type="uint"/>
		<bitfield name="VS_RESOURCE" pos="16" type="boolean"/>
		<bitfield name="PS_RESOURCE" pos="17" type="boolean"/>
		<bitfield name="PARAM_GEN" pos="18" type="boolean"/>
		<bitfield name="GEN_INDEX_PIX" pos="19" type="boolean"/>
		<bitfield name="VS_EXPORT_COUNT" low="20" high="23" type="uint"/>
		<bitfield name="VS_EXPORT_MODE" low="24" high="26" type="a2xx_sq_ps_vtx_mode"/>
		<bitfield name="PS_EXPORT_MODE" low="27" high="30" type="uint"/>
		<bitfield name="GEN_INDEX_VTX" pos="31" type="boolean"/>
	</reg32>
	<reg32 offset="0x2181" name="SQ_CONTEXT_MISC">
		<bitfield name="INST_PRED_OPTIMIZE" pos="0" type="boolean"/>
		<bitfield name="SC_OUTPUT_SCREEN_XY" pos="1" type="boolean"/>
		<bitfield name="SC_SAMPLE_CNTL" low="2" high="3" type="a2xx_sq_sample_cntl"/>
		<bitfield name="PARAM_GEN_POS" low="8" high="15" type="uint"/>
		<bitfield name="PERFCOUNTER_REF" pos="16" type="boolean"/>
		<bitfield name="YEILD_OPTIMIZE" pos="17" type="boolean"/>
		<bitfield name="TX_CACHE_SEL" pos="18" type="boolean"/>
	</reg32>
	<reg32 offset="0x2182" name="SQ_INTERPOLATOR_CNTL">
		<bitfield name="PARAM_SHADE" low="0" high="15" type="uint"/>
		<bitfield name="SAMPLING_PATTERN" low="16" high="31" type="uint"/>
	</reg32>
	<reg32 offset="0x2183" name="SQ_WRAPPING_0">
		<bitfield name="PARAM_WRAP_0" low="0" high="3" type="uint"/>
		<bitfield name="PARAM_WRAP_1" low="4" high="7" type="uint"/>
		<bitfield name="PARAM_WRAP_2" low="8" high="11" type="uint"/>
		<bitfield name="PARAM_WRAP_3" low="12" high="15" type="uint"/>
		<bitfield name="PARAM_WRAP_4" low="16" high="19" type="uint"/>
		<bitfield name="PARAM_WRAP_5" low="20" high="23" type="uint"/>
		<bitfield name="PARAM_WRAP_6" low="24" high="27" type="uint"/>
		<bitfield name="PARAM_WRAP_7" low="28" high="31" type="uint"/>
	</reg32>
	<reg32 offset="0x2184" name="SQ_WRAPPING_1">
		<bitfield name="PARAM_WRAP_8" low="0" high="3" type="uint"/>
		<bitfield name="PARAM_WRAP_9" low="4" high="7" type="uint"/>
		<bitfield name="PARAM_WRAP_10" low="8" high="11" type="uint"/>
		<bitfield name="PARAM_WRAP_11" low="12" high="15" type="uint"/>
		<bitfield name="PARAM_WRAP_12" low="16" high="19" type="uint"/>
		<bitfield name="PARAM_WRAP_13" low="20" high="23" type="uint"/>
		<bitfield name="PARAM_WRAP_14" low="24" high="27" type="uint"/>
		<bitfield name="PARAM_WRAP_15" low="28" high="31" type="uint"/>
	</reg32>
	<reg32 offset="0x21f6" name="SQ_PS_PROGRAM">
		<bitfield name="BASE" low="0" high="11" type="uint"/>
		<bitfield name="SIZE" low="12" high="23" type="uint"/>
	</reg32>
	<reg32 offset="0x21f7" name="SQ_VS_PROGRAM">
		<bitfield name="BASE" low="0" high="11" type="uint"/>
		<bitfield name="SIZE" low="12" high="23" type="uint"/>
	</reg32>
	<reg32 offset="0x21f9" name="VGT_EVENT_INITIATOR"/>
	<reg32 offset="0x21fc" name="VGT_DRAW_INITIATOR" type="vgt_draw_initiator"/>
	<reg32 offset="0x21fd" name="VGT_IMMED_DATA"/>
	<reg32 offset="0x2200" name="RB_DEPTHCONTROL">
		<bitfield name="STENCIL_ENABLE" pos="0" type="boolean"/>
		<bitfield name="Z_ENABLE" pos="1" type="boolean"/>
		<bitfield name="Z_WRITE_ENABLE" pos="2" type="boolean"/>
		<bitfield name="EARLY_Z_ENABLE" pos="3" type="boolean"/>
		<bitfield name="ZFUNC" low="4" high="6" type="adreno_compare_func"/>
		<bitfield name="BACKFACE_ENABLE" pos="7" type="boolean"/>
		<bitfield name="STENCILFUNC" low="8" high="10" type="adreno_compare_func"/>
		<bitfield name="STENCILFAIL" low="11" high="13" type="adreno_stencil_op"/>
		<bitfield name="STENCILZPASS" low="14" high="16" type="adreno_stencil_op"/>
		<bitfield name="STENCILZFAIL" low="17" high="19" type="adreno_stencil_op"/>
		<bitfield name="STENCILFUNC_BF" low="20" high="22" type="adreno_compare_func"/>
		<bitfield name="STENCILFAIL_BF" low="23" high="25" type="adreno_stencil_op"/>
		<bitfield name="STENCILZPASS_BF" low="26" high="28" type="adreno_stencil_op"/>
		<bitfield name="STENCILZFAIL_BF" low="29" high="31" type="adreno_stencil_op"/>
	</reg32>
	<reg32 offset="0x2201" name="RB_BLEND_CONTROL">
		<bitfield name="COLOR_SRCBLEND" low="0" high="4" type="adreno_rb_blend_factor"/>
		<bitfield name="COLOR_COMB_FCN" low="5" high="7" type="a2xx_rb_blend_opcode"/>
		<bitfield name="COLOR_DESTBLEND" low="8" high="12" type="adreno_rb_blend_factor"/>
		<bitfield name="ALPHA_SRCBLEND" low="16" high="20" type="adreno_rb_blend_factor"/>
		<bitfield name="ALPHA_COMB_FCN" low="21" high="23" type="a2xx_rb_blend_opcode"/>
		<bitfield name="ALPHA_DESTBLEND" low="24" high="28" type="adreno_rb_blend_factor"/>
		<bitfield name="BLEND_FORCE_ENABLE" pos="29" type="boolean"/>
		<bitfield name="BLEND_FORCE" pos="30" type="boolean"/>
	</reg32>
	<reg32 offset="0x2202" name="RB_COLORCONTROL">
		<bitfield name="ALPHA_FUNC" low="0" high="2" type="adreno_compare_func"/>
		<bitfield name="ALPHA_TEST_ENABLE" pos="3" type="boolean"/>
		<bitfield name="ALPHA_TO_MASK_ENABLE" pos="4" type="boolean"/>
		<bitfield name="BLEND_DISABLE" pos="5" type="boolean"/>
		<bitfield name="VOB_ENABLE" pos="6" type="boolean"/>
		<bitfield name="VS_EXPORTS_FOG" pos="7" type="boolean"/>
		<bitfield name="ROP_CODE" low="8" high="11" type="uint"/>
		<bitfield name="DITHER_MODE" low="12" high="13" type="adreno_rb_dither_mode"/>
		<bitfield name="DITHER_TYPE" low="14" high="15" type="a2xx_rb_dither_type"/>
		<bitfield name="PIXEL_FOG" pos="16" type="boolean"/>
		<bitfield name="ALPHA_TO_MASK_OFFSET0" low="24" high="25" type="uint"/>
		<bitfield name="ALPHA_TO_MASK_OFFSET1" low="26" high="27" type="uint"/>
		<bitfield name="ALPHA_TO_MASK_OFFSET2" low="28" high="29" type="uint"/>
		<bitfield name="ALPHA_TO_MASK_OFFSET3" low="30" high="31" type="uint"/>
	</reg32>
	<reg32 offset="0x2203" name="VGT_CURRENT_BIN_ID_MAX" type="a2xx_vgt_current_bin_id_min_max"/>
	<reg32 offset="0x2204" name="PA_CL_CLIP_CNTL">
		<bitfield name="CLIP_DISABLE" pos="16" type="boolean"/>
		<bitfield name="BOUNDARY_EDGE_FLAG_ENA" pos="18" type="boolean"/>
		<bitfield name="DX_CLIP_SPACE_DEF" pos="19" type="a2xx_dx_clip_space"/>
		<bitfield name="DIS_CLIP_ERR_DETECT" pos="20" type="boolean"/>
		<bitfield name="VTX_KILL_OR" pos="21" type="boolean"/>
		<bitfield name="XY_NAN_RETAIN" pos="22" type="boolean"/>
		<bitfield name="Z_NAN_RETAIN" pos="23" type="boolean"/>
		<bitfield name="W_NAN_RETAIN" pos="24" type="boolean"/>
	</reg32>
	<reg32 offset="0x2205" name="PA_SU_SC_MODE_CNTL">
		<bitfield name="CULL_FRONT" pos="0" type="boolean"/>
		<bitfield name="CULL_BACK" pos="1" type="boolean"/>
		<bitfield name="FACE" pos="2" type="boolean"/>
		<bitfield name="POLYMODE" low="3" high="4" type="a2xx_pa_su_sc_polymode"/>
		<bitfield name="FRONT_PTYPE" low="5" high="7" type="adreno_pa_su_sc_draw"/>
		<bitfield name="BACK_PTYPE" low="8" high="10" type="adreno_pa_su_sc_draw"/>
		<bitfield name="POLY_OFFSET_FRONT_ENABLE" pos="11" type="boolean"/>
		<bitfield name="POLY_OFFSET_BACK_ENABLE" pos="12" type="boolean"/>
		<bitfield name="POLY_OFFSET_PARA_ENABLE" pos="13" type="boolean"/>
		<bitfield name="MSAA_ENABLE" pos="15" type="boolean"/>
		<bitfield name="VTX_WINDOW_OFFSET_ENABLE" pos="16" type="boolean"/>
		<bitfield name="LINE_STIPPLE_ENABLE" pos="18" type="boolean"/>
		<bitfield name="PROVOKING_VTX_LAST" pos="19" type="boolean"/>
		<bitfield name="PERSP_CORR_DIS" pos="20" type="boolean"/>
		<bitfield name="MULTI_PRIM_IB_ENA" pos="21" type="boolean"/>
		<bitfield name="QUAD_ORDER_ENABLE" pos="23" type="boolean"/>
		<bitfield name="WAIT_RB_IDLE_ALL_TRI" pos="25" type="boolean"/>
		<bitfield name="WAIT_RB_IDLE_FIRST_TRI_NEW_STATE" pos="26" type="boolean"/>
		<bitfield name="CLAMPED_FACENESS" pos="28" type="boolean"/>
		<bitfield name="ZERO_AREA_FACENESS" pos="29" type="boolean"/>
		<bitfield name="FACE_KILL_ENABLE" pos="30" type="boolean"/>
		<bitfield name="FACE_WRITE_ENABLE" pos="31" type="boolean"/>
	</reg32>
	<reg32 offset="0x2206" name="PA_CL_VTE_CNTL">
		<bitfield name="VPORT_X_SCALE_ENA" pos="0" type="boolean"/>
		<bitfield name="VPORT_X_OFFSET_ENA" pos="1" type="boolean"/>
		<bitfield name="VPORT_Y_SCALE_ENA" pos="2" type="boolean"/>
		<bitfield name="VPORT_Y_OFFSET_ENA" pos="3" type="boolean"/>
		<bitfield name="VPORT_Z_SCALE_ENA" pos="4" type="boolean"/>
		<bitfield name="VPORT_Z_OFFSET_ENA" pos="5" type="boolean"/>
		<bitfield name="VTX_XY_FMT" pos="8" type="boolean"/>
		<bitfield name="VTX_Z_FMT" pos="9" type="boolean"/>
		<bitfield name="VTX_W0_FMT" pos="10" type="boolean"/>
		<bitfield name="PERFCOUNTER_REF" pos="11" type="boolean"/>
	</reg32>
	<reg32 offset="0x2207" name="VGT_CURRENT_BIN_ID_MIN" type="a2xx_vgt_current_bin_id_min_max"/>
	<reg32 offset="0x2208" name="RB_MODECONTROL">
		<bitfield name="EDRAM_MODE" low="0" high="2" type="a2xx_rb_edram_mode"/>
	</reg32>
	<reg32 offset="0x2209" name="A220_RB_LRZ_VSC_CONTROL"/>
	<reg32 offset="0x220a" name="RB_SAMPLE_POS"/>
	<reg32 offset="0x220b" name="CLEAR_COLOR">
		<bitfield name="RED" low="0" high="7"/>
		<bitfield name="GREEN" low="8" high="15"/>
		<bitfield name="BLUE" low="16" high="23"/>
		<bitfield name="ALPHA" low="24" high="31"/>
	</reg32>
	<reg32 offset="0x2210" name="A220_GRAS_CONTROL"/>
	<reg32 offset="0x2280" name="PA_SU_POINT_SIZE">
		<bitfield name="HEIGHT" low="0" high="15" type="ufixed" radix="4"/>
		<bitfield name="WIDTH" low="16" high="31" type="ufixed" radix="4"/>
	</reg32>
	<reg32 offset="0x2281" name="PA_SU_POINT_MINMAX">
		<bitfield name="MIN" low="0" high="15" type="ufixed" radix="4"/>
		<bitfield name="MAX" low="16" high="31" type="ufixed" radix="4"/>
	</reg32>
	<reg32 offset="0x2282" name="PA_SU_LINE_CNTL">
		<bitfield name="WIDTH" low="0" high="15" type="ufixed" radix="4"/>
	</reg32>
	<reg32 offset="0x2283" name="PA_SC_LINE_STIPPLE">
		<bitfield name="LINE_PATTERN" low="0" high="15" type="hex"/>
		<bitfield name="REPEAT_COUNT" low="16" high="23" type="uint"/>
		<bitfield name="PATTERN_BIT_ORDER" pos="28" type="a2xx_pa_sc_pattern_bit_order"/>
		<bitfield name="AUTO_RESET_CNTL" low="29" high="30" type="a2xx_pa_sc_auto_reset_cntl"/>
	</reg32>
	<reg32 offset="0x2293" name="PA_SC_VIZ_QUERY">
		<bitfield name="VIZ_QUERY_ENA" pos="0" type="boolean"/>
		<bitfield name="VIZ_QUERY_ID" low="1" high="6" type="uint"/>
		<bitfield name="KILL_PIX_POST_EARLY_Z" pos="8" type="boolean"/>
	</reg32>
	<reg32 offset="0x2294" name="VGT_ENHANCE"/>
	<reg32 offset="0x2300" name="PA_SC_LINE_CNTL">
		<bitfield name="BRES_CNTL" low="0" high="15" type="uint"/>
		<bitfield name="USE_BRES_CNTL" pos="8" type="boolean"/>
		<bitfield name="EXPAND_LINE_WIDTH" pos="9" type="boolean"/>
		<bitfield name="LAST_PIXEL" pos="10" type="boolean"/>
	</reg32>
	<reg32 offset="0x2301" name="PA_SC_AA_CONFIG">
		<bitfield name="MSAA_NUM_SAMPLES" low="0" high="2" type="uint"/>
		<bitfield name="MAX_SAMPLE_DIST" low="13" high="16" type="uint"/>
	</reg32>
	<reg32 offset="0x2302" name="PA_SU_VTX_CNTL">
		<bitfield name="PIX_CENTER" pos="0" type="a2xx_pa_pixcenter"/>
		<bitfield name="ROUND_MODE" low="1" high="2" type="a2xx_pa_roundmode"/>
		<bitfield name="QUANT_MODE" low="7" high="9" type="a2xx_pa_quantmode"/>
	</reg32>
	<reg32 offset="0x2303" name="PA_CL_GB_VERT_CLIP_ADJ" type="float"/>
	<reg32 offset="0x2304" name="PA_CL_GB_VERT_DISC_ADJ" type="float"/>
	<reg32 offset="0x2305" name="PA_CL_GB_HORZ_CLIP_ADJ" type="float"/>
	<reg32 offset="0x2306" name="PA_CL_GB_HORZ_DISC_ADJ" type="float"/>
	<reg32 offset="0x2307" name="SQ_VS_CONST">
		<bitfield name="BASE" low="0" high="8" type="uint"/>
		<bitfield name="SIZE" low="12" high="20" type="uint"/>
	</reg32>
	<reg32 offset="0x2308" name="SQ_PS_CONST">
		<bitfield name="BASE" low="0" high="8" type="uint"/>
		<bitfield name="SIZE" low="12" high="20" type="uint"/>
	</reg32>
	<reg32 offset="0x2309" name="SQ_DEBUG_MISC_0"/>
	<reg32 offset="0x230a" name="SQ_DEBUG_MISC_1"/>
	<reg32 offset="0x2312" name="PA_SC_AA_MASK"/>
	<reg32 offset="0x2316" name="VGT_VERTEX_REUSE_BLOCK_CNTL">
		<bitfield name="VTX_REUSE_DEPTH" low="0" high="2" type="uint"/>
	</reg32>
	<reg32 offset="0x2317" name="VGT_OUT_DEALLOC_CNTL">
		<bitfield name="DEALLOC_DIST" low="0" high="1" type="uint"/>
	</reg32>
	<reg32 offset="0x2318" name="RB_COPY_CONTROL">
		<bitfield name="COPY_SAMPLE_SELECT" low="0" high="2" type="a2xx_rb_copy_sample_select"/>
		<bitfield name="DEPTH_CLEAR_ENABLE" pos="3" type="boolean"/>
		<bitfield name="CLEAR_MASK" low="4" high="7" type="hex"/>
	</reg32>
	<reg32 offset="0x2319" name="RB_COPY_DEST_BASE"/>
	<reg32 offset="0x231a" name="RB_COPY_DEST_PITCH" shr="5" type="uint"/>
	<reg32 offset="0x231b" name="RB_COPY_DEST_INFO">
		<bitfield name="DEST_ENDIAN" low="0" high="2" type="adreno_rb_surface_endian"/>
		<bitfield name="LINEAR" pos="3" type="boolean"/>
		<bitfield name="FORMAT" low="4" high="7" type="a2xx_colorformatx"/>
		<bitfield name="SWAP" low="8" high="9" type="uint"/>
		<bitfield name="DITHER_MODE" low="10" high="11" type="adreno_rb_dither_mode"/>
		<bitfield name="DITHER_TYPE" low="12" high="13" type="a2xx_rb_dither_type"/>
		<bitfield name="WRITE_RED" pos="14" type="boolean"/>
		<bitfield name="WRITE_GREEN" pos="15" type="boolean"/>
		<bitfield name="WRITE_BLUE" pos="16" type="boolean"/>
		<bitfield name="WRITE_ALPHA" pos="17" type="boolean"/>
	</reg32>
	<reg32 offset="0x231c" name="RB_COPY_DEST_OFFSET">
		<bitfield name="X" low="0" high="12" type="uint"/>
		<bitfield name="Y" low="13" high="25" type="uint"/>
	</reg32>
	<reg32 offset="0x231d" name="RB_DEPTH_CLEAR"/>
	<reg32 offset="0x2324" name="RB_SAMPLE_COUNT_CTL"/>
	<reg32 offset="0x2326" name="RB_COLOR_DEST_MASK"/>
	<reg32 offset="0x2340" name="A225_GRAS_UCP0X"/>
	<reg32 offset="0x2357" name="A225_GRAS_UCP5W"/>
	<reg32 offset="0x2360" name="A225_GRAS_UCP_ENABLED"/>
	<reg32 offset="0x2380" name="PA_SU_POLY_OFFSET_FRONT_SCALE"/>
	<reg32 offset="0x2381" name="PA_SU_POLY_OFFSET_FRONT_OFFSET"/>
	<reg32 offset="0x2382" name="PA_SU_POLY_OFFSET_BACK_SCALE"/>
	<reg32 offset="0x2383" name="PA_SU_POLY_OFFSET_BACK_OFFSET"/>
	<reg32 offset="0x4000" name="SQ_CONSTANT_0"/>
	<reg32 offset="0x4800" name="SQ_FETCH_0"/>
	<reg32 offset="0x4900" name="SQ_CF_BOOLEANS"/>
	<reg32 offset="0x4908" name="SQ_CF_LOOP"/>
	<reg32 offset="0xa29" name="COHER_SIZE_PM4"/>
	<reg32 offset="0xa2a" name="COHER_BASE_PM4"/>
	<reg32 offset="0xa2b" name="COHER_STATUS_PM4"/>

	<reg32 offset="0x0c88" name="PA_SU_PERFCOUNTER0_SELECT"/>
	<reg32 offset="0x0c89" name="PA_SU_PERFCOUNTER1_SELECT"/>
	<reg32 offset="0x0c8a" name="PA_SU_PERFCOUNTER2_SELECT"/>
	<reg32 offset="0x0c8b" name="PA_SU_PERFCOUNTER3_SELECT"/>
	<reg32 offset="0x0c8c" name="PA_SU_PERFCOUNTER0_LOW"/>
	<reg32 offset="0x0c8d" name="PA_SU_PERFCOUNTER0_HI"/>
	<reg32 offset="0x0c8e" name="PA_SU_PERFCOUNTER1_LOW"/>
	<reg32 offset="0x0c8f" name="PA_SU_PERFCOUNTER1_HI"/>
	<reg32 offset="0x0c90" name="PA_SU_PERFCOUNTER2_LOW"/>
	<reg32 offset="0x0c91" name="PA_SU_PERFCOUNTER2_HI"/>
	<reg32 offset="0x0c92" name="PA_SU_PERFCOUNTER3_LOW"/>
	<reg32 offset="0x0c93" name="PA_SU_PERFCOUNTER3_HI"/>
	<reg32 offset="0x0c98" name="PA_SC_PERFCOUNTER0_SELECT"/>
	<reg32 offset="0x0c99" name="PA_SC_PERFCOUNTER0_LOW"/>
	<reg32 offset="0x0c9a" name="PA_SC_PERFCOUNTER0_HI"/>
	<reg32 offset="0x0c48" name="VGT_PERFCOUNTER0_SELECT"/>
	<reg32 offset="0x0c49" name="VGT_PERFCOUNTER1_SELECT"/>
	<reg32 offset="0x0c4a" name="VGT_PERFCOUNTER2_SELECT"/>
	<reg32 offset="0x0c4b" name="VGT_PERFCOUNTER3_SELECT"/>
	<reg32 offset="0x0c4c" name="VGT_PERFCOUNTER0_LOW"/>
	<reg32 offset="0x0c4e" name="VGT_PERFCOUNTER1_LOW"/>
	<reg32 offset="0x0c50" name="VGT_PERFCOUNTER2_LOW"/>
	<reg32 offset="0x0c52" name="VGT_PERFCOUNTER3_LOW"/>
	<reg32 offset="0x0c4d" name="VGT_PERFCOUNTER0_HI"/>
	<reg32 offset="0x0c4f" name="VGT_PERFCOUNTER1_HI"/>
	<reg32 offset="0x0c51" name="VGT_PERFCOUNTER2_HI"/>
	<reg32 offset="0x0c53" name="VGT_PERFCOUNTER3_HI"/>
	<reg32 offset="0x0e05" name="TCR_PERFCOUNTER0_SELECT"/>
	<reg32 offset="0x0e08" name="TCR_PERFCOUNTER1_SELECT"/>
	<reg32 offset="0x0e06" name="TCR_PERFCOUNTER0_HI"/>
	<reg32 offset="0x0e09" name="TCR_PERFCOUNTER1_HI"/>
	<reg32 offset="0x0e07" name="TCR_PERFCOUNTER0_LOW"/>
	<reg32 offset="0x0e0a" name="TCR_PERFCOUNTER1_LOW"/>
	<reg32 offset="0x0e1f" name="TP0_PERFCOUNTER0_SELECT"/>
	<reg32 offset="0x0e20" name="TP0_PERFCOUNTER0_HI"/>
	<reg32 offset="0x0e21" name="TP0_PERFCOUNTER0_LOW"/>
	<reg32 offset="0x0e22" name="TP0_PERFCOUNTER1_SELECT"/>
	<reg32 offset="0x0e23" name="TP0_PERFCOUNTER1_HI"/>
	<reg32 offset="0x0e24" name="TP0_PERFCOUNTER1_LOW"/>
	<reg32 offset="0x0e54" name="TCM_PERFCOUNTER0_SELECT"/>
	<reg32 offset="0x0e57" name="TCM_PERFCOUNTER1_SELECT"/>
	<reg32 offset="0x0e55" name="TCM_PERFCOUNTER0_HI"/>
	<reg32 offset="0x0e58" name="TCM_PERFCOUNTER1_HI"/>
	<reg32 offset="0x0e56" name="TCM_PERFCOUNTER0_LOW"/>
	<reg32 offset="0x0e59" name="TCM_PERFCOUNTER1_LOW"/>
	<reg32 offset="0x0e5a" name="TCF_PERFCOUNTER0_SELECT"/>
	<reg32 offset="0x0e5d" name="TCF_PERFCOUNTER1_SELECT"/>
	<reg32 offset="0x0e60" name="TCF_PERFCOUNTER2_SELECT"/>
	<reg32 offset="0x0e63" name="TCF_PERFCOUNTER3_SELECT"/>
	<reg32 offset="0x0e66" name="TCF_PERFCOUNTER4_SELECT"/>
	<reg32 offset="0x0e69" name="TCF_PERFCOUNTER5_SELECT"/>
	<reg32 offset="0x0e6c" name="TCF_PERFCOUNTER6_SELECT"/>
	<reg32 offset="0x0e6f" name="TCF_PERFCOUNTER7_SELECT"/>
	<reg32 offset="0x0e72" name="TCF_PERFCOUNTER8_SELECT"/>
	<reg32 offset="0x0e75" name="TCF_PERFCOUNTER9_SELECT"/>
	<reg32 offset="0x0e78" name="TCF_PERFCOUNTER10_SELECT"/>
	<reg32 offset="0x0e7b" name="TCF_PERFCOUNTER11_SELECT"/>
	<reg32 offset="0x0e5b" name="TCF_PERFCOUNTER0_HI"/>
	<reg32 offset="0x0e5e" name="TCF_PERFCOUNTER1_HI"/>
	<reg32 offset="0x0e61" name="TCF_PERFCOUNTER2_HI"/>
	<reg32 offset="0x0e64" name="TCF_PERFCOUNTER3_HI"/>
	<reg32 offset="0x0e67" name="TCF_PERFCOUNTER4_HI"/>
	<reg32 offset="0x0e6a" name="TCF_PERFCOUNTER5_HI"/>
	<reg32 offset="0x0e6d" name="TCF_PERFCOUNTER6_HI"/>
	<reg32 offset="0x0e70" name="TCF_PERFCOUNTER7_HI"/>
	<reg32 offset="0x0e73" name="TCF_PERFCOUNTER8_HI"/>
	<reg32 offset="0x0e76" name="TCF_PERFCOUNTER9_HI"/>
	<reg32 offset="0x0e79" name="TCF_PERFCOUNTER10_HI"/>
	<reg32 offset="0x0e7c" name="TCF_PERFCOUNTER11_HI"/>
	<reg32 offset="0x0e5c" name="TCF_PERFCOUNTER0_LOW"/>
	<reg32 offset="0x0e5f" name="TCF_PERFCOUNTER1_LOW"/>
	<reg32 offset="0x0e62" name="TCF_PERFCOUNTER2_LOW"/>
	<reg32 offset="0x0e65" name="TCF_PERFCOUNTER3_LOW"/>
	<reg32 offset="0x0e68" name="TCF_PERFCOUNTER4_LOW"/>
	<reg32 offset="0x0e6b" name="TCF_PERFCOUNTER5_LOW"/>
	<reg32 offset="0x0e6e" name="TCF_PERFCOUNTER6_LOW"/>
	<reg32 offset="0x0e71" name="TCF_PERFCOUNTER7_LOW"/>
	<reg32 offset="0x0e74" name="TCF_PERFCOUNTER8_LOW"/>
	<reg32 offset="0x0e77" name="TCF_PERFCOUNTER9_LOW"/>
	<reg32 offset="0x0e7a" name="TCF_PERFCOUNTER10_LOW"/>
	<reg32 offset="0x0e7d" name="TCF_PERFCOUNTER11_LOW"/>
	<reg32 offset="0x0dc8" name="SQ_PERFCOUNTER0_SELECT"/>
	<reg32 offset="0x0dc9" name="SQ_PERFCOUNTER1_SELECT"/>
	<reg32 offset="0x0dca" name="SQ_PERFCOUNTER2_SELECT"/>
	<reg32 offset="0x0dcb" name="SQ_PERFCOUNTER3_SELECT"/>
	<reg32 offset="0x0dcc" name="SQ_PERFCOUNTER0_LOW"/>
	<reg32 offset="0x0dcd" name="SQ_PERFCOUNTER0_HI"/>
	<reg32 offset="0x0dce" name="SQ_PERFCOUNTER1_LOW"/>
	<reg32 offset="0x0dcf" name="SQ_PERFCOUNTER1_HI"/>
	<reg32 offset="0x0dd0" name="SQ_PERFCOUNTER2_LOW"/>
	<reg32 offset="0x0dd1" name="SQ_PERFCOUNTER2_HI"/>
	<reg32 offset="0x0dd2" name="SQ_PERFCOUNTER3_LOW"/>
	<reg32 offset="0x0dd3" name="SQ_PERFCOUNTER3_HI"/>
	<reg32 offset="0x0dd4" name="SX_PERFCOUNTER0_SELECT"/>
	<reg32 offset="0x0dd8" name="SX_PERFCOUNTER0_LOW"/>
	<reg32 offset="0x0dd9" name="SX_PERFCOUNTER0_HI"/>
	<reg32 offset="0x0a46" name="MH_PERFCOUNTER0_SELECT"/>
	<reg32 offset="0x0a4a" name="MH_PERFCOUNTER1_SELECT"/>
	<reg32 offset="0x0a47" name="MH_PERFCOUNTER0_CONFIG"/>
	<reg32 offset="0x0a4b" name="MH_PERFCOUNTER1_CONFIG"/>
	<reg32 offset="0x0a48" name="MH_PERFCOUNTER0_LOW"/>
	<reg32 offset="0x0a4c" name="MH_PERFCOUNTER1_LOW"/>
	<reg32 offset="0x0a49" name="MH_PERFCOUNTER0_HI"/>
	<reg32 offset="0x0a4d" name="MH_PERFCOUNTER1_HI"/>
	<reg32 offset="0x0f04" name="RB_PERFCOUNTER0_SELECT"/>
	<reg32 offset="0x0f05" name="RB_PERFCOUNTER1_SELECT"/>
	<reg32 offset="0x0f06" name="RB_PERFCOUNTER2_SELECT"/>
	<reg32 offset="0x0f07" name="RB_PERFCOUNTER3_SELECT"/>
	<reg32 offset="0x0f08" name="RB_PERFCOUNTER0_LOW"/>
	<reg32 offset="0x0f09" name="RB_PERFCOUNTER0_HI"/>
	<reg32 offset="0x0f0a" name="RB_PERFCOUNTER1_LOW"/>
	<reg32 offset="0x0f0b" name="RB_PERFCOUNTER1_HI"/>
	<reg32 offset="0x0f0c" name="RB_PERFCOUNTER2_LOW"/>
	<reg32 offset="0x0f0d" name="RB_PERFCOUNTER2_HI"/>
	<reg32 offset="0x0f0e" name="RB_PERFCOUNTER3_LOW"/>
	<reg32 offset="0x0f0f" name="RB_PERFCOUNTER3_HI"/>
</domain>

<domain name="A2XX_SQ_TEX" width="32">
	<doc>Texture state dwords</doc>
	<enum name="sq_tex_clamp">
		<value name="SQ_TEX_WRAP" value="0"/>
		<value name="SQ_TEX_MIRROR" value="1"/>
		<value name="SQ_TEX_CLAMP_LAST_TEXEL" value="2"/>
		<value name="SQ_TEX_MIRROR_ONCE_LAST_TEXEL" value="3"/>
		<value name="SQ_TEX_CLAMP_HALF_BORDER" value="4"/>
		<value name="SQ_TEX_MIRROR_ONCE_HALF_BORDER" value="5"/>
		<value name="SQ_TEX_CLAMP_BORDER" value="6"/>
		<value name="SQ_TEX_MIRROR_ONCE_BORDER" value="7"/>
	</enum>
	<enum name="sq_tex_swiz">
		<value name="SQ_TEX_X" value="0"/>
		<value name="SQ_TEX_Y" value="1"/>
		<value name="SQ_TEX_Z" value="2"/>
		<value name="SQ_TEX_W" value="3"/>
		<value name="SQ_TEX_ZERO" value="4"/>
		<value name="SQ_TEX_ONE" value="5"/>
	</enum>
	<enum name="sq_tex_filter">
		<value name="SQ_TEX_FILTER_POINT" value="0"/>
		<value name="SQ_TEX_FILTER_BILINEAR" value="1"/>
		<value name="SQ_TEX_FILTER_BASEMAP" value="2"/>
		<value name="SQ_TEX_FILTER_USE_FETCH_CONST" value="3"/>
	</enum>
	<enum name="sq_tex_aniso_filter">
		<value name="SQ_TEX_ANISO_FILTER_DISABLED" value="0"/>
		<value name="SQ_TEX_ANISO_FILTER_MAX_1_1" value="1"/>
		<value name="SQ_TEX_ANISO_FILTER_MAX_2_1" value="2"/>
		<value name="SQ_TEX_ANISO_FILTER_MAX_4_1" value="3"/>
		<value name="SQ_TEX_ANISO_FILTER_MAX_8_1" value="4"/>
		<value name="SQ_TEX_ANISO_FILTER_MAX_16_1" value="5"/>
		<value name="SQ_TEX_ANISO_FILTER_USE_FETCH_CONST" value="7"/>
	</enum>
	<enum name="sq_tex_dimension">
		<value name="SQ_TEX_DIMENSION_1D" value="0"/>
		<value name="SQ_TEX_DIMENSION_2D" value="1"/>
		<value name="SQ_TEX_DIMENSION_3D" value="2"/>
		<value name="SQ_TEX_DIMENSION_CUBE" value="3"/>
	</enum>
	<enum name="sq_tex_border_color">
		<value name="SQ_TEX_BORDER_COLOR_BLACK" value="0"/>
		<value name="SQ_TEX_BORDER_COLOR_WHITE" value="1"/>
		<value name="SQ_TEX_BORDER_COLOR_ACBYCR_BLACK" value="2"/>
		<value name="SQ_TEX_BORDER_COLOR_ACBCRY_BLACK" value="3"/>
	</enum>
	<enum name="sq_tex_sign">
		<value name="SQ_TEX_SIGN_UNSIGNED" value="0"/>
		<value name="SQ_TEX_SIGN_SIGNED" value="1"/>
		<!-- biased: 2*color-1 (range -1,1 when sampling) -->
		<value name="SQ_TEX_SIGN_UNSIGNED_BIASED" value="2"/>
		<!-- gamma: sRGB to linear - doesn't seem to work on adreno? -->
		<value name="SQ_TEX_SIGN_GAMMA" value="3"/>
	</enum>
	<enum name="sq_tex_endian">
		<value name="SQ_TEX_ENDIAN_NONE" value="0"/>
		<value name="SQ_TEX_ENDIAN_8IN16" value="1"/>
		<value name="SQ_TEX_ENDIAN_8IN32" value="2"/>
		<value name="SQ_TEX_ENDIAN_16IN32" value="3"/>
	</enum>
	<enum name="sq_tex_clamp_policy">
		<value name="SQ_TEX_CLAMP_POLICY_D3D" value="0"/>
		<value name="SQ_TEX_CLAMP_POLICY_OGL" value="1"/>
	</enum>
	<enum name="sq_tex_num_format">
		<value name="SQ_TEX_NUM_FORMAT_FRAC" value="0"/>
		<value name="SQ_TEX_NUM_FORMAT_INT" value="1"/>
	</enum>
	<enum name="sq_tex_type">
		<value name="SQ_TEX_TYPE_0" value="0"/>
		<value name="SQ_TEX_TYPE_1" value="1"/>
		<value name="SQ_TEX_TYPE_2" value="2"/>
		<value name="SQ_TEX_TYPE_3" value="3"/>
	</enum>
	<reg32 offset="0" name="0">
		<bitfield name="TYPE" low="0" high="1" type="sq_tex_type"/>
		<bitfield name="SIGN_X" low="2" high="3" type="sq_tex_sign"/>
		<bitfield name="SIGN_Y" low="4" high="5" type="sq_tex_sign"/>
		<bitfield name="SIGN_Z" low="6" high="7" type="sq_tex_sign"/>
		<bitfield name="SIGN_W" low="8" high="9" type="sq_tex_sign"/>
		<bitfield name="CLAMP_X" low="10" high="12" type="sq_tex_clamp"/>
		<bitfield name="CLAMP_Y" low="13" high="15" type="sq_tex_clamp"/>
		<bitfield name="CLAMP_Z" low="16" high="18" type="sq_tex_clamp"/>
		<bitfield name="PITCH" low="22" high="30" shr="5" type="uint"/>
		<bitfield name="TILED" pos="31" type="boolean"/>
	</reg32>
	<reg32 offset="1" name="1">
		<bitfield name="FORMAT" low="0" high="5" type="a2xx_sq_surfaceformat"/>
		<bitfield name="ENDIANNESS" low="6" high="7" type="sq_tex_endian"/>
		<bitfield name="REQUEST_SIZE" low="8" high="9" type="uint"/>
		<bitfield name="STACKED" pos="10" type="boolean"/>
		<bitfield name="CLAMP_POLICY" pos="11" type="sq_tex_clamp_policy"/>
		<bitfield name="BASE_ADDRESS" low="12" high="31" type="uint" shr="12"/>
	</reg32>
	<reg32 offset="2" name="2">
		<bitfield name="WIDTH" low="0" high="12" type="uint"/>
		<bitfield name="HEIGHT" low="13" high="25" type="uint"/>
		<bitfield name="DEPTH" low="26" high="31" type="uint"/>
		<!-- 1d/3d have different bit configurations -->
	</reg32>
	<reg32 offset="3" name="3">
		<bitfield name="NUM_FORMAT" pos="0" type="sq_tex_num_format"/>
		<bitfield name="SWIZ_X" low="1" high="3" type="sq_tex_swiz"/>
		<bitfield name="SWIZ_Y" low="4" high="6" type="sq_tex_swiz"/>
		<bitfield name="SWIZ_Z" low="7" high="9" type="sq_tex_swiz"/>
		<bitfield name="SWIZ_W" low="10" high="12" type="sq_tex_swiz"/>
		<bitfield name="EXP_ADJUST" low="13" high="18" type="int"/>
		<bitfield name="XY_MAG_FILTER" low="19" high="20" type="sq_tex_filter"/>
		<bitfield name="XY_MIN_FILTER" low="21" high="22" type="sq_tex_filter"/>
		<bitfield name="MIP_FILTER" low="23" high="24" type="sq_tex_filter"/>
		<bitfield name="ANISO_FILTER" low="25" high="27" type="sq_tex_aniso_filter"/>
		<bitfield name="BORDER_SIZE" pos="31" type="uint"/>
	</reg32>
	<reg32 offset="4" name="4">
		<bitfield name="VOL_MAG_FILTER" pos="0" type="sq_tex_filter"/>
		<bitfield name="VOL_MIN_FILTER" pos="1" type="sq_tex_filter"/>
		<bitfield name="MIP_MIN_LEVEL" low="2" high="5" type="uint"/>
		<bitfield name="MIP_MAX_LEVEL" low="6" high="9" type="uint"/>
		<bitfield name="MAX_ANISO_WALK" pos="10" type="boolean"/>
		<bitfield name="MIN_ANISO_WALK" pos="11" type="boolean"/>
		<bitfield name="LOD_BIAS" low="12" high="21" type="fixed" radix="5"/>
		<bitfield name="GRAD_EXP_ADJUST_H" low="22" high="26" type="uint"/>
		<bitfield name="GRAD_EXP_ADJUST_V" low="27" high="31" type="uint"/>
	</reg32>
	<reg32 offset="5" name="5">
		<bitfield name="BORDER_COLOR" low="0" high="1" type="sq_tex_border_color"/>
		<bitfield name="FORCE_BCW_MAX" pos="2" type="boolean"/>
		<bitfield name="TRI_CLAMP" low="3" high="4" type="uint"/>
		<bitfield name="ANISO_BIAS" low="5" high="8" type="fixed" radix="0"/> <!-- radix unknown -->
		<bitfield name="DIMENSION" low="9" high="10" type="sq_tex_dimension"/>
		<bitfield name="PACKED_MIPS" pos="11" type="boolean"/>
		<bitfield name="MIP_ADDRESS" low="12" high="31" type="uint" shr="12"/>
	</reg32>
</domain>

</database>
