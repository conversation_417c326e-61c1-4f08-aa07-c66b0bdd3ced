<?xml version="1.0" encoding="UTF-8"?>
<database xmlns="http://nouveau.freedesktop.org/"
xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
xsi:schemaLocation="https://gitlab.freedesktop.org/freedreno/ rules-fd.xsd">
<import file="freedreno_copyright.xml"/>

<enum name="chip" bare="yes">
	<value name="A2XX" value="2"/>
	<value name="A3XX" value="3"/>
	<value name="A4XX" value="4"/>
	<value name="A5XX" value="5"/>
	<value name="A6XX" value="6"/>
	<value name="A7XX" value="7"/>
</enum>

<enum name="adreno_pa_su_sc_draw">
	<value name="PC_DRAW_POINTS" value="0"/>
	<value name="PC_DRAW_LINES" value="1"/>
	<value name="PC_DRAW_TRIANGLES" value="2"/>
</enum>

<enum name="adreno_compare_func">
	<value name="FUNC_NEVER" value="0"/>
	<value name="FUNC_LESS" value="1"/>
	<value name="FUNC_EQUAL" value="2"/>
	<value name="FUNC_LEQUAL" value="3"/>
	<value name="FUNC_GREATER" value="4"/>
	<value name="FUNC_NOTEQUAL" value="5"/>
	<value name="FUNC_GEQUAL" value="6"/>
	<value name="FUNC_ALWAYS" value="7"/>
</enum>

<enum name="adreno_stencil_op">
	<value name="STENCIL_KEEP" value="0"/>
	<value name="STENCIL_ZERO" value="1"/>
	<value name="STENCIL_REPLACE" value="2"/>
	<value name="STENCIL_INCR_CLAMP" value="3"/>
	<value name="STENCIL_DECR_CLAMP" value="4"/>
	<value name="STENCIL_INVERT" value="5"/>
	<value name="STENCIL_INCR_WRAP" value="6"/>
	<value name="STENCIL_DECR_WRAP" value="7"/>
</enum>

<enum name="adreno_rb_blend_factor">
	<value name="FACTOR_ZERO" value="0"/>
	<value name="FACTOR_ONE" value="1"/>
	<value name="FACTOR_SRC_COLOR" value="4"/>
	<value name="FACTOR_ONE_MINUS_SRC_COLOR" value="5"/>
	<value name="FACTOR_SRC_ALPHA" value="6"/>
	<value name="FACTOR_ONE_MINUS_SRC_ALPHA" value="7"/>
	<value name="FACTOR_DST_COLOR" value="8"/>
	<value name="FACTOR_ONE_MINUS_DST_COLOR" value="9"/>
	<value name="FACTOR_DST_ALPHA" value="10"/>
	<value name="FACTOR_ONE_MINUS_DST_ALPHA" value="11"/>
	<value name="FACTOR_CONSTANT_COLOR" value="12"/>
	<value name="FACTOR_ONE_MINUS_CONSTANT_COLOR" value="13"/>
	<value name="FACTOR_CONSTANT_ALPHA" value="14"/>
	<value name="FACTOR_ONE_MINUS_CONSTANT_ALPHA" value="15"/>
	<value name="FACTOR_SRC_ALPHA_SATURATE" value="16"/>
	<value name="FACTOR_SRC1_COLOR" value="20"/>
	<value name="FACTOR_ONE_MINUS_SRC1_COLOR" value="21"/>
	<value name="FACTOR_SRC1_ALPHA" value="22"/>
	<value name="FACTOR_ONE_MINUS_SRC1_ALPHA" value="23"/>
</enum>

<bitset name="adreno_rb_stencilrefmask" inline="yes">
	<bitfield name="STENCILREF" low="0" high="7" type="hex"/>
	<bitfield name="STENCILMASK" low="8" high="15" type="hex"/>
	<bitfield name="STENCILWRITEMASK" low="16" high="23" type="hex"/>
</bitset>

<enum name="adreno_rb_surface_endian">
	<value name="ENDIAN_NONE" value="0"/>
	<value name="ENDIAN_8IN16" value="1"/>
	<value name="ENDIAN_8IN32" value="2"/>
	<value name="ENDIAN_16IN32" value="3"/>
	<value name="ENDIAN_8IN64" value="4"/>
	<value name="ENDIAN_8IN128" value="5"/>
</enum>

<enum name="adreno_rb_dither_mode">
	<value name="DITHER_DISABLE" value="0"/>
	<value name="DITHER_ALWAYS" value="1"/>
	<value name="DITHER_IF_ALPHA_OFF" value="2"/>
</enum>

<enum name="adreno_rb_depth_format">
	<value name="DEPTHX_16" value="0"/>
	<value name="DEPTHX_24_8" value="1"/>
	<value name="DEPTHX_32" value="2"/>
</enum>

<enum name="adreno_rb_copy_control_mode">
	<value name="RB_COPY_RESOLVE" value="1"/>
	<value name="RB_COPY_CLEAR" value="2"/>
	<value name="RB_COPY_DEPTH_STENCIL" value="5"/>  <!-- not sure if this is part of MODE or another bitfield?? -->
</enum>

<bitset name="adreno_reg_xy" inline="yes">
	<bitfield name="WINDOW_OFFSET_DISABLE" pos="31" type="boolean"/>
	<bitfield name="X" low="0" high="14" type="uint"/>
	<bitfield name="Y" low="16" high="30" type="uint"/>
</bitset>

<bitset name="adreno_cp_protect" inline="yes">
	<bitfield name="BASE_ADDR" low="0" high="16"/>
	<bitfield name="MASK_LEN" low="24" high="28"/>
	<bitfield name="TRAP_WRITE" pos="29"/>
	<bitfield name="TRAP_READ" pos="30"/>
</bitset>

<domain name="AXXX" width="32">
	<brief>Registers in common between a2xx and a3xx</brief>

	<reg32 offset="0x01c0" name="CP_RB_BASE"/>
	<reg32 offset="0x01c1" name="CP_RB_CNTL">
		<bitfield name="BUFSZ" low="0" high="5"/>
		<bitfield name="BLKSZ" low="8" high="13"/>
		<bitfield name="BUF_SWAP" low="16" high="17"/>
		<bitfield name="POLL_EN" pos="20" type="boolean"/>
		<bitfield name="NO_UPDATE" pos="27" type="boolean"/>
		<bitfield name="RPTR_WR_EN" pos="31" type="boolean"/>
	</reg32>
	<reg32 offset="0x01c3" name="CP_RB_RPTR_ADDR">
		<bitfield name="SWAP" low="0" high="1" type="uint"/>
		<bitfield name="ADDR" low="2" high="31" shr="2"/>
	</reg32>
	<reg32 offset="0x01c4" name="CP_RB_RPTR" type="uint"/>
	<reg32 offset="0x01c5" name="CP_RB_WPTR" type="uint"/>
	<reg32 offset="0x01c6" name="CP_RB_WPTR_DELAY"/>
	<reg32 offset="0x01c7" name="CP_RB_RPTR_WR"/>
	<reg32 offset="0x01c8" name="CP_RB_WPTR_BASE"/>
	<reg32 offset="0x01d5" name="CP_QUEUE_THRESHOLDS">
		<bitfield name="CSQ_IB1_START" low="0" high="3" type="uint"/>
		<bitfield name="CSQ_IB2_START" low="8" high="11" type="uint"/>
		<bitfield name="CSQ_ST_START" low="16" high="19" type="uint"/>
	</reg32>
	<reg32 offset="0x01d6" name="CP_MEQ_THRESHOLDS">
		<bitfield name="MEQ_END" low="16" high="20" type="uint"/>
		<bitfield name="ROQ_END" low="24" high="28" type="uint"/>
	</reg32>
	<reg32 offset="0x01d7" name="CP_CSQ_AVAIL">
		<bitfield name="RING" low="0" high="6" type="uint"/>
		<bitfield name="IB1" low="8" high="14" type="uint"/>
		<bitfield name="IB2" low="16" high="22" type="uint"/>
	</reg32>
	<reg32 offset="0x01d8" name="CP_STQ_AVAIL">
		<bitfield name="ST" low="0" high="6" type="uint"/>
	</reg32>
	<reg32 offset="0x01d9" name="CP_MEQ_AVAIL">
		<bitfield name="MEQ" low="0" high="4" type="uint"/>
	</reg32>
	<reg32 offset="0x01dc" name="SCRATCH_UMSK">
		<bitfield name="UMSK" low="0" high="7" type="uint"/>
		<bitfield name="SWAP" low="16" high="17" type="uint"/>
	</reg32>
	<reg32 offset="0x01dd" name="SCRATCH_ADDR"/>
	<reg32 offset="0x01ea" name="CP_ME_RDADDR"/>

	<reg32 offset="0x01ec" name="CP_STATE_DEBUG_INDEX"/>
	<reg32 offset="0x01ed" name="CP_STATE_DEBUG_DATA"/>
	<reg32 offset="0x01f2" name="CP_INT_CNTL">
		<bitfield name="SW_INT_MASK" pos="19" type="boolean"/>
		<bitfield name="T0_PACKET_IN_IB_MASK" pos="23" type="boolean"/>
		<bitfield name="OPCODE_ERROR_MASK" pos="24" type="boolean"/>
		<bitfield name="PROTECTED_MODE_ERROR_MASK" pos="25" type="boolean"/>
		<bitfield name="RESERVED_BIT_ERROR_MASK" pos="26" type="boolean"/>
		<bitfield name="IB_ERROR_MASK" pos="27" type="boolean"/>
		<bitfield name="IB2_INT_MASK" pos="29" type="boolean"/>
		<bitfield name="IB1_INT_MASK" pos="30" type="boolean"/>
		<bitfield name="RB_INT_MASK" pos="31" type="boolean"/>
	</reg32>
	<reg32 offset="0x01f3" name="CP_INT_STATUS"/>
	<reg32 offset="0x01f4" name="CP_INT_ACK"/>
	<reg32 offset="0x01f6" name="CP_ME_CNTL">
		<bitfield name="BUSY" pos="29" type="boolean"/>
		<bitfield name="HALT" pos="28" type="boolean"/>
	</reg32>
	<reg32 offset="0x01f7" name="CP_ME_STATUS"/>
	<reg32 offset="0x01f8" name="CP_ME_RAM_WADDR"/>
	<reg32 offset="0x01f9" name="CP_ME_RAM_RADDR"/>
	<reg32 offset="0x01fa" name="CP_ME_RAM_DATA"/>
	<reg32 offset="0x01fc" name="CP_DEBUG">
		<bitfield name="PREDICATE_DISABLE" pos="23" type="boolean"/>
		<bitfield name="PROG_END_PTR_ENABLE" pos="24" type="boolean"/>
		<bitfield name="MIU_128BIT_WRITE_ENABLE" pos="25" type="boolean"/>
		<bitfield name="PREFETCH_PASS_NOPS" pos="26" type="boolean"/>
		<bitfield name="DYNAMIC_CLK_DISABLE" pos="27" type="boolean"/>
		<bitfield name="PREFETCH_MATCH_DISABLE" pos="28" type="boolean"/>
		<bitfield name="SIMPLE_ME_FLOW_CONTROL" pos="30" type="boolean"/>
		<bitfield name="MIU_WRITE_PACK_DISABLE" pos="31" type="boolean"/>
	</reg32>
	<reg32 offset="0x01fd" name="CP_CSQ_RB_STAT">
		<bitfield name="RPTR" low="0" high="6" type="uint"/>
		<bitfield name="WPTR" low="16" high="22" type="uint"/>
	</reg32>
	<reg32 offset="0x01fe" name="CP_CSQ_IB1_STAT">
		<bitfield name="RPTR" low="0" high="6" type="uint"/>
		<bitfield name="WPTR" low="16" high="22" type="uint"/>
	</reg32>
	<reg32 offset="0x01ff" name="CP_CSQ_IB2_STAT">
		<bitfield name="RPTR" low="0" high="6" type="uint"/>
		<bitfield name="WPTR" low="16" high="22" type="uint"/>
	</reg32>

	<reg32 offset="0x0440" name="CP_NON_PREFETCH_CNTRS"/>
	<reg32 offset="0x0443" name="CP_STQ_ST_STAT"/>
	<reg32 offset="0x044d" name="CP_ST_BASE"/>
	<reg32 offset="0x044e" name="CP_ST_BUFSZ"/>
	<reg32 offset="0x044f" name="CP_MEQ_STAT"/>
	<reg32 offset="0x0452" name="CP_MIU_TAG_STAT"/>
	<reg32 offset="0x0454" name="CP_BIN_MASK_LO"/>
	<reg32 offset="0x0455" name="CP_BIN_MASK_HI"/>
	<reg32 offset="0x0456" name="CP_BIN_SELECT_LO"/>
	<reg32 offset="0x0457" name="CP_BIN_SELECT_HI"/>
	<reg32 offset="0x0458" name="CP_IB1_BASE"/>
	<reg32 offset="0x0459" name="CP_IB1_BUFSZ"/>
	<reg32 offset="0x045a" name="CP_IB2_BASE"/>
	<reg32 offset="0x045b" name="CP_IB2_BUFSZ"/>
	<reg32 offset="0x047f" name="CP_STAT">
		<bitfield pos="31" name="CP_BUSY"/>
		<bitfield pos="30" name="VS_EVENT_FIFO_BUSY"/>
		<bitfield pos="29" name="PS_EVENT_FIFO_BUSY"/>
		<bitfield pos="28" name="CF_EVENT_FIFO_BUSY"/>
		<bitfield pos="27" name="RB_EVENT_FIFO_BUSY"/>
		<bitfield pos="26" name="ME_BUSY"/>
		<bitfield pos="25" name="MIU_WR_C_BUSY"/>
		<bitfield pos="23" name="CP_3D_BUSY"/>
		<bitfield pos="22" name="CP_NRT_BUSY"/>
		<bitfield pos="21" name="RBIU_SCRATCH_BUSY"/>
		<bitfield pos="20" name="RCIU_ME_BUSY"/>
		<bitfield pos="19" name="RCIU_PFP_BUSY"/>
		<bitfield pos="18" name="MEQ_RING_BUSY"/>
		<bitfield pos="17" name="PFP_BUSY"/>
		<bitfield pos="16" name="ST_QUEUE_BUSY"/>
		<bitfield pos="13" name="INDIRECT2_QUEUE_BUSY"/>
		<bitfield pos="12" name="INDIRECTS_QUEUE_BUSY"/>
		<bitfield pos="11" name="RING_QUEUE_BUSY"/>
		<bitfield pos="10" name="CSF_BUSY"/>
		<bitfield pos="9"  name="CSF_ST_BUSY"/>
		<bitfield pos="8"  name="EVENT_BUSY"/>
		<bitfield pos="7"  name="CSF_INDIRECT2_BUSY"/>
		<bitfield pos="6"  name="CSF_INDIRECTS_BUSY"/>
		<bitfield pos="5"  name="CSF_RING_BUSY"/>
		<bitfield pos="4"  name="RCIU_BUSY"/>
		<bitfield pos="3"  name="RBIU_BUSY"/>
		<bitfield pos="2"  name="MIU_RD_RETURN_BUSY"/>
		<bitfield pos="1"  name="MIU_RD_REQ_BUSY"/>
		<bitfield pos="0"  name="MIU_WR_BUSY"/>
	</reg32>
	<reg32 offset="0x0578" name="CP_SCRATCH_REG0" type="uint"/>
	<reg32 offset="0x0579" name="CP_SCRATCH_REG1" type="uint"/>
	<reg32 offset="0x057a" name="CP_SCRATCH_REG2" type="uint"/>
	<reg32 offset="0x057b" name="CP_SCRATCH_REG3" type="uint"/>
	<reg32 offset="0x057c" name="CP_SCRATCH_REG4" type="uint"/>
	<reg32 offset="0x057d" name="CP_SCRATCH_REG5" type="uint"/>
	<reg32 offset="0x057e" name="CP_SCRATCH_REG6" type="uint"/>
	<reg32 offset="0x057f" name="CP_SCRATCH_REG7" type="uint"/>

	<reg32 offset="0x0600" name="CP_ME_VS_EVENT_SRC"/>
	<reg32 offset="0x0601" name="CP_ME_VS_EVENT_ADDR"/>
	<reg32 offset="0x0602" name="CP_ME_VS_EVENT_DATA"/>
	<reg32 offset="0x0603" name="CP_ME_VS_EVENT_ADDR_SWM"/>
	<reg32 offset="0x0604" name="CP_ME_VS_EVENT_DATA_SWM"/>
	<reg32 offset="0x0605" name="CP_ME_PS_EVENT_SRC"/>
	<reg32 offset="0x0606" name="CP_ME_PS_EVENT_ADDR"/>
	<reg32 offset="0x0607" name="CP_ME_PS_EVENT_DATA"/>
	<reg32 offset="0x0608" name="CP_ME_PS_EVENT_ADDR_SWM"/>
	<reg32 offset="0x0609" name="CP_ME_PS_EVENT_DATA_SWM"/>
	<reg32 offset="0x060a" name="CP_ME_CF_EVENT_SRC"/>
	<reg32 offset="0x060b" name="CP_ME_CF_EVENT_ADDR"/>
	<reg32 offset="0x060c" name="CP_ME_CF_EVENT_DATA" type="uint"/>
	<reg32 offset="0x060d" name="CP_ME_NRT_ADDR"/>
	<reg32 offset="0x060e" name="CP_ME_NRT_DATA"/>
	<reg32 offset="0x0612" name="CP_ME_VS_FETCH_DONE_SRC"/>
	<reg32 offset="0x0613" name="CP_ME_VS_FETCH_DONE_ADDR"/>
	<reg32 offset="0x0614" name="CP_ME_VS_FETCH_DONE_DATA"/>

</domain>

<!--
	Common between A3xx and A4xx:
 -->

<enum name="a3xx_rop_code">
	<value name="ROP_CLEAR"         value="0"/>
	<value name="ROP_NOR"           value="1"/>
	<value name="ROP_AND_INVERTED"  value="2"/>
	<value name="ROP_COPY_INVERTED" value="3"/>
	<value name="ROP_AND_REVERSE"   value="4"/>
	<value name="ROP_INVERT"        value="5"/>
	<value name="ROP_XOR"           value="6"/>
	<value name="ROP_NAND"          value="7"/>
	<value name="ROP_AND"           value="8"/>
	<value name="ROP_EQUIV"         value="9"/>
	<value name="ROP_NOOP"          value="10"/>
	<value name="ROP_OR_INVERTED"   value="11"/>
	<value name="ROP_COPY"          value="12"/>
	<value name="ROP_OR_REVERSE"    value="13"/>
	<value name="ROP_OR"            value="14"/>
	<value name="ROP_SET"           value="15"/>
</enum>

<enum name="a3xx_render_mode">
	<value name="RB_RENDERING_PASS" value="0"/>
	<value name="RB_TILING_PASS" value="1"/>
	<value name="RB_RESOLVE_PASS" value="2"/>
	<value name="RB_COMPUTE_PASS" value="3"/>
</enum>

<enum name="a3xx_msaa_samples">
	<value name="MSAA_ONE" value="0"/>
	<value name="MSAA_TWO" value="1"/>
	<value name="MSAA_FOUR" value="2"/>
	<value name="MSAA_EIGHT" value="3"/>
</enum>

<enum name="a3xx_threadmode">
	<value value="0" name="MULTI"/>
	<value value="1" name="SINGLE"/>
</enum>

<enum name="a3xx_instrbuffermode">
	<!--
	When shader size goes above ~128 or so, blob switches to '0'
	and doesn't emit shader in cmdstream.  When either is '0' it
	doesn't get emitted via CP_LOAD_STATE.  When only one is
	'0' the other gets size 256-others_size.  So I think that:
		BUFFER => execute out of state memory
		CACHE  => use available state memory as local cache
	NOTE that when CACHE mode, also set CACHEINVALID flag!

	TODO check if that 256 size is same for all a3xx
	 -->
	<value value="0" name="CACHE"/>
	<value value="1" name="BUFFER"/>
</enum>

<enum name="a3xx_threadsize">
	<value value="0" name="TWO_QUADS"/>
	<value value="1" name="FOUR_QUADS"/>
</enum>

<enum name="a3xx_color_swap">
	<value name="WZYX" value="0"/>
	<value name="WXYZ" value="1"/>
	<value name="ZYXW" value="2"/>
	<value name="XYZW" value="3"/>
</enum>

<enum name="a3xx_rb_blend_opcode">
	<value name="BLEND_DST_PLUS_SRC" value="0"/>
	<value name="BLEND_SRC_MINUS_DST" value="1"/>
	<value name="BLEND_DST_MINUS_SRC" value="2"/>
	<value name="BLEND_MIN_DST_SRC" value="3"/>
	<value name="BLEND_MAX_DST_SRC" value="4"/>
</enum>

<enum name="a4xx_tess_spacing">
	<value name="EQUAL_SPACING" value="0"/>
	<value name="ODD_SPACING" value="2"/>
	<value name="EVEN_SPACING" value="3"/>
</enum>

<doc>Address mode for a5xx+</doc>
<enum name="a5xx_address_mode">
	<value name="ADDR_32B" value="0"/>
	<value name="ADDR_64B" value="1"/>
</enum>

<doc>
    Line mode for a5xx+
	Note that Bresenham lines are only supported with MSAA disabled.
</doc>
<enum name="a5xx_line_mode">
	<value value="0x0"  name="BRESENHAM"/>
	<value value="0x1"  name="RECTANGULAR"/>
</enum>

<doc>
	Blob (v615) seem to only use SAM and I wasn't able to coerce
	it to produce any other command.
	Probably valid for a4xx+ but not enabled or tested on anything
	but a6xx.
</doc>
<enum name="a6xx_tex_prefetch_cmd">
	<doc> Produces garbage </doc>
	<value value="0x0" name="TEX_PREFETCH_UNK0"/>
	<value value="0x1" name="TEX_PREFETCH_SAM"/>
	<value value="0x2" name="TEX_PREFETCH_GATHER4R"/>
	<value value="0x3" name="TEX_PREFETCH_GATHER4G"/>
	<value value="0x4" name="TEX_PREFETCH_GATHER4B"/>
	<value value="0x5" name="TEX_PREFETCH_GATHER4A"/>
	<doc> Causes reads from an invalid address </doc>
	<value value="0x6" name="TEX_PREFETCH_UNK6"/>
	<doc> Results in color being zero </doc>
	<value value="0x7" name="TEX_PREFETCH_UNK7"/>
</enum>

</database>
