<?xml version="1.0" encoding="UTF-8"?>
<database xmlns="http://nouveau.freedesktop.org/"
xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
xsi:schemaLocation="https://gitlab.freedesktop.org/freedreno/ rules-fd.xsd">
<import file="freedreno_copyright.xml"/>
<import file="adreno/adreno_common.xml"/>
<import file="adreno/adreno_pm4.xml"/>

<enum name="a3xx_tile_mode">
	<value name="LINEAR" value="0"/>
	<value name="TILE_4X4" value="1"/>    <!-- "normal" case for textures -->
	<value name="TILE_32X32" value="2"/>  <!-- only used in GMEM -->
	<value name="TILE_4X2" value="3"/>    <!-- only used for CrCb -->
</enum>

<enum name="a3xx_state_block_id">
	<value name="HLSQ_BLOCK_ID_TP_TEX" value="2"/>
	<value name="HLSQ_BLOCK_ID_TP_MIPMAP" value="3"/>
	<value name="HLSQ_BLOCK_ID_SP_VS" value="4"/>
	<value name="HLSQ_BLOCK_ID_SP_FS" value="6"/>
</enum>

<enum name="a3xx_cache_opcode">
	<value name="INVALIDATE" value="1"/>
</enum>

<enum name="a3xx_vtx_fmt">
	<value name="VFMT_32_FLOAT" value="0x0"/>
	<value name="VFMT_32_32_FLOAT" value="0x1"/>
	<value name="VFMT_32_32_32_FLOAT" value="0x2"/>
	<value name="VFMT_32_32_32_32_FLOAT" value="0x3"/>

	<value name="VFMT_16_FLOAT" value="0x4"/>
	<value name="VFMT_16_16_FLOAT" value="0x5"/>
	<value name="VFMT_16_16_16_FLOAT" value="0x6"/>
	<value name="VFMT_16_16_16_16_FLOAT" value="0x7"/>

	<value name="VFMT_32_FIXED" value="0x8"/>
	<value name="VFMT_32_32_FIXED" value="0x9"/>
	<value name="VFMT_32_32_32_FIXED" value="0xa"/>
	<value name="VFMT_32_32_32_32_FIXED" value="0xb"/>

	<value name="VFMT_16_SINT" value="0x10"/>
	<value name="VFMT_16_16_SINT" value="0x11"/>
	<value name="VFMT_16_16_16_SINT" value="0x12"/>
	<value name="VFMT_16_16_16_16_SINT" value="0x13"/>
	<value name="VFMT_16_UINT" value="0x14"/>
	<value name="VFMT_16_16_UINT" value="0x15"/>
	<value name="VFMT_16_16_16_UINT" value="0x16"/>
	<value name="VFMT_16_16_16_16_UINT" value="0x17"/>
	<value name="VFMT_16_SNORM" value="0x18"/>
	<value name="VFMT_16_16_SNORM" value="0x19"/>
	<value name="VFMT_16_16_16_SNORM" value="0x1a"/>
	<value name="VFMT_16_16_16_16_SNORM" value="0x1b"/>
	<value name="VFMT_16_UNORM" value="0x1c"/>
	<value name="VFMT_16_16_UNORM" value="0x1d"/>
	<value name="VFMT_16_16_16_UNORM" value="0x1e"/>
	<value name="VFMT_16_16_16_16_UNORM" value="0x1f"/>

	<!-- seems to be no NORM variants for 32bit.. -->
	<value name="VFMT_32_UINT" value="0x20"/>
	<value name="VFMT_32_32_UINT" value="0x21"/>
	<value name="VFMT_32_32_32_UINT" value="0x22"/>
	<value name="VFMT_32_32_32_32_UINT" value="0x23"/>
	<value name="VFMT_32_SINT" value="0x24"/>
	<value name="VFMT_32_32_SINT" value="0x25"/>
	<value name="VFMT_32_32_32_SINT" value="0x26"/>
	<value name="VFMT_32_32_32_32_SINT" value="0x27"/>

	<value name="VFMT_8_UINT" value="0x28"/>
	<value name="VFMT_8_8_UINT" value="0x29"/>
	<value name="VFMT_8_8_8_UINT" value="0x2a"/>
	<value name="VFMT_8_8_8_8_UINT" value="0x2b"/>
	<value name="VFMT_8_UNORM" value="0x2c"/>
	<value name="VFMT_8_8_UNORM" value="0x2d"/>
	<value name="VFMT_8_8_8_UNORM" value="0x2e"/>
	<value name="VFMT_8_8_8_8_UNORM" value="0x2f"/>
	<value name="VFMT_8_SINT" value="0x30"/>
	<value name="VFMT_8_8_SINT" value="0x31"/>
	<value name="VFMT_8_8_8_SINT" value="0x32"/>
	<value name="VFMT_8_8_8_8_SINT" value="0x33"/>
	<value name="VFMT_8_SNORM" value="0x34"/>
	<value name="VFMT_8_8_SNORM" value="0x35"/>
	<value name="VFMT_8_8_8_SNORM" value="0x36"/>
	<value name="VFMT_8_8_8_8_SNORM" value="0x37"/>
	<value name="VFMT_10_10_10_2_UINT" value="0x38"/>
	<value name="VFMT_10_10_10_2_UNORM" value="0x39"/>
	<value name="VFMT_10_10_10_2_SINT" value="0x3a"/>
	<value name="VFMT_10_10_10_2_SNORM" value="0x3b"/>
	<value name="VFMT_2_10_10_10_UINT" value="0x3c"/>
	<value name="VFMT_2_10_10_10_UNORM" value="0x3d"/>
	<value name="VFMT_2_10_10_10_SINT" value="0x3e"/>
	<value name="VFMT_2_10_10_10_SNORM" value="0x3f"/>

	<value name="VFMT_NONE" value="0xff"/>
</enum>

<enum name="a3xx_tex_fmt">
	<value name="TFMT_5_6_5_UNORM" value="0x4"/>
	<value name="TFMT_5_5_5_1_UNORM" value="0x5"/>
	<value name="TFMT_4_4_4_4_UNORM" value="0x7"/>
	<value name="TFMT_Z16_UNORM" value="0x9"/>
	<value name="TFMT_X8Z24_UNORM" value="0xa"/>
	<value name="TFMT_Z32_FLOAT" value="0xb"/>

	<!--
		The NV12 tiled/linear formats seem to require gang'd sampler
		slots (ie. sampler state N plus N+1) for Y and UV planes.
		They fetch yuv in single sam instruction, but still require
		colorspace conversion in the shader.
	 -->
	<value name="TFMT_UV_64X32" value="0x10"/>
	<value name="TFMT_VU_64X32" value="0x11"/>
	<value name="TFMT_Y_64X32" value="0x12"/>
	<value name="TFMT_NV12_64X32" value="0x13"/>
	<value name="TFMT_UV_LINEAR" value="0x14"/>
	<value name="TFMT_VU_LINEAR" value="0x15"/>
	<value name="TFMT_Y_LINEAR" value="0x16"/>
	<value name="TFMT_NV12_LINEAR" value="0x17"/>
	<value name="TFMT_I420_Y" value="0x18"/>
	<value name="TFMT_I420_U" value="0x1a"/>
	<value name="TFMT_I420_V" value="0x1b"/>

	<value name="TFMT_ATC_RGB" value="0x20"/>
	<value name="TFMT_ATC_RGBA_EXPLICIT" value="0x21"/>
	<value name="TFMT_ETC1" value="0x22"/>
	<value name="TFMT_ATC_RGBA_INTERPOLATED" value="0x23"/>

	<value name="TFMT_DXT1" value="0x24"/>
	<value name="TFMT_DXT3" value="0x25"/>
	<value name="TFMT_DXT5" value="0x26"/>

	<value name="TFMT_2_10_10_10_UNORM" value="0x28"/>
	<value name="TFMT_10_10_10_2_UNORM" value="0x29"/>
	<value name="TFMT_9_9_9_E5_FLOAT" value="0x2a"/>
	<value name="TFMT_11_11_10_FLOAT" value="0x2b"/>
	<value name="TFMT_A8_UNORM" value="0x2c"/>    <!-- GL_ALPHA -->
	<value name="TFMT_L8_UNORM" value="0x2d"/>
	<value name="TFMT_L8_A8_UNORM" value="0x2f"/> <!-- GL_LUMINANCE_ALPHA -->

	<!--
		NOTE: GL_ALPHA and GL_LUMINANCE_ALPHA aren't handled in a similar way
		to float16, float32.. but they seem to use non-standard swizzle too..
		perhaps we can ditch that if the pattern follows of 0xn0, 0xn1, 0xn2,
		0xn3 for 1, 2, 3, 4 components respectively..

		Only formats filled in below are the ones that have been observed by
		the blob or tested.. you can guess what the missing ones are..
	 -->

	<value name="TFMT_8_UNORM" value="0x30"/>     <!-- GL_LUMINANCE -->
	<value name="TFMT_8_8_UNORM" value="0x31"/>
	<value name="TFMT_8_8_8_UNORM" value="0x32"/>
	<value name="TFMT_8_8_8_8_UNORM" value="0x33"/>

	<value name="TFMT_8_SNORM" value="0x34"/>
	<value name="TFMT_8_8_SNORM" value="0x35"/>
	<value name="TFMT_8_8_8_SNORM" value="0x36"/>
	<value name="TFMT_8_8_8_8_SNORM" value="0x37"/>

	<value name="TFMT_8_UINT" value="0x38"/>
	<value name="TFMT_8_8_UINT" value="0x39"/>
	<value name="TFMT_8_8_8_UINT" value="0x3a"/>
	<value name="TFMT_8_8_8_8_UINT" value="0x3b"/>

	<value name="TFMT_8_SINT" value="0x3c"/>
	<value name="TFMT_8_8_SINT" value="0x3d"/>
	<value name="TFMT_8_8_8_SINT" value="0x3e"/>
	<value name="TFMT_8_8_8_8_SINT" value="0x3f"/>

	<value name="TFMT_16_FLOAT" value="0x40"/>
	<value name="TFMT_16_16_FLOAT" value="0x41"/>
	<!-- TFMT_FLOAT_16_16_16 -->
	<value name="TFMT_16_16_16_16_FLOAT" value="0x43"/>

	<value name="TFMT_16_UINT" value="0x44"/>
	<value name="TFMT_16_16_UINT" value="0x45"/>
	<value name="TFMT_16_16_16_16_UINT" value="0x47"/>

	<value name="TFMT_16_SINT" value="0x48"/>
	<value name="TFMT_16_16_SINT" value="0x49"/>
	<value name="TFMT_16_16_16_16_SINT" value="0x4b"/>

	<value name="TFMT_16_UNORM" value="0x4c"/>
	<value name="TFMT_16_16_UNORM" value="0x4d"/>
	<value name="TFMT_16_16_16_16_UNORM" value="0x4f"/>

	<value name="TFMT_16_SNORM" value="0x50"/>
	<value name="TFMT_16_16_SNORM" value="0x51"/>
	<value name="TFMT_16_16_16_16_SNORM" value="0x53"/>

	<value name="TFMT_32_FLOAT" value="0x54"/>
	<value name="TFMT_32_32_FLOAT" value="0x55"/>
	<!-- TFMT_32_32_32_FLOAT -->
	<value name="TFMT_32_32_32_32_FLOAT" value="0x57"/>

	<value name="TFMT_32_UINT" value="0x58"/>
	<value name="TFMT_32_32_UINT" value="0x59"/>
	<value name="TFMT_32_32_32_32_UINT" value="0x5b"/>

	<value name="TFMT_32_SINT" value="0x5c"/>
	<value name="TFMT_32_32_SINT" value="0x5d"/>
	<value name="TFMT_32_32_32_32_SINT" value="0x5f"/>

	<value name="TFMT_2_10_10_10_UINT" value="0x60"/>
	<value name="TFMT_10_10_10_2_UINT" value="0x61"/>

	<value name="TFMT_ETC2_RG11_SNORM" value="0x70"/>
	<value name="TFMT_ETC2_RG11_UNORM" value="0x71"/>
	<value name="TFMT_ETC2_R11_SNORM" value="0x72"/>
	<value name="TFMT_ETC2_R11_UNORM" value="0x73"/>
	<value name="TFMT_ETC2_RGBA8" value="0x74"/>
	<value name="TFMT_ETC2_RGB8A1" value="0x75"/>
	<value name="TFMT_ETC2_RGB8" value="0x76"/>

	<value name="TFMT_NONE" value="0xff"/>
</enum>

<enum name="a3xx_color_fmt">
	<value name="RB_R5G6B5_UNORM"       value="0x00"/>
	<value name="RB_R5G5B5A1_UNORM"     value="0x01"/>
	<value name="RB_R4G4B4A4_UNORM"     value="0x03"/>
	<value name="RB_R8G8B8_UNORM"	    value="0x04"/>
	<value name="RB_R8G8B8A8_UNORM"	    value="0x08"/>
	<value name="RB_R8G8B8A8_SNORM"	    value="0x09"/>
	<value name="RB_R8G8B8A8_UINT"	    value="0x0a"/>
	<value name="RB_R8G8B8A8_SINT"	    value="0x0b"/>
	<value name="RB_R8G8_UNORM"	    value="0x0c"/>
	<value name="RB_R8G8_SNORM"	    value="0x0d"/>
	<value name="RB_R8G8_UINT"	    value="0x0e"/>
	<value name="RB_R8G8_SINT"	    value="0x0f"/>
	<value name="RB_R10G10B10A2_UNORM"  value="0x10"/>
	<value name="RB_A2R10G10B10_UNORM"  value="0x11"/>
	<value name="RB_R10G10B10A2_UINT"   value="0x12"/>
	<value name="RB_A2R10G10B10_UINT"   value="0x13"/>

	<value name="RB_A8_UNORM"	    value="0x14"/>
	<value name="RB_R8_UNORM"	    value="0x15"/>

	<value name="RB_R16_FLOAT"          value="0x18"/>
	<value name="RB_R16G16_FLOAT"       value="0x19"/>
	<value name="RB_R16G16B16A16_FLOAT" value="0x1b"/> <!-- GL_HALF_FLOAT_OES -->
	<value name="RB_R11G11B10_FLOAT"    value="0x1c"/>

	<value name="RB_R16_SNORM"          value="0x20"/>
	<value name="RB_R16G16_SNORM"       value="0x21"/>
	<value name="RB_R16G16B16A16_SNORM" value="0x23"/>

	<value name="RB_R16_UNORM"          value="0x24"/>
	<value name="RB_R16G16_UNORM"       value="0x25"/>
	<value name="RB_R16G16B16A16_UNORM" value="0x27"/>

	<value name="RB_R16_SINT"	    value="0x28"/>
	<value name="RB_R16G16_SINT"	    value="0x29"/>
	<value name="RB_R16G16B16A16_SINT"  value="0x2b"/>

	<value name="RB_R16_UINT"	    value="0x2c"/>
	<value name="RB_R16G16_UINT"	    value="0x2d"/>
	<value name="RB_R16G16B16A16_UINT"  value="0x2f"/>

	<value name="RB_R32_FLOAT"          value="0x30"/>
	<value name="RB_R32G32_FLOAT"       value="0x31"/>
	<value name="RB_R32G32B32A32_FLOAT" value="0x33"/> <!-- GL_FLOAT -->

	<value name="RB_R32_SINT"	    value="0x34"/>
	<value name="RB_R32G32_SINT"	    value="0x35"/>
	<value name="RB_R32G32B32A32_SINT"  value="0x37"/>

	<value name="RB_R32_UINT"	    value="0x38"/>
	<value name="RB_R32G32_UINT"	    value="0x39"/>
	<value name="RB_R32G32B32A32_UINT"  value="0x3b"/>

	<value name="RB_NONE"               value="0xff"/>
</enum>

<enum name="a3xx_cp_perfcounter_select">
	<value value="0x00" name="CP_ALWAYS_COUNT"/>
	<value value="0x03" name="CP_AHB_PFPTRANS_WAIT"/>
	<value value="0x06" name="CP_AHB_NRTTRANS_WAIT"/>
	<value value="0x08" name="CP_CSF_NRT_READ_WAIT"/>
	<value value="0x09" name="CP_CSF_I1_FIFO_FULL"/>
	<value value="0x0a" name="CP_CSF_I2_FIFO_FULL"/>
	<value value="0x0b" name="CP_CSF_ST_FIFO_FULL"/>
	<value value="0x0c" name="CP_RESERVED_12"/>
	<value value="0x0d" name="CP_CSF_RING_ROQ_FULL"/>
	<value value="0x0e" name="CP_CSF_I1_ROQ_FULL"/>
	<value value="0x0f" name="CP_CSF_I2_ROQ_FULL"/>
	<value value="0x10" name="CP_CSF_ST_ROQ_FULL"/>
	<value value="0x11" name="CP_RESERVED_17"/>
	<value value="0x12" name="CP_MIU_TAG_MEM_FULL"/>
	<value value="0x16" name="CP_MIU_NRT_WRITE_STALLED"/>
	<value value="0x17" name="CP_MIU_NRT_READ_STALLED"/>
	<value value="0x1a" name="CP_ME_REGS_RB_DONE_FIFO_FULL"/>
	<value value="0x1b" name="CP_ME_REGS_VS_EVENT_FIFO_FULL"/>
	<value value="0x1c" name="CP_ME_REGS_PS_EVENT_FIFO_FULL"/>
	<value value="0x1d" name="CP_ME_REGS_CF_EVENT_FIFO_FULL"/>
	<value value="0x1e" name="CP_ME_MICRO_RB_STARVED"/>
	<value value="0x28" name="CP_AHB_RBBM_DWORD_SENT"/>
	<value value="0x29" name="CP_ME_BUSY_CLOCKS"/>
	<value value="0x2a" name="CP_ME_WAIT_CONTEXT_AVAIL"/>
	<value value="0x2b" name="CP_PFP_TYPE0_PACKET"/>
	<value value="0x2c" name="CP_PFP_TYPE3_PACKET"/>
	<value value="0x2d" name="CP_CSF_RB_WPTR_NEQ_RPTR"/>
	<value value="0x2e" name="CP_CSF_I1_SIZE_NEQ_ZERO"/>
	<value value="0x2f" name="CP_CSF_I2_SIZE_NEQ_ZERO"/>
	<value value="0x30" name="CP_CSF_RBI1I2_FETCHING"/>
</enum>

<enum name="a3xx_gras_tse_perfcounter_select">
	<value value="0x00" name="GRAS_TSEPERF_INPUT_PRIM"/>
	<value value="0x01" name="GRAS_TSEPERF_INPUT_NULL_PRIM"/>
	<value value="0x02" name="GRAS_TSEPERF_TRIVAL_REJ_PRIM"/>
	<value value="0x03" name="GRAS_TSEPERF_CLIPPED_PRIM"/>
	<value value="0x04" name="GRAS_TSEPERF_NEW_PRIM"/>
	<value value="0x05" name="GRAS_TSEPERF_ZERO_AREA_PRIM"/>
	<value value="0x06" name="GRAS_TSEPERF_FACENESS_CULLED_PRIM"/>
	<value value="0x07" name="GRAS_TSEPERF_ZERO_PIXEL_PRIM"/>
	<value value="0x08" name="GRAS_TSEPERF_OUTPUT_NULL_PRIM"/>
	<value value="0x09" name="GRAS_TSEPERF_OUTPUT_VISIBLE_PRIM"/>
	<value value="0x0a" name="GRAS_TSEPERF_PRE_CLIP_PRIM"/>
	<value value="0x0b" name="GRAS_TSEPERF_POST_CLIP_PRIM"/>
	<value value="0x0c" name="GRAS_TSEPERF_WORKING_CYCLES"/>
	<value value="0x0d" name="GRAS_TSEPERF_PC_STARVE"/>
	<value value="0x0e" name="GRAS_TSERASPERF_STALL"/>
</enum>

<enum name="a3xx_gras_ras_perfcounter_select">
	<value value="0x00" name="GRAS_RASPERF_16X16_TILES"/>
	<value value="0x01" name="GRAS_RASPERF_8X8_TILES"/>
	<value value="0x02" name="GRAS_RASPERF_4X4_TILES"/>
	<value value="0x03" name="GRAS_RASPERF_WORKING_CYCLES"/>
	<value value="0x04" name="GRAS_RASPERF_STALL_CYCLES_BY_RB"/>
	<value value="0x05" name="GRAS_RASPERF_STALL_CYCLES_BY_VSC"/>
	<value value="0x06" name="GRAS_RASPERF_STARVE_CYCLES_BY_TSE"/>
</enum>

<enum name="a3xx_hlsq_perfcounter_select">
	<value value="0x00" name="HLSQ_PERF_SP_VS_CONSTANT"/>
	<value value="0x01" name="HLSQ_PERF_SP_VS_INSTRUCTIONS"/>
	<value value="0x02" name="HLSQ_PERF_SP_FS_CONSTANT"/>
	<value value="0x03" name="HLSQ_PERF_SP_FS_INSTRUCTIONS"/>
	<value value="0x04" name="HLSQ_PERF_TP_STATE"/>
	<value value="0x05" name="HLSQ_PERF_QUADS"/>
	<value value="0x06" name="HLSQ_PERF_PIXELS"/>
	<value value="0x07" name="HLSQ_PERF_VERTICES"/>
	<value value="0x08" name="HLSQ_PERF_FS8_THREADS"/>
	<value value="0x09" name="HLSQ_PERF_FS16_THREADS"/>
	<value value="0x0a" name="HLSQ_PERF_FS32_THREADS"/>
	<value value="0x0b" name="HLSQ_PERF_VS8_THREADS"/>
	<value value="0x0c" name="HLSQ_PERF_VS16_THREADS"/>
	<value value="0x0d" name="HLSQ_PERF_SP_VS_DATA_BYTES"/>
	<value value="0x0e" name="HLSQ_PERF_SP_FS_DATA_BYTES"/>
	<value value="0x0f" name="HLSQ_PERF_ACTIVE_CYCLES"/>
	<value value="0x10" name="HLSQ_PERF_STALL_CYCLES_SP_STATE"/>
	<value value="0x11" name="HLSQ_PERF_STALL_CYCLES_SP_VS"/>
	<value value="0x12" name="HLSQ_PERF_STALL_CYCLES_SP_FS"/>
	<value value="0x13" name="HLSQ_PERF_STALL_CYCLES_UCHE"/>
	<value value="0x14" name="HLSQ_PERF_RBBM_LOAD_CYCLES"/>
	<value value="0x15" name="HLSQ_PERF_DI_TO_VS_START_SP0"/>
	<value value="0x16" name="HLSQ_PERF_DI_TO_FS_START_SP0"/>
	<value value="0x17" name="HLSQ_PERF_VS_START_TO_DONE_SP0"/>
	<value value="0x18" name="HLSQ_PERF_FS_START_TO_DONE_SP0"/>
	<value value="0x19" name="HLSQ_PERF_SP_STATE_COPY_CYCLES_VS"/>
	<value value="0x1a" name="HLSQ_PERF_SP_STATE_COPY_CYCLES_FS"/>
	<value value="0x1b" name="HLSQ_PERF_UCHE_LATENCY_CYCLES"/>
	<value value="0x1c" name="HLSQ_PERF_UCHE_LATENCY_COUNT"/>
</enum>

<enum name="a3xx_pc_perfcounter_select">
	<value value="0x00" name="PC_PCPERF_VISIBILITY_STREAMS"/>
	<value value="0x01" name="PC_PCPERF_TOTAL_INSTANCES"/>
	<value value="0x02" name="PC_PCPERF_PRIMITIVES_PC_VPC"/>
	<value value="0x03" name="PC_PCPERF_PRIMITIVES_KILLED_BY_VS"/>
	<value value="0x04" name="PC_PCPERF_PRIMITIVES_VISIBLE_BY_VS"/>
	<value value="0x05" name="PC_PCPERF_DRAWCALLS_KILLED_BY_VS"/>
	<value value="0x06" name="PC_PCPERF_DRAWCALLS_VISIBLE_BY_VS"/>
	<value value="0x07" name="PC_PCPERF_VERTICES_TO_VFD"/>
	<value value="0x08" name="PC_PCPERF_REUSED_VERTICES"/>
	<value value="0x09" name="PC_PCPERF_CYCLES_STALLED_BY_VFD"/>
	<value value="0x0a" name="PC_PCPERF_CYCLES_STALLED_BY_TSE"/>
	<value value="0x0b" name="PC_PCPERF_CYCLES_STALLED_BY_VBIF"/>
	<value value="0x0c" name="PC_PCPERF_CYCLES_IS_WORKING"/>
</enum>

<enum name="a3xx_rb_perfcounter_select">
	<value value="0x00" name="RB_RBPERF_ACTIVE_CYCLES_ANY"/>
	<value value="0x01" name="RB_RBPERF_ACTIVE_CYCLES_ALL"/>
	<value value="0x02" name="RB_RBPERF_STARVE_CYCLES_BY_SP"/>
	<value value="0x03" name="RB_RBPERF_STARVE_CYCLES_BY_RAS"/>
	<value value="0x04" name="RB_RBPERF_STARVE_CYCLES_BY_MARB"/>
	<value value="0x05" name="RB_RBPERF_STALL_CYCLES_BY_MARB"/>
	<value value="0x06" name="RB_RBPERF_STALL_CYCLES_BY_HLSQ"/>
	<value value="0x07" name="RB_RBPERF_RB_MARB_DATA"/>
	<value value="0x08" name="RB_RBPERF_SP_RB_QUAD"/>
	<value value="0x09" name="RB_RBPERF_RAS_EARLY_Z_QUADS"/>
	<value value="0x0a" name="RB_RBPERF_GMEM_CH0_READ"/>
	<value value="0x0b" name="RB_RBPERF_GMEM_CH1_READ"/>
	<value value="0x0c" name="RB_RBPERF_GMEM_CH0_WRITE"/>
	<value value="0x0d" name="RB_RBPERF_GMEM_CH1_WRITE"/>
	<value value="0x0e" name="RB_RBPERF_CP_CONTEXT_DONE"/>
	<value value="0x0f" name="RB_RBPERF_CP_CACHE_FLUSH"/>
	<value value="0x10" name="RB_RBPERF_CP_ZPASS_DONE"/>
</enum>

<enum name="a3xx_rbbm_perfcounter_select">
	<value value="0" name="RBBM_ALAWYS_ON"/>
	<value value="1" name="RBBM_VBIF_BUSY"/>
	<value value="2" name="RBBM_TSE_BUSY"/>
	<value value="3" name="RBBM_RAS_BUSY"/>
	<value value="4" name="RBBM_PC_DCALL_BUSY"/>
	<value value="5" name="RBBM_PC_VSD_BUSY"/>
	<value value="6" name="RBBM_VFD_BUSY"/>
	<value value="7" name="RBBM_VPC_BUSY"/>
	<value value="8" name="RBBM_UCHE_BUSY"/>
	<value value="9" name="RBBM_VSC_BUSY"/>
	<value value="10" name="RBBM_HLSQ_BUSY"/>
	<value value="11" name="RBBM_ANY_RB_BUSY"/>
	<value value="12" name="RBBM_ANY_TEX_BUSY"/>
	<value value="13" name="RBBM_ANY_USP_BUSY"/>
	<value value="14" name="RBBM_ANY_MARB_BUSY"/>
	<value value="15" name="RBBM_ANY_ARB_BUSY"/>
	<value value="16" name="RBBM_AHB_STATUS_BUSY"/>
	<value value="17" name="RBBM_AHB_STATUS_STALLED"/>
	<value value="18" name="RBBM_AHB_STATUS_TXFR"/>
	<value value="19" name="RBBM_AHB_STATUS_TXFR_SPLIT"/>
	<value value="20" name="RBBM_AHB_STATUS_TXFR_ERROR"/>
	<value value="21" name="RBBM_AHB_STATUS_LONG_STALL"/>
	<value value="22" name="RBBM_RBBM_STATUS_MASKED"/>
</enum>

<enum name="a3xx_sp_perfcounter_select">
	<value value="0x00" name="SP_LM_LOAD_INSTRUCTIONS"/>
	<value value="0x01" name="SP_LM_STORE_INSTRUCTIONS"/>
	<value value="0x02" name="SP_LM_ATOMICS"/>
	<value value="0x03" name="SP_UCHE_LOAD_INSTRUCTIONS"/>
	<value value="0x04" name="SP_UCHE_STORE_INSTRUCTIONS"/>
	<value value="0x05" name="SP_UCHE_ATOMICS"/>
	<value value="0x06" name="SP_VS_TEX_INSTRUCTIONS"/>
	<value value="0x07" name="SP_VS_CFLOW_INSTRUCTIONS"/>
	<value value="0x08" name="SP_VS_EFU_INSTRUCTIONS"/>
	<value value="0x09" name="SP_VS_FULL_ALU_INSTRUCTIONS"/>
	<value value="0x0a" name="SP_VS_HALF_ALU_INSTRUCTIONS"/>
	<value value="0x0b" name="SP_FS_TEX_INSTRUCTIONS"/>
	<value value="0x0c" name="SP_FS_CFLOW_INSTRUCTIONS"/>
	<value value="0x0d" name="SP_FS_EFU_INSTRUCTIONS"/>
	<value value="0x0e" name="SP_FS_FULL_ALU_INSTRUCTIONS"/>
	<value value="0x0f" name="SP_FS_HALF_ALU_INSTRUCTIONS"/>
	<value value="0x10" name="SP_FS_BARY_INSTRUCTIONS"/>
	<value value="0x11" name="SP_VS_INSTRUCTIONS"/>
	<value value="0x12" name="SP_FS_INSTRUCTIONS"/>
	<value value="0x13" name="SP_ADDR_LOCK_COUNT"/>
	<value value="0x14" name="SP_UCHE_READ_TRANS"/>
	<value value="0x15" name="SP_UCHE_WRITE_TRANS"/>
	<value value="0x16" name="SP_EXPORT_VPC_TRANS"/>
	<value value="0x17" name="SP_EXPORT_RB_TRANS"/>
	<value value="0x18" name="SP_PIXELS_KILLED"/>
	<value value="0x19" name="SP_ICL1_REQUESTS"/>
	<value value="0x1a" name="SP_ICL1_MISSES"/>
	<value value="0x1b" name="SP_ICL0_REQUESTS"/>
	<value value="0x1c" name="SP_ICL0_MISSES"/>
	<value value="0x1d" name="SP_ALU_ACTIVE_CYCLES"/>
	<value value="0x1e" name="SP_EFU_ACTIVE_CYCLES"/>
	<value value="0x1f" name="SP_STALL_CYCLES_BY_VPC"/>
	<value value="0x20" name="SP_STALL_CYCLES_BY_TP"/>
	<value value="0x21" name="SP_STALL_CYCLES_BY_UCHE"/>
	<value value="0x22" name="SP_STALL_CYCLES_BY_RB"/>
	<value value="0x23" name="SP_ACTIVE_CYCLES_ANY"/>
	<value value="0x24" name="SP_ACTIVE_CYCLES_ALL"/>
</enum>

<enum name="a3xx_tp_perfcounter_select">
	<value value="0x00" name="TPL1_TPPERF_L1_REQUESTS"/>
	<value value="0x01" name="TPL1_TPPERF_TP0_L1_REQUESTS"/>
	<value value="0x02" name="TPL1_TPPERF_TP0_L1_MISSES"/>
	<value value="0x03" name="TPL1_TPPERF_TP1_L1_REQUESTS"/>
	<value value="0x04" name="TPL1_TPPERF_TP1_L1_MISSES"/>
	<value value="0x05" name="TPL1_TPPERF_TP2_L1_REQUESTS"/>
	<value value="0x06" name="TPL1_TPPERF_TP2_L1_MISSES"/>
	<value value="0x07" name="TPL1_TPPERF_TP3_L1_REQUESTS"/>
	<value value="0x08" name="TPL1_TPPERF_TP3_L1_MISSES"/>
	<value value="0x09" name="TPL1_TPPERF_OUTPUT_TEXELS_POINT"/>
	<value value="0x0a" name="TPL1_TPPERF_OUTPUT_TEXELS_BILINEAR"/>
	<value value="0x0b" name="TPL1_TPPERF_OUTPUT_TEXELS_MIP"/>
	<value value="0x0c" name="TPL1_TPPERF_OUTPUT_TEXELS_ANISO"/>
	<value value="0x0d" name="TPL1_TPPERF_BILINEAR_OPS"/>
	<value value="0x0e" name="TPL1_TPPERF_QUADSQUADS_OFFSET"/>
	<value value="0x0f" name="TPL1_TPPERF_QUADQUADS_SHADOW"/>
	<value value="0x10" name="TPL1_TPPERF_QUADS_ARRAY"/>
	<value value="0x11" name="TPL1_TPPERF_QUADS_PROJECTION"/>
	<value value="0x12" name="TPL1_TPPERF_QUADS_GRADIENT"/>
	<value value="0x13" name="TPL1_TPPERF_QUADS_1D2D"/>
	<value value="0x14" name="TPL1_TPPERF_QUADS_3DCUBE"/>
	<value value="0x15" name="TPL1_TPPERF_ZERO_LOD"/>
	<value value="0x16" name="TPL1_TPPERF_OUTPUT_TEXELS"/>
	<value value="0x17" name="TPL1_TPPERF_ACTIVE_CYCLES_ANY"/>
	<value value="0x18" name="TPL1_TPPERF_ACTIVE_CYCLES_ALL"/>
	<value value="0x19" name="TPL1_TPPERF_STALL_CYCLES_BY_ARB"/>
	<value value="0x1a" name="TPL1_TPPERF_LATENCY"/>
	<value value="0x1b" name="TPL1_TPPERF_LATENCY_TRANS"/>
</enum>

<enum name="a3xx_vfd_perfcounter_select">
	<value value="0" name="VFD_PERF_UCHE_BYTE_FETCHED"/>
	<value value="1" name="VFD_PERF_UCHE_TRANS"/>
	<value value="2" name="VFD_PERF_VPC_BYPASS_COMPONENTS"/>
	<value value="3" name="VFD_PERF_FETCH_INSTRUCTIONS"/>
	<value value="4" name="VFD_PERF_DECODE_INSTRUCTIONS"/>
	<value value="5" name="VFD_PERF_ACTIVE_CYCLES"/>
	<value value="6" name="VFD_PERF_STALL_CYCLES_UCHE"/>
	<value value="7" name="VFD_PERF_STALL_CYCLES_HLSQ"/>
	<value value="8" name="VFD_PERF_STALL_CYCLES_VPC_BYPASS"/>
	<value value="9" name="VFD_PERF_STALL_CYCLES_VPC_ALLOC"/>
</enum>

<enum name="a3xx_vpc_perfcounter_select">
	<value value="0" name="VPC_PERF_SP_LM_PRIMITIVES"/>
	<value value="1" name="VPC_PERF_COMPONENTS_FROM_SP"/>
	<value value="2" name="VPC_PERF_SP_LM_COMPONENTS"/>
	<value value="3" name="VPC_PERF_ACTIVE_CYCLES"/>
	<value value="4" name="VPC_PERF_STALL_CYCLES_LM"/>
	<value value="5" name="VPC_PERF_STALL_CYCLES_RAS"/>
</enum>

<enum name="a3xx_uche_perfcounter_select">
	<value value="0x00" name="UCHE_UCHEPERF_VBIF_READ_BEATS_TP"/>
	<value value="0x01" name="UCHE_UCHEPERF_VBIF_READ_BEATS_VFD"/>
	<value value="0x02" name="UCHE_UCHEPERF_VBIF_READ_BEATS_HLSQ"/>
	<value value="0x03" name="UCHE_UCHEPERF_VBIF_READ_BEATS_MARB"/>
	<value value="0x04" name="UCHE_UCHEPERF_VBIF_READ_BEATS_SP"/>
	<value value="0x08" name="UCHE_UCHEPERF_READ_REQUESTS_TP"/>
	<value value="0x09" name="UCHE_UCHEPERF_READ_REQUESTS_VFD"/>
	<value value="0x0a" name="UCHE_UCHEPERF_READ_REQUESTS_HLSQ"/>
	<value value="0x0b" name="UCHE_UCHEPERF_READ_REQUESTS_MARB"/>
	<value value="0x0c" name="UCHE_UCHEPERF_READ_REQUESTS_SP"/>
	<value value="0x0d" name="UCHE_UCHEPERF_WRITE_REQUESTS_MARB"/>
	<value value="0x0e" name="UCHE_UCHEPERF_WRITE_REQUESTS_SP"/>
	<value value="0x0f" name="UCHE_UCHEPERF_TAG_CHECK_FAILS"/>
	<value value="0x10" name="UCHE_UCHEPERF_EVICTS"/>
	<value value="0x11" name="UCHE_UCHEPERF_FLUSHES"/>
	<value value="0x12" name="UCHE_UCHEPERF_VBIF_LATENCY_CYCLES"/>
	<value value="0x13" name="UCHE_UCHEPERF_VBIF_LATENCY_SAMPLES"/>
	<value value="0x14" name="UCHE_UCHEPERF_ACTIVE_CYCLES"/>
</enum>

<enum name="a3xx_intp_mode">
	<value name="SMOOTH" value="0"/>
	<value name="FLAT" value="1"/>
	<value name="ZERO" value="2"/>
	<value name="ONE" value="3"/>
</enum>

<enum name="a3xx_repl_mode">
	<value name="S" value="1"/>
	<value name="T" value="2"/>
	<value name="ONE_T" value="3"/>
</enum>

<domain name="A3XX" width="32">
	<!-- RBBM registers -->
	<reg32 offset="0x0000" name="RBBM_HW_VERSION"/>
	<reg32 offset="0x0001" name="RBBM_HW_RELEASE"/>
	<reg32 offset="0x0002" name="RBBM_HW_CONFIGURATION"/>
	<reg32 offset="0x0010" name="RBBM_CLOCK_CTL"/>
	<reg32 offset="0x0012" name="RBBM_SP_HYST_CNT"/>
	<reg32 offset="0x0018" name="RBBM_SW_RESET_CMD"/>
	<reg32 offset="0x0020" name="RBBM_AHB_CTL0"/>
	<reg32 offset="0x0021" name="RBBM_AHB_CTL1"/>
	<reg32 offset="0x0022" name="RBBM_AHB_CMD"/>
	<reg32 offset="0x0027" name="RBBM_AHB_ERROR_STATUS"/>
	<reg32 offset="0x002e" name="RBBM_GPR0_CTL"/>
	<reg32 offset="0x0030" name="RBBM_STATUS">
		<bitfield name="HI_BUSY" pos="0" type="boolean"/>
		<bitfield name="CP_ME_BUSY" pos="1" type="boolean"/>
		<bitfield name="CP_PFP_BUSY" pos="2" type="boolean"/>
		<bitfield name="CP_NRT_BUSY" pos="14" type="boolean"/>
		<bitfield name="VBIF_BUSY" pos="15" type="boolean"/>
		<bitfield name="TSE_BUSY" pos="16" type="boolean"/>
		<bitfield name="RAS_BUSY" pos="17" type="boolean"/>
		<bitfield name="RB_BUSY" pos="18" type="boolean"/>
		<bitfield name="PC_DCALL_BUSY" pos="19" type="boolean"/>
		<bitfield name="PC_VSD_BUSY" pos="20" type="boolean"/>
		<bitfield name="VFD_BUSY" pos="21" type="boolean"/>
		<bitfield name="VPC_BUSY" pos="22" type="boolean"/>
		<bitfield name="UCHE_BUSY" pos="23" type="boolean"/>
		<bitfield name="SP_BUSY" pos="24" type="boolean"/>
		<bitfield name="TPL1_BUSY" pos="25" type="boolean"/>
		<bitfield name="MARB_BUSY" pos="26" type="boolean"/>
		<bitfield name="VSC_BUSY" pos="27" type="boolean"/>
		<bitfield name="ARB_BUSY" pos="28" type="boolean"/>
		<bitfield name="HLSQ_BUSY" pos="29" type="boolean"/>
		<bitfield name="GPU_BUSY_NOHC" pos="30" type="boolean"/>
		<bitfield name="GPU_BUSY" pos="31" type="boolean"/>
	</reg32>
	<!-- used in fw CP_WAIT_FOR_IDLE, similar to NQWAIT_UNTIL on a2xx: -->
	<reg32 offset="0x0040" name="RBBM_NQWAIT_UNTIL"/>
	<reg32 offset="0x0033" name="RBBM_WAIT_IDLE_CLOCKS_CTL"/>
	<reg32 offset="0x0050" name="RBBM_INTERFACE_HANG_INT_CTL"/>
	<reg32 offset="0x0051" name="RBBM_INTERFACE_HANG_MASK_CTL0"/>
	<reg32 offset="0x0054" name="RBBM_INTERFACE_HANG_MASK_CTL1"/>
	<reg32 offset="0x0057" name="RBBM_INTERFACE_HANG_MASK_CTL2"/>
	<reg32 offset="0x005a" name="RBBM_INTERFACE_HANG_MASK_CTL3"/>

	<bitset name="A3XX_INT0">
		<bitfield name="RBBM_GPU_IDLE" pos="0" type="boolean"/>
		<bitfield name="RBBM_AHB_ERROR" pos="1" type="boolean"/>
		<bitfield name="RBBM_REG_TIMEOUT" pos="2" type="boolean"/>
		<bitfield name="RBBM_ME_MS_TIMEOUT" pos="3" type="boolean"/>
		<bitfield name="RBBM_PFP_MS_TIMEOUT" pos="4" type="boolean"/>
		<bitfield name="RBBM_ATB_BUS_OVERFLOW" pos="5" type="boolean"/>
		<bitfield name="VFD_ERROR" pos="6" type="boolean"/>
		<bitfield name="CP_SW_INT" pos="7" type="boolean"/>
		<bitfield name="CP_T0_PACKET_IN_IB" pos="8" type="boolean"/>
		<bitfield name="CP_OPCODE_ERROR" pos="9" type="boolean"/>
		<bitfield name="CP_RESERVED_BIT_ERROR" pos="10" type="boolean"/>
		<bitfield name="CP_HW_FAULT" pos="11" type="boolean"/>
		<bitfield name="CP_DMA" pos="12" type="boolean"/>
		<bitfield name="CP_IB2_INT" pos="13" type="boolean"/>
		<bitfield name="CP_IB1_INT" pos="14" type="boolean"/>
		<bitfield name="CP_RB_INT" pos="15" type="boolean"/>
		<bitfield name="CP_REG_PROTECT_FAULT" pos="16" type="boolean"/>
		<bitfield name="CP_RB_DONE_TS" pos="17" type="boolean"/>
		<bitfield name="CP_VS_DONE_TS" pos="18" type="boolean"/>
		<bitfield name="CP_PS_DONE_TS" pos="19" type="boolean"/>
		<bitfield name="CACHE_FLUSH_TS" pos="20" type="boolean"/>
		<bitfield name="CP_AHB_ERROR_HALT" pos="21" type="boolean"/>
		<bitfield name="MISC_HANG_DETECT" pos="24" type="boolean"/>
		<bitfield name="UCHE_OOB_ACCESS" pos="25" type="boolean"/>
	</bitset>


	<!--
		set in pm4 fw INVALID_JUMP_TABLE_ENTRY and CP_INTERRUPT (compare
		to CP_INT_STATUS in a2xx firmware), so this seems to be the a3xx
		way for fw to raise and irq:
	 -->
	<reg32 offset="0x0060" name="RBBM_INT_SET_CMD" type="A3XX_INT0"/>
	<reg32 offset="0x0061" name="RBBM_INT_CLEAR_CMD" type="A3XX_INT0"/>
	<reg32 offset="0x0063" name="RBBM_INT_0_MASK" type="A3XX_INT0"/>
	<reg32 offset="0x0064" name="RBBM_INT_0_STATUS" type="A3XX_INT0"/>
	<reg32 offset="0x0080" name="RBBM_PERFCTR_CTL">
		<bitfield name="ENABLE" pos="0" type="boolean"/>
	</reg32>
	<reg32 offset="0x0081" name="RBBM_PERFCTR_LOAD_CMD0"/>
	<reg32 offset="0x0082" name="RBBM_PERFCTR_LOAD_CMD1"/>
	<reg32 offset="0x0084" name="RBBM_PERFCTR_LOAD_VALUE_LO"/>
	<reg32 offset="0x0085" name="RBBM_PERFCTR_LOAD_VALUE_HI"/>
	<reg32 offset="0x0086" name="RBBM_PERFCOUNTER0_SELECT" type="a3xx_rbbm_perfcounter_select"/>
	<reg32 offset="0x0087" name="RBBM_PERFCOUNTER1_SELECT" type="a3xx_rbbm_perfcounter_select"/>
	<reg32 offset="0x0088" name="RBBM_GPU_BUSY_MASKED"/>
	<reg32 offset="0x0090" name="RBBM_PERFCTR_CP_0_LO"/>
	<reg32 offset="0x0091" name="RBBM_PERFCTR_CP_0_HI"/>
	<reg32 offset="0x0092" name="RBBM_PERFCTR_RBBM_0_LO"/>
	<reg32 offset="0x0093" name="RBBM_PERFCTR_RBBM_0_HI"/>
	<reg32 offset="0x0094" name="RBBM_PERFCTR_RBBM_1_LO"/>
	<reg32 offset="0x0095" name="RBBM_PERFCTR_RBBM_1_HI"/>
	<reg32 offset="0x0096" name="RBBM_PERFCTR_PC_0_LO"/>
	<reg32 offset="0x0097" name="RBBM_PERFCTR_PC_0_HI"/>
	<reg32 offset="0x0098" name="RBBM_PERFCTR_PC_1_LO"/>
	<reg32 offset="0x0099" name="RBBM_PERFCTR_PC_1_HI"/>
	<reg32 offset="0x009a" name="RBBM_PERFCTR_PC_2_LO"/>
	<reg32 offset="0x009b" name="RBBM_PERFCTR_PC_2_HI"/>
	<reg32 offset="0x009c" name="RBBM_PERFCTR_PC_3_LO"/>
	<reg32 offset="0x009d" name="RBBM_PERFCTR_PC_3_HI"/>
	<reg32 offset="0x009e" name="RBBM_PERFCTR_VFD_0_LO"/>
	<reg32 offset="0x009f" name="RBBM_PERFCTR_VFD_0_HI"/>
	<reg32 offset="0x00a0" name="RBBM_PERFCTR_VFD_1_LO"/>
	<reg32 offset="0x00a1" name="RBBM_PERFCTR_VFD_1_HI"/>
	<reg32 offset="0x00a2" name="RBBM_PERFCTR_HLSQ_0_LO"/>
	<reg32 offset="0x00a3" name="RBBM_PERFCTR_HLSQ_0_HI"/>
	<reg32 offset="0x00a4" name="RBBM_PERFCTR_HLSQ_1_LO"/>
	<reg32 offset="0x00a5" name="RBBM_PERFCTR_HLSQ_1_HI"/>
	<reg32 offset="0x00a6" name="RBBM_PERFCTR_HLSQ_2_LO"/>
	<reg32 offset="0x00a7" name="RBBM_PERFCTR_HLSQ_2_HI"/>
	<reg32 offset="0x00a8" name="RBBM_PERFCTR_HLSQ_3_LO"/>
	<reg32 offset="0x00a9" name="RBBM_PERFCTR_HLSQ_3_HI"/>
	<reg32 offset="0x00aa" name="RBBM_PERFCTR_HLSQ_4_LO"/>
	<reg32 offset="0x00ab" name="RBBM_PERFCTR_HLSQ_4_HI"/>
	<reg32 offset="0x00ac" name="RBBM_PERFCTR_HLSQ_5_LO"/>
	<reg32 offset="0x00ad" name="RBBM_PERFCTR_HLSQ_5_HI"/>
	<reg32 offset="0x00ae" name="RBBM_PERFCTR_VPC_0_LO"/>
	<reg32 offset="0x00af" name="RBBM_PERFCTR_VPC_0_HI"/>
	<reg32 offset="0x00b0" name="RBBM_PERFCTR_VPC_1_LO"/>
	<reg32 offset="0x00b1" name="RBBM_PERFCTR_VPC_1_HI"/>
	<reg32 offset="0x00b2" name="RBBM_PERFCTR_TSE_0_LO"/>
	<reg32 offset="0x00b3" name="RBBM_PERFCTR_TSE_0_HI"/>
	<reg32 offset="0x00b4" name="RBBM_PERFCTR_TSE_1_LO"/>
	<reg32 offset="0x00b5" name="RBBM_PERFCTR_TSE_1_HI"/>
	<reg32 offset="0x00b6" name="RBBM_PERFCTR_RAS_0_LO"/>
	<reg32 offset="0x00b7" name="RBBM_PERFCTR_RAS_0_HI"/>
	<reg32 offset="0x00b8" name="RBBM_PERFCTR_RAS_1_LO"/>
	<reg32 offset="0x00b9" name="RBBM_PERFCTR_RAS_1_HI"/>
	<reg32 offset="0x00ba" name="RBBM_PERFCTR_UCHE_0_LO"/>
	<reg32 offset="0x00bb" name="RBBM_PERFCTR_UCHE_0_HI"/>
	<reg32 offset="0x00bc" name="RBBM_PERFCTR_UCHE_1_LO"/>
	<reg32 offset="0x00bd" name="RBBM_PERFCTR_UCHE_1_HI"/>
	<reg32 offset="0x00be" name="RBBM_PERFCTR_UCHE_2_LO"/>
	<reg32 offset="0x00bf" name="RBBM_PERFCTR_UCHE_2_HI"/>
	<reg32 offset="0x00c0" name="RBBM_PERFCTR_UCHE_3_LO"/>
	<reg32 offset="0x00c1" name="RBBM_PERFCTR_UCHE_3_HI"/>
	<reg32 offset="0x00c2" name="RBBM_PERFCTR_UCHE_4_LO"/>
	<reg32 offset="0x00c3" name="RBBM_PERFCTR_UCHE_4_HI"/>
	<reg32 offset="0x00c4" name="RBBM_PERFCTR_UCHE_5_LO"/>
	<reg32 offset="0x00c5" name="RBBM_PERFCTR_UCHE_5_HI"/>
	<reg32 offset="0x00c6" name="RBBM_PERFCTR_TP_0_LO"/>
	<reg32 offset="0x00c7" name="RBBM_PERFCTR_TP_0_HI"/>
	<reg32 offset="0x00c8" name="RBBM_PERFCTR_TP_1_LO"/>
	<reg32 offset="0x00c9" name="RBBM_PERFCTR_TP_1_HI"/>
	<reg32 offset="0x00ca" name="RBBM_PERFCTR_TP_2_LO"/>
	<reg32 offset="0x00cb" name="RBBM_PERFCTR_TP_2_HI"/>
	<reg32 offset="0x00cc" name="RBBM_PERFCTR_TP_3_LO"/>
	<reg32 offset="0x00cd" name="RBBM_PERFCTR_TP_3_HI"/>
	<reg32 offset="0x00ce" name="RBBM_PERFCTR_TP_4_LO"/>
	<reg32 offset="0x00cf" name="RBBM_PERFCTR_TP_4_HI"/>
	<reg32 offset="0x00d0" name="RBBM_PERFCTR_TP_5_LO"/>
	<reg32 offset="0x00d1" name="RBBM_PERFCTR_TP_5_HI"/>
	<reg32 offset="0x00d2" name="RBBM_PERFCTR_SP_0_LO"/>
	<reg32 offset="0x00d3" name="RBBM_PERFCTR_SP_0_HI"/>
	<reg32 offset="0x00d4" name="RBBM_PERFCTR_SP_1_LO"/>
	<reg32 offset="0x00d5" name="RBBM_PERFCTR_SP_1_HI"/>
	<reg32 offset="0x00d6" name="RBBM_PERFCTR_SP_2_LO"/>
	<reg32 offset="0x00d7" name="RBBM_PERFCTR_SP_2_HI"/>
	<reg32 offset="0x00d8" name="RBBM_PERFCTR_SP_3_LO"/>
	<reg32 offset="0x00d9" name="RBBM_PERFCTR_SP_3_HI"/>
	<reg32 offset="0x00da" name="RBBM_PERFCTR_SP_4_LO"/>
	<reg32 offset="0x00db" name="RBBM_PERFCTR_SP_4_HI"/>
	<reg32 offset="0x00dc" name="RBBM_PERFCTR_SP_5_LO"/>
	<reg32 offset="0x00dd" name="RBBM_PERFCTR_SP_5_HI"/>
	<reg32 offset="0x00de" name="RBBM_PERFCTR_SP_6_LO"/>
	<reg32 offset="0x00df" name="RBBM_PERFCTR_SP_6_HI"/>
	<reg32 offset="0x00e0" name="RBBM_PERFCTR_SP_7_LO"/>
	<reg32 offset="0x00e1" name="RBBM_PERFCTR_SP_7_HI"/>
	<reg32 offset="0x00e2" name="RBBM_PERFCTR_RB_0_LO"/>
	<reg32 offset="0x00e3" name="RBBM_PERFCTR_RB_0_HI"/>
	<reg32 offset="0x00e4" name="RBBM_PERFCTR_RB_1_LO"/>
	<reg32 offset="0x00e5" name="RBBM_PERFCTR_RB_1_HI"/>
	<reg32 offset="0x00ea" name="RBBM_PERFCTR_PWR_0_LO"/>
	<reg32 offset="0x00eb" name="RBBM_PERFCTR_PWR_0_HI"/>
	<reg32 offset="0x00ec" name="RBBM_PERFCTR_PWR_1_LO"/>
	<reg32 offset="0x00ed" name="RBBM_PERFCTR_PWR_1_HI"/>
	<reg32 offset="0x0100" name="RBBM_RBBM_CTL"/>
	<reg32 offset="0x0111" name="RBBM_DEBUG_BUS_CTL"/>
	<reg32 offset="0x0112" name="RBBM_DEBUG_BUS_DATA_STATUS"/>

	<!-- CP registers -->
	<reg32 offset="0x01c9" name="CP_PFP_UCODE_ADDR"/>
	<reg32 offset="0x01ca" name="CP_PFP_UCODE_DATA"/>
	<reg32 offset="0x01cc" name="CP_ROQ_ADDR"/>
	<reg32 offset="0x01cd" name="CP_ROQ_DATA"/>
	<reg32 offset="0x01d1" name="CP_MERCIU_ADDR"/>
	<reg32 offset="0x01d2" name="CP_MERCIU_DATA"/>
	<reg32 offset="0x01d3" name="CP_MERCIU_DATA2"/>
	<!-- see a3xx_snapshot_cp_meq().. looks like the way to dump queue between pfp and pm4 -->
	<reg32 offset="0x01da" name="CP_MEQ_ADDR"/>
	<reg32 offset="0x01db" name="CP_MEQ_DATA"/>
	<reg32 offset="0x01f5" name="CP_WFI_PEND_CTR"/>
	<reg32 offset="0x039d" name="RBBM_PM_OVERRIDE2"/>

	<reg32 offset="0x0445" name="CP_PERFCOUNTER_SELECT" type="a3xx_cp_perfcounter_select"/>
	<reg32 offset="0x045c" name="CP_HW_FAULT"/>
	<reg32 offset="0x045e" name="CP_PROTECT_CTRL"/>
	<reg32 offset="0x045f" name="CP_PROTECT_STATUS"/>
	<array offset="0x0460" name="CP_PROTECT" stride="1" length="16">
		<reg32 offset="0x0" name="REG"/>
	</array>
	<reg32 offset="0x054d" name="CP_AHB_FAULT"/>

	<reg32 offset="0x0d00" name="SQ_GPR_MANAGEMENT"/>
	<reg32 offset="0x0d02" name="SQ_INST_STORE_MANAGMENT"/>
	<reg32 offset="0x0e1e" name="TP0_CHICKEN"/>

	<!-- these I guess or either SP or HLSQ since related to shader core setup: -->
	<reg32 offset="0x0e22" name="SP_GLOBAL_MEM_SIZE" type="uint">
		<doc>
			The pair of MEM_SIZE/ADDR registers get programmed
			in sequence with the size/addr of each buffer.
		</doc>
	</reg32>
	<reg32 offset="0x0e23" name="SP_GLOBAL_MEM_ADDR"/>

	<!-- GRAS registers -->
	<reg32 offset="0x2040" name="GRAS_CL_CLIP_CNTL">
		<bitfield name="IJ_PERSP_CENTER" pos="12" type="boolean"/>
		<bitfield name="IJ_NON_PERSP_CENTER" pos="13" type="boolean"/>
		<bitfield name="IJ_PERSP_CENTROID" pos="14" type="boolean"/>
		<bitfield name="IJ_NON_PERSP_CENTROID" pos="15" type="boolean"/>
		<bitfield name="CLIP_DISABLE" pos="16" type="boolean"/>
		<bitfield name="ZFAR_CLIP_DISABLE" pos="17" type="boolean"/>
		<bitfield name="VP_CLIP_CODE_IGNORE" pos="19" type="boolean"/>
		<bitfield name="VP_XFORM_DISABLE" pos="20" type="boolean"/>
		<bitfield name="PERSP_DIVISION_DISABLE" pos="21" type="boolean"/>
		<bitfield name="ZERO_GB_SCALE_Z" pos="22" type="boolean">
			<doc>aka clip_halfz</doc>
		</bitfield>
		<!-- set when gl_FragCoord.z is enabled in frag shader: -->
		<bitfield name="ZCOORD" pos="23" type="boolean"/>
		<bitfield name="WCOORD" pos="24" type="boolean"/>
		<!-- set when frag shader writes z (so early z test disabled: -->
		<bitfield name="ZCLIP_DISABLE" pos="25" type="boolean"/>
		<bitfield name="NUM_USER_CLIP_PLANES" low="26" high="28" type="uint"/>
	</reg32>
	<reg32 offset="0x2044" name="GRAS_CL_GB_CLIP_ADJ">
		<bitfield name="HORZ" low="0" high="9" type="uint"/>
		<bitfield name="VERT" low="10" high="19" type="uint"/>
	</reg32>
	<reg32 offset="0x2048" name="GRAS_CL_VPORT_XOFFSET" type="float"/>
	<reg32 offset="0x2049" name="GRAS_CL_VPORT_XSCALE" type="float"/>
	<reg32 offset="0x204a" name="GRAS_CL_VPORT_YOFFSET" type="float"/>
	<reg32 offset="0x204b" name="GRAS_CL_VPORT_YSCALE" type="float"/>
	<reg32 offset="0x204c" name="GRAS_CL_VPORT_ZOFFSET" type="float"/>
	<reg32 offset="0x204d" name="GRAS_CL_VPORT_ZSCALE" type="float"/>
	<reg32 offset="0x2068" name="GRAS_SU_POINT_MINMAX">
		<bitfield name="MIN" low="0" high="15" type="ufixed" radix="4"/>
		<bitfield name="MAX" low="16" high="31" type="ufixed" radix="4"/>
	</reg32>
	<reg32 offset="0x2069" name="GRAS_SU_POINT_SIZE" type="fixed" radix="4"/>
	<reg32 offset="0x206c" name="GRAS_SU_POLY_OFFSET_SCALE">
		<bitfield name="VAL" low="0" high="23" type="fixed" radix="20"/>
		<doc>range of -8.0 to 8.0</doc>
	</reg32>
	<reg32 offset="0x206d" name="GRAS_SU_POLY_OFFSET_OFFSET" radix="6" type="fixed">
		<doc>range of -512.0 to 512.0</doc>
	</reg32>
	<reg32 offset="0x2070" name="GRAS_SU_MODE_CONTROL">
		<bitfield name="CULL_FRONT" pos="0" type="boolean"/>
		<bitfield name="CULL_BACK" pos="1" type="boolean"/>
		<bitfield name="FRONT_CW" pos="2" type="boolean"/>
		<bitfield name="LINEHALFWIDTH" low="3" high="10" radix="2" type="fixed"/>
		<bitfield name="POLY_OFFSET" pos="11" type="boolean"/>
	</reg32>
	<reg32 offset="0x2072" name="GRAS_SC_CONTROL">
		<!-- complete wild-ass-guess for sizes of these bitfields.. -->
		<bitfield name="RENDER_MODE" low="4" high="7" type="a3xx_render_mode"/>
		<bitfield name="MSAA_SAMPLES" low="8" high="11" type="a3xx_msaa_samples"/>
		<bitfield name="RASTER_MODE" low="12" high="15"/>
	</reg32>

	<reg32 offset="0x2074" name="GRAS_SC_SCREEN_SCISSOR_TL" type="adreno_reg_xy"/>
	<reg32 offset="0x2075" name="GRAS_SC_SCREEN_SCISSOR_BR" type="adreno_reg_xy"/>
	<reg32 offset="0x2079" name="GRAS_SC_WINDOW_SCISSOR_TL" type="adreno_reg_xy"/>
	<reg32 offset="0x207a" name="GRAS_SC_WINDOW_SCISSOR_BR" type="adreno_reg_xy"/>

	<!-- RB registers -->
	<reg32 offset="0x20c0" name="RB_MODE_CONTROL">
		<!-- guess on the # of bits here.. -->
		<bitfield name="GMEM_BYPASS" pos="7" type="boolean"/>
		<doc>
			RENDER_MODE is RB_RESOLVE_PASS for gmem->mem, otherwise RB_RENDER_PASS
		</doc>
		<bitfield name="RENDER_MODE" low="8" high="10" type="a3xx_render_mode"/>
		<bitfield name="MRT" low="12" high="13" type="uint">
			<doc>render targets - 1</doc>
		</bitfield>
		<bitfield name="MARB_CACHE_SPLIT_MODE" pos="15" type="boolean"/>
		<bitfield name="PACKER_TIMER_ENABLE" pos="16" type="boolean"/>
	</reg32>
	<reg32 offset="0x20c1" name="RB_RENDER_CONTROL">
		<bitfield name="DUAL_COLOR_IN_ENABLE" pos="0" type="boolean"/>
		<bitfield name="YUV_IN_ENABLE" pos="1" type="boolean"/>
		<bitfield name="COV_VALUE_INPUT_ENABLE" pos="2" type="boolean"/>
		<!-- set when gl_FrontFacing is accessed in frag shader: -->
		<bitfield name="FACENESS" pos="3" type="boolean"/>
		<bitfield name="BIN_WIDTH" low="4" high="11" shr="5" type="uint"/>
		<bitfield name="DISABLE_COLOR_PIPE" pos="12" type="boolean"/>
		<!--
			ENABLE_GMEM not set on mem2gmem..  so possibly it is actually
			controlling blend or readback from GMEM??
		 -->
		<bitfield name="ENABLE_GMEM" pos="13" type="boolean"/>
		<bitfield name="COORD_MASK" low="14" high="17" type="hex"/>
		<bitfield name="I_CLAMP_ENABLE" pos="19" type="boolean"/>
		<bitfield name="COV_VALUE_OUTPUT_ENABLE" pos="20" type="boolean"/>
		<bitfield name="ALPHA_TEST" pos="22" type="boolean"/>
		<bitfield name="ALPHA_TEST_FUNC" low="24" high="26" type="adreno_compare_func"/>
		<bitfield name="ALPHA_TO_COVERAGE" pos="30" type="boolean"/>
		<bitfield name="ALPHA_TO_ONE" pos="31" type="boolean"/>
	</reg32>
	<reg32 offset="0x20c2" name="RB_MSAA_CONTROL">
		<bitfield name="DISABLE" pos="10" type="boolean"/>
		<bitfield name="SAMPLES" low="12" high="15" type="a3xx_msaa_samples"/>
		<bitfield name="SAMPLE_MASK" low="16" high="31" type="hex"/>
	</reg32>
	<reg32 offset="0x20c3" name="RB_ALPHA_REF">
		<bitfield name="UINT" low="8" high="15" type="hex"/>
		<bitfield name="FLOAT" low="16" high="31" type="float"/>
	</reg32>
	<array offset="0x20c4" name="RB_MRT" stride="4" length="4">
		<reg32 offset="0x0" name="CONTROL">
			<bitfield name="READ_DEST_ENABLE" pos="3" type="boolean"/>
			<!-- both these bits seem to get set when enabling GL_BLEND.. -->
			<bitfield name="BLEND" pos="4" type="boolean"/>
			<bitfield name="BLEND2" pos="5" type="boolean"/>
			<bitfield name="ROP_CODE" low="8" high="11" type="a3xx_rop_code"/>
			<bitfield name="DITHER_MODE" low="12" high="13" type="adreno_rb_dither_mode"/>
			<bitfield name="COMPONENT_ENABLE" low="24" high="27" type="hex"/>
		</reg32>
		<reg32 offset="0x1" name="BUF_INFO">
			<bitfield name="COLOR_FORMAT" low="0" high="5" type="a3xx_color_fmt"/>
			<bitfield name="COLOR_TILE_MODE" low="6" high="7" type="a3xx_tile_mode"/>
			<bitfield name="COLOR_SWAP" low="10" high="11" type="a3xx_color_swap"/>
			<bitfield name="COLOR_SRGB" pos="14" type="boolean"/>
			<doc>
				Pitch (actually, appears to be pitch in bytes, so really is a stride)
				in GMEM, so pitch of the current tile.
			</doc>
			<bitfield name="COLOR_BUF_PITCH" low="17" high="31" shr="5" type="uint"/>
		</reg32>
		<reg32 offset="0x2" name="BUF_BASE">
			<doc>offset into GMEM (or system memory address in bypass mode)</doc>
			<bitfield name="COLOR_BUF_BASE" low="4" high="31" shr="5" type="hex"/>
		</reg32>
		<reg32 offset="0x3" name="BLEND_CONTROL">
			<bitfield name="RGB_SRC_FACTOR" low="0" high="4" type="adreno_rb_blend_factor"/>
			<bitfield name="RGB_BLEND_OPCODE" low="5" high="7" type="a3xx_rb_blend_opcode"/>
			<bitfield name="RGB_DEST_FACTOR" low="8" high="12" type="adreno_rb_blend_factor"/>
			<bitfield name="ALPHA_SRC_FACTOR" low="16" high="20" type="adreno_rb_blend_factor"/>
			<bitfield name="ALPHA_BLEND_OPCODE" low="21" high="23" type="a3xx_rb_blend_opcode"/>
			<bitfield name="ALPHA_DEST_FACTOR" low="24" high="28" type="adreno_rb_blend_factor"/>
			<bitfield name="CLAMP_ENABLE" pos="29" type="boolean"/>
		</reg32>
	</array>

	<reg32 offset="0x20e4" name="RB_BLEND_RED">
		<bitfield name="UINT" low="0" high="7" type="hex"/>
		<bitfield name="FLOAT" low="16" high="31" type="float"/>
	</reg32>
	<reg32 offset="0x20e5" name="RB_BLEND_GREEN">
		<bitfield name="UINT" low="0" high="7" type="hex"/>
		<bitfield name="FLOAT" low="16" high="31" type="float"/>
	</reg32>
	<reg32 offset="0x20e6" name="RB_BLEND_BLUE">
		<bitfield name="UINT" low="0" high="7" type="hex"/>
		<bitfield name="FLOAT" low="16" high="31" type="float"/>
	</reg32>
	<reg32 offset="0x20e7" name="RB_BLEND_ALPHA">
		<bitfield name="UINT" low="0" high="7" type="hex"/>
		<bitfield name="FLOAT" low="16" high="31" type="float"/>
	</reg32>

	<reg32 offset="0x20e8" name="RB_CLEAR_COLOR_DW0"/>
	<reg32 offset="0x20e9" name="RB_CLEAR_COLOR_DW1"/>
	<reg32 offset="0x20ea" name="RB_CLEAR_COLOR_DW2"/>
	<reg32 offset="0x20eb" name="RB_CLEAR_COLOR_DW3"/>
	<reg32 offset="0x20ec" name="RB_COPY_CONTROL">
		<!-- not sure # of bits -->
		<bitfield name="MSAA_RESOLVE" low="0" high="1" type="a3xx_msaa_samples"/>
		<bitfield name="DEPTHCLEAR" pos="3" type="boolean"/>
		<bitfield name="MODE" low="4" high="6" type="adreno_rb_copy_control_mode"/>
		<bitfield name="MSAA_SRGB_DOWNSAMPLE" pos="7" type="boolean"/>
		<bitfield name="FASTCLEAR" low="8" high="11" type="hex"/>
		<bitfield name="DEPTH32_RESOLVE" pos="12" type="boolean"/> <!-- enabled on a Z32F copy -->
		<bitfield name="GMEM_BASE" low="14" high="31" shr="14" type="hex"/>
	</reg32>
	<reg32 offset="0x20ed" name="RB_COPY_DEST_BASE">
		<bitfield name="BASE" low="4" high="31" shr="5" type="hex"/>
	</reg32>
	<reg32 offset="0x20ee" name="RB_COPY_DEST_PITCH">
		<doc>actually, appears to be pitch in bytes, so really is a stride</doc>
		<!-- not actually sure about max pitch... -->
		<bitfield name="PITCH" low="0" high="31" shr="5" type="uint"/>
	</reg32>
	<reg32 offset="0x20ef" name="RB_COPY_DEST_INFO">
		<bitfield name="TILE" low="0" high="1" type="a3xx_tile_mode"/>
		<bitfield name="FORMAT" low="2" high="7" type="a3xx_color_fmt"/>
		<bitfield name="SWAP" low="8" high="9" type="a3xx_color_swap"/>
		<bitfield name="DITHER_MODE" low="10" high="11" type="adreno_rb_dither_mode"/>
		<bitfield name="COMPONENT_ENABLE" low="14" high="17" type="hex"/>
		<bitfield name="ENDIAN" low="18" high="20" type="adreno_rb_surface_endian"/>
	</reg32>
	<reg32 offset="0x2100" name="RB_DEPTH_CONTROL">
		<!--
			guessing that this matches a2xx with the stencil fields
			moved out into RB_STENCIL_CONTROL?
		 -->
		<bitfield name="FRAG_WRITES_Z" pos="0" type="boolean"/>
		<bitfield name="Z_TEST_ENABLE" pos="1" type="boolean"/>
		<bitfield name="Z_WRITE_ENABLE" pos="2" type="boolean"/>
		<bitfield name="EARLY_Z_DISABLE" pos="3" type="boolean"/>
		<bitfield name="ZFUNC" low="4" high="6" type="adreno_compare_func"/>
		<bitfield name="Z_CLAMP_ENABLE" pos="7" type="boolean"/>
		<doc>Z_READ_ENABLE bit is set for zfunc other than GL_ALWAYS or GL_NEVER</doc>
		<bitfield name="Z_READ_ENABLE" pos="31" type="boolean"/>
	</reg32>
	<reg32 offset="0x2101" name="RB_DEPTH_CLEAR">
		<doc>seems to be always set to 0x00000000</doc>
	</reg32>
	<reg32 offset="0x2102" name="RB_DEPTH_INFO">
		<bitfield name="DEPTH_FORMAT" low="0" high="1" type="adreno_rb_depth_format"/>
		<doc>
			DEPTH_BASE is offset in GMEM to depth/stencil buffer, ie
			bin_w * bin_h / 1024 (possible rounded up to multiple of
			something??  ie. 39 becomes 40, 78 becomes 80.. 75 becomes
			80.. so maybe it needs to be multiple of 8??
		</doc>
		<bitfield name="DEPTH_BASE" low="11" high="31" shr="12" type="hex"/>
	</reg32>
	<reg32 offset="0x2103" name="RB_DEPTH_PITCH" shr="3" type="uint">
		<doc>
			Pitch of depth buffer or combined depth+stencil buffer
			in z24s8 cases.
		</doc>
	</reg32>
	<reg32 offset="0x2104" name="RB_STENCIL_CONTROL">
		<bitfield name="STENCIL_ENABLE" pos="0" type="boolean"/>
		<bitfield name="STENCIL_ENABLE_BF" pos="1" type="boolean"/>
		<!--
			set for stencil operations that require read from stencil
			buffer, but not for example for stencil clear (which does
			not require read).. so guessing this is analogous to
			READ_DEST_ENABLE for color buffer..
		 -->
		<bitfield name="STENCIL_READ" pos="2" type="boolean"/>
		<bitfield name="FUNC" low="8" high="10" type="adreno_compare_func"/>
		<bitfield name="FAIL" low="11" high="13" type="adreno_stencil_op"/>
		<bitfield name="ZPASS" low="14" high="16" type="adreno_stencil_op"/>
		<bitfield name="ZFAIL" low="17" high="19" type="adreno_stencil_op"/>
		<bitfield name="FUNC_BF" low="20" high="22" type="adreno_compare_func"/>
		<bitfield name="FAIL_BF" low="23" high="25" type="adreno_stencil_op"/>
		<bitfield name="ZPASS_BF" low="26" high="28" type="adreno_stencil_op"/>
		<bitfield name="ZFAIL_BF" low="29" high="31" type="adreno_stencil_op"/>
	</reg32>
	<reg32 offset="0x2105" name="RB_STENCIL_CLEAR">
		<doc>seems to be always set to 0x00000000</doc>
	</reg32>
	<reg32 offset="0x2106" name="RB_STENCIL_INFO">
		<doc>Base address for stencil when not using interleaved depth/stencil</doc>
		<bitfield name="STENCIL_BASE" low="11" high="31" shr="12" type="hex"/>
	</reg32>
	<reg32 offset="0x2107" name="RB_STENCIL_PITCH" shr="3" type="uint">
		<doc>pitch of stencil buffer when not using interleaved depth/stencil</doc>
	</reg32>
	<reg32 offset="0x2108" name="RB_STENCILREFMASK" type="adreno_rb_stencilrefmask"/>
	<reg32 offset="0x2109" name="RB_STENCILREFMASK_BF" type="adreno_rb_stencilrefmask"/>
	<!-- VSC == visibility stream c?? -->
	<reg32 offset="0x210c" name="RB_LRZ_VSC_CONTROL">
		<doc>seems to be set to 0x00000002 during binning pass</doc>
		<bitfield name="BINNING_ENABLE" pos="1" type="boolean"/>
	</reg32>
	<reg32 offset="0x210e" name="RB_WINDOW_OFFSET">
		<doc>X/Y offset of current bin</doc>
		<bitfield name="X" low="0" high="15" type="uint"/>
		<bitfield name="Y" low="16" high="31" type="uint"/>
	</reg32>
	<reg32 offset="0x2110" name="RB_SAMPLE_COUNT_CONTROL">
		<bitfield name="RESET" pos="0" type="boolean"/>
		<bitfield name="COPY" pos="1" type="boolean"/>
	</reg32>
	<reg32 offset="0x2111" name="RB_SAMPLE_COUNT_ADDR"/>
	<reg32 offset="0x2114" name="RB_Z_CLAMP_MIN"/>
	<reg32 offset="0x2115" name="RB_Z_CLAMP_MAX"/>

	<!-- PC registers -->
	<reg32 offset="0x21e1" name="VGT_BIN_BASE">
		<doc>
			seems to be where firmware writes BIN_DATA_ADDR from
			CP_SET_BIN_DATA packet..  probably should be called
			PC_BIN_BASE (just using name from yamato for now)
		</doc>
	</reg32>
	<reg32 offset="0x21e2" name="VGT_BIN_SIZE">
		<doc>probably should be PC_BIN_SIZE</doc>
	</reg32>
	<reg32 offset="0x21e4" name="PC_VSTREAM_CONTROL">
		<doc>SIZE is current pipe width * height (in tiles)</doc>
		<bitfield name="SIZE" low="16" high="21" type="uint"/>
		<doc>
			N is some sort of slot # between 0..(SIZE-1).  In case
			multiple tiles use same pipe, each tile gets unique slot #
		</doc>
		<bitfield name="N" low="22" high="26" type="uint"/>
	</reg32>
	<reg32 offset="0x21ea" name="PC_VERTEX_REUSE_BLOCK_CNTL"/>
	<reg32 offset="0x21ec" name="PC_PRIM_VTX_CNTL">
		<doc>
			STRIDE_IN_VPC: ALIGN(next_outloc - 8, 4) / 4
			(but, in cases where you'd expect 1, the blob driver uses
			2, so possibly 0 (no varying) or minimum of 2)
		</doc>
		<bitfield name="STRIDE_IN_VPC" low="0" high="4" type="uint"/>
		<bitfield name="POLYMODE_FRONT_PTYPE" low="5" high="7" type="adreno_pa_su_sc_draw"/>
		<bitfield name="POLYMODE_BACK_PTYPE" low="8" high="10" type="adreno_pa_su_sc_draw"/>
		<bitfield name="POLYMODE_ENABLE" pos="12" type="boolean"/>
		<bitfield name="PRIMITIVE_RESTART" pos="20" type="boolean"/>
		<bitfield name="PROVOKING_VTX_LAST" pos="25" type="boolean"/>
		<!-- PSIZE bit set if gl_PointSize written: -->
		<bitfield name="PSIZE" pos="26" type="boolean"/>
	</reg32>
	<reg32 offset="0x21ed" name="PC_RESTART_INDEX"/>

	<!-- HLSQ registers -->
	<bitset name="a3xx_hlsq_vs_fs_control_reg" inline="yes">
		<bitfield name="CONSTLENGTH" low="0" high="9" type="uint"/>
		<bitfield name="CONSTSTARTOFFSET" low="12" high="20" type="uint"/>
		<bitfield name="INSTRLENGTH" low="24" high="31" type="uint"/>
	</bitset>
	<bitset name="a3xx_hlsq_const_vs_fs_presv_range_reg" inline="yes">
		<!-- are these a3xx_regid?? -->
		<bitfield name="STARTENTRY" low="0" high="8"/>
		<bitfield name="ENDENTRY" low="16" high="24"/>
	</bitset>

	<reg32 offset="0x2200" name="HLSQ_CONTROL_0_REG">
		<bitfield name="FSTHREADSIZE" low="4" high="5" type="a3xx_threadsize"/>
		<bitfield name="FSSUPERTHREADENABLE" pos="6" type="boolean"/>
		<bitfield name="COMPUTEMODE" pos="8" type="boolean"/>
		<bitfield name="SPSHADERRESTART" pos="9" type="boolean"/>
		<bitfield name="RESERVED2" pos="10" type="boolean"/>
		<bitfield name="CYCLETIMEOUTLIMITVPC" low="12" high="23" type="uint"/>
		<bitfield name="FSONLYTEX" pos="25" type="boolean"/>
		<bitfield name="CHUNKDISABLE" pos="26" type="boolean"/>
		<bitfield name="CONSTMODE" pos="27" type="uint"/>
		<bitfield name="LAZYUPDATEDISABLE" pos="28" type="boolean"/>
		<bitfield name="SPCONSTFULLUPDATE" pos="29" type="boolean"/>
		<bitfield name="TPFULLUPDATE" pos="30" type="boolean"/>
		<bitfield name="SINGLECONTEXT" pos="31" type="boolean"/>
	</reg32>
	<reg32 offset="0x2201" name="HLSQ_CONTROL_1_REG">
		<bitfield name="VSTHREADSIZE" low="6" high="7" type="a3xx_threadsize"/>
		<bitfield name="VSSUPERTHREADENABLE" pos="8" type="boolean"/>
		<bitfield name="FRAGCOORDXYREGID" low="16" high="23" type="a3xx_regid"/>
		<bitfield name="FRAGCOORDZWREGID" low="24" high="31" type="a3xx_regid"/>
	</reg32>
	<reg32 offset="0x2202" name="HLSQ_CONTROL_2_REG">
		<bitfield name="FACENESSREGID" low="2" high="9" type="a3xx_regid"/>
		<bitfield name="COVVALUEREGID" low="18" high="25" type="a3xx_regid"/>
		<bitfield name="PRIMALLOCTHRESHOLD" low="26" high="31" type="uint"/>
	</reg32>
	<reg32 offset="0x2203" name="HLSQ_CONTROL_3_REG">
		<bitfield name="IJPERSPCENTERREGID" low="0" high="7" type="a3xx_regid"/>
		<bitfield name="IJNONPERSPCENTERREGID" low="8" high="15" type="a3xx_regid"/>
		<bitfield name="IJPERSPCENTROIDREGID" low="16" high="23" type="a3xx_regid"/>
		<bitfield name="IJNONPERSPCENTROIDREGID" low="24" high="31" type="a3xx_regid"/>
	</reg32>
	<reg32 offset="0x2204" name="HLSQ_VS_CONTROL_REG" type="a3xx_hlsq_vs_fs_control_reg"/>
	<reg32 offset="0x2205" name="HLSQ_FS_CONTROL_REG" type="a3xx_hlsq_vs_fs_control_reg"/>
	<reg32 offset="0x2206" name="HLSQ_CONST_VSPRESV_RANGE_REG" type="a3xx_hlsq_const_vs_fs_presv_range_reg"/>
	<reg32 offset="0x2207" name="HLSQ_CONST_FSPRESV_RANGE_REG" type="a3xx_hlsq_const_vs_fs_presv_range_reg"/>
	<reg32 offset="0x220a" name="HLSQ_CL_NDRANGE_0_REG">
		<bitfield name="WORKDIM" low="0" high="1" type="uint"/>
		<bitfield name="LOCALSIZE0" low="2" high="11" type="uint"/>
		<bitfield name="LOCALSIZE1" low="12" high="21" type="uint"/>
		<bitfield name="LOCALSIZE2" low="22" high="31" type="uint"/>
	</reg32>
	<array offset="0x220b" name="HLSQ_CL_GLOBAL_WORK" stride="2" length="3">
		<doc>indexed by dimension</doc>
		<reg32 offset="0" name="SIZE" type="uint"/>
		<reg32 offset="1" name="OFFSET" type="uint"/>
	</array>
	<reg32 offset="0x2211" name="HLSQ_CL_CONTROL_0_REG"/>
	<reg32 offset="0x2212" name="HLSQ_CL_CONTROL_1_REG"/>
	<reg32 offset="0x2214" name="HLSQ_CL_KERNEL_CONST_REG"/>
	<array offset="0x2215" name="HLSQ_CL_KERNEL_GROUP" stride="1" length="3">
		<doc>indexed by dimension, global_size / local_size</doc>
		<reg32 offset="0" name="RATIO" type="uint"/>
	</array>
	<reg32 offset="0x2216" name="HLSQ_CL_KERNEL_GROUP_Y_REG" type="uint"/>
	<reg32 offset="0x2217" name="HLSQ_CL_KERNEL_GROUP_Z_REG" type="uint"/>
	<reg32 offset="0x221a" name="HLSQ_CL_WG_OFFSET_REG"/>

	<!-- VFD registers -->
	<reg32 offset="0x2240" name="VFD_CONTROL_0">
		<doc>
			TOTALATTRTOVS is # of attributes to vertex shader, in register
			slots (ie. vec4+vec3 -> 7)
		</doc>
		<bitfield name="TOTALATTRTOVS" low="0" high="17" type="uint"/>
		<bitfield name="PACKETSIZE" low="18" high="21" type="uint"/>
		<doc>STRMDECINSTRCNT is # of VFD_DECODE_INSTR registers valid</doc>
		<bitfield name="STRMDECINSTRCNT" low="22" high="26" type="uint"/>
		<doc>STRMFETCHINSTRCNT is # of VFD_FETCH_INSTR registers valid</doc>
		<bitfield name="STRMFETCHINSTRCNT" low="27" high="31" type="uint"/>
	</reg32>
	<reg32 offset="0x2241" name="VFD_CONTROL_1">
		<doc>MAXSTORAGE could be # of attributes/vbo's</doc>
		<bitfield name="MAXSTORAGE" low="0" high="3" type="uint"/>
		<bitfield name="MAXTHRESHOLD" low="4" high="7" type="uint"/>
		<bitfield name="MINTHRESHOLD" low="8" high="11" type="uint"/>
		<bitfield name="REGID4VTX" low="16" high="23" type="a3xx_regid"/>
		<bitfield name="REGID4INST" low="24" high="31" type="a3xx_regid"/>
	</reg32>
	<reg32 offset="0x2242" name="VFD_INDEX_MIN" type="uint"/>
	<reg32 offset="0x2243" name="VFD_INDEX_MAX" type="uint"/>
	<reg32 offset="0x2244" name="VFD_INSTANCEID_OFFSET" type="uint"/>
	<reg32 offset="0x2245" name="VFD_INDEX_OFFSET" type="uint"/>
	<array offset="0x2246" name="VFD_FETCH" stride="2" length="16">
		<reg32 offset="0x0" name="INSTR_0">
			<bitfield name="FETCHSIZE" low="0" high="6" type="uint"/>
			<bitfield name="BUFSTRIDE" low="7" high="15" type="uint"/>
			<bitfield name="INSTANCED" pos="16" type="boolean"/>
			<bitfield name="SWITCHNEXT" pos="17" type="boolean"/>
			<bitfield name="INDEXCODE" low="18" high="23" type="uint"/>
			<bitfield name="STEPRATE" low="24" high="31" type="uint"/>
		</reg32>
		<reg32 offset="0x1" name="INSTR_1"/>
	</array>
	<array offset="0x2266" name="VFD_DECODE" stride="1" length="16">
		<reg32 offset="0x0" name="INSTR">
			<bitfield name="WRITEMASK" low="0" high="3" type="hex"/>
			<!-- not sure if this is a bit flag and another flag above it, or?? -->
			<bitfield name="CONSTFILL" pos="4" type="boolean"/>
			<bitfield name="FORMAT" low="6" high="11" type="a3xx_vtx_fmt"/>
			<bitfield name="REGID" low="12" high="19" type="a3xx_regid"/>
			<bitfield name="INT" pos="20" type="boolean"/>
			<doc>SHIFTCNT appears to be size, ie. FLOAT_32_32_32 is 12, and BYTE_8 is 1</doc>
			<bitfield name="SWAP" low="22" high="23" type="a3xx_color_swap"/>
			<bitfield name="SHIFTCNT" low="24" high="28" type="uint"/>
			<bitfield name="LASTCOMPVALID" pos="29" type="boolean"/>
			<bitfield name="SWITCHNEXT" pos="30" type="boolean"/>
		</reg32>
	</array>
	<reg32 offset="0x227e" name="VFD_VS_THREADING_THRESHOLD">
		<bitfield name="REGID_THRESHOLD" low="0" high="3" type="uint"/>
		<!-- <bitfield name="RESERVED6" low="4" high="7" type="uint"/> -->
		<bitfield name="REGID_VTXCNT" low="8" high="15" type="a3xx_regid"/>
	</reg32>

	<!-- VPC registers -->
	<reg32 offset="0x2280" name="VPC_ATTR">
		<bitfield name="TOTALATTR" low="0" high="8" type="uint"/>
		<!-- PSIZE bit set if gl_PointSize written: -->
		<bitfield name="PSIZE" pos="9" type="boolean"/>
		<bitfield name="THRDASSIGN" low="12" high="27" type="uint"/>
		<bitfield name="LMSIZE" low="28" high="31" type="uint"/>
	</reg32>
	<reg32 offset="0x2281" name="VPC_PACK">
		<!-- these are always seem to be set to same as TOTALATTR -->
		<bitfield name="NUMFPNONPOSVAR" low="8" high="15" type="uint"/>
		<bitfield name="NUMNONPOSVSVAR" low="16" high="23" type="uint"/>
	</reg32>
	<!--
		varying interpolate mode.  One field per scalar/component
		(since varying slots are scalar, so things don't have to
		be aligned to vec4).
		4 regs * 16 scalar components each => 16 vec4
	 -->
	<array offset="0x2282" name="VPC_VARYING_INTERP" stride="1" length="4">
		<reg32 offset="0x0" name="MODE">
			<bitfield name="C0" low="0"  high="1"  type="a3xx_intp_mode"/>
			<bitfield name="C1" low="2"  high="3"  type="a3xx_intp_mode"/>
			<bitfield name="C2" low="4"  high="5"  type="a3xx_intp_mode"/>
			<bitfield name="C3" low="6"  high="7"  type="a3xx_intp_mode"/>
			<bitfield name="C4" low="8"  high="9"  type="a3xx_intp_mode"/>
			<bitfield name="C5" low="10" high="11" type="a3xx_intp_mode"/>
			<bitfield name="C6" low="12" high="13" type="a3xx_intp_mode"/>
			<bitfield name="C7" low="14" high="15" type="a3xx_intp_mode"/>
			<bitfield name="C8" low="16" high="17" type="a3xx_intp_mode"/>
			<bitfield name="C9" low="18" high="19" type="a3xx_intp_mode"/>
			<bitfield name="CA" low="20" high="21" type="a3xx_intp_mode"/>
			<bitfield name="CB" low="22" high="23" type="a3xx_intp_mode"/>
			<bitfield name="CC" low="24" high="25" type="a3xx_intp_mode"/>
			<bitfield name="CD" low="26" high="27" type="a3xx_intp_mode"/>
			<bitfield name="CE" low="28" high="29" type="a3xx_intp_mode"/>
			<bitfield name="CF" low="30" high="31" type="a3xx_intp_mode"/>
		</reg32>
	</array>
	<array offset="0x2286" name="VPC_VARYING_PS_REPL" stride="1" length="4">
		<reg32 offset="0x0" name="MODE">
			<bitfield name="C0" low="0"  high="1"  type="a3xx_repl_mode"/>
			<bitfield name="C1" low="2"  high="3"  type="a3xx_repl_mode"/>
			<bitfield name="C2" low="4"  high="5"  type="a3xx_repl_mode"/>
			<bitfield name="C3" low="6"  high="7"  type="a3xx_repl_mode"/>
			<bitfield name="C4" low="8"  high="9"  type="a3xx_repl_mode"/>
			<bitfield name="C5" low="10" high="11" type="a3xx_repl_mode"/>
			<bitfield name="C6" low="12" high="13" type="a3xx_repl_mode"/>
			<bitfield name="C7" low="14" high="15" type="a3xx_repl_mode"/>
			<bitfield name="C8" low="16" high="17" type="a3xx_repl_mode"/>
			<bitfield name="C9" low="18" high="19" type="a3xx_repl_mode"/>
			<bitfield name="CA" low="20" high="21" type="a3xx_repl_mode"/>
			<bitfield name="CB" low="22" high="23" type="a3xx_repl_mode"/>
			<bitfield name="CC" low="24" high="25" type="a3xx_repl_mode"/>
			<bitfield name="CD" low="26" high="27" type="a3xx_repl_mode"/>
			<bitfield name="CE" low="28" high="29" type="a3xx_repl_mode"/>
			<bitfield name="CF" low="30" high="31" type="a3xx_repl_mode"/>
		</reg32>
	</array>
	<reg32 offset="0x228a" name="VPC_VARY_CYLWRAP_ENABLE_0"/>
	<reg32 offset="0x228b" name="VPC_VARY_CYLWRAP_ENABLE_1"/>

	<!-- SP registers -->
	<bitset name="a3xx_vs_fs_length_reg" inline="yes">
		<bitfield name="SHADERLENGTH" low="0" high="31" type="uint"/>
	</bitset>

	<bitset name="sp_vs_fs_obj_offset_reg" inline="yes">
		<bitfield name="FIRSTEXECINSTROFFSET" low="0" high="15" type="uint"/>
		<doc>
			From register spec:
			SP_FS_OBJ_OFFSET_REG.CONSTOBJECTSTARTOFFSET [16:24]: Constant object
			start offset in on chip RAM,
			128bit aligned
		</doc>
		<bitfield name="CONSTOBJECTOFFSET" low="16" high="24" type="uint"/>
		<bitfield name="SHADEROBJOFFSET" low="25" high="31" type="uint"/>
	</bitset>

	<reg32 offset="0x22c0" name="SP_SP_CTRL_REG">
		<!-- this bit is set during resolve pass: -->
		<bitfield name="RESOLVE" pos="16" type="boolean"/>
		<bitfield name="CONSTMODE" pos="18" type="uint"/>
		<bitfield name="BINNING" pos="19" type="boolean"/>
		<bitfield name="SLEEPMODE" low="20" high="21" type="uint"/>
		<!-- L0MODE==1 when oxiliForceSpL0ModeBuffer=1 -->
		<bitfield name="L0MODE" low="22" high="23" type="uint"/>
	</reg32>
	<reg32 offset="0x22c4" name="SP_VS_CTRL_REG0">
		<bitfield name="THREADMODE" pos="0" type="a3xx_threadmode"/>
		<bitfield name="INSTRBUFFERMODE" pos="1" type="a3xx_instrbuffermode"/>
		<!-- maybe CACHEINVALID is two bits?? -->
		<bitfield name="CACHEINVALID" pos="2" type="boolean"/>
		<bitfield name="ALUSCHMODE" pos="3" type="boolean"/>
		<doc>
			The full/half register footprint is in units of four components,
			so if r0.x is used, that counts as all of r0.[xyzw] as used.
			There are separate full/half register footprint values as the
			full and half registers are independent (not overlapping).
			Presumably the thread scheduler hardware allocates the full/half
			register names from the actual physical register file and
			handles the register renaming.
		</doc>
		<bitfield name="HALFREGFOOTPRINT" low="4" high="9" type="uint"/>
		<bitfield name="FULLREGFOOTPRINT" low="10" high="15" type="uint"/>
		<bitfield name="THREADSIZE" pos="20" type="a3xx_threadsize"/>
		<bitfield name="SUPERTHREADMODE" pos="21" type="boolean"/>
		<doc>
			From regspec:
			SP_FS_CTRL_REG0.FS_LENGTH [31:24]: FS length, unit = 256bits.
			If bit31 is 1, it means overflow
			or any long shader.
		</doc>
		<bitfield name="LENGTH" low="24" high="31" type="uint"/>
	</reg32>
	<reg32 offset="0x22c5" name="SP_VS_CTRL_REG1">
		<bitfield name="CONSTLENGTH" low="0" high="9" type="uint"/>
		<!--
			not sure about full vs half const.. I can't get blob generate
			something with a mediump/lowp uniform.
		 -->
		<bitfield name="CONSTFOOTPRINT" low="10" high="19" type="uint"/>
		<bitfield name="INITIALOUTSTANDING" low="24" high="30" type="uint"/>
	</reg32>
	<reg32 offset="0x22c6" name="SP_VS_PARAM_REG">
		<bitfield name="POSREGID" low="0" high="7" type="a3xx_regid"/>
		<bitfield name="PSIZEREGID" low="8" high="15" type="a3xx_regid"/>
		<bitfield name="POS2DMODE" pos="16" type="boolean"/>
		<bitfield name="TOTALVSOUTVAR" low="20" high="24" type="uint"/>
	</reg32>
	<array offset="0x22c7" name="SP_VS_OUT" stride="1" length="8">
		<reg32 offset="0x0" name="REG">
			<bitfield name="A_REGID" low="0" high="7" type="a3xx_regid"/>
			<bitfield name="A_HALF" pos="8" type="boolean"/>
			<bitfield name="A_COMPMASK" low="9" high="12" type="hex"/>
			<bitfield name="B_REGID" low="16" high="23" type="a3xx_regid"/>
			<bitfield name="B_HALF" pos="24" type="boolean"/>
			<bitfield name="B_COMPMASK" low="25" high="28" type="hex"/>
		</reg32>
	</array>
	<array offset="0x22d0" name="SP_VS_VPC_DST" stride="1" length="4">
		<reg32 offset="0x0" name="REG">
			<doc>
				These seem to be offsets for storage of the varyings.
				Always seems to start from 8, possibly loc 0 and 4
				are for gl_Position and gl_PointSize?
			</doc>
			<bitfield name="OUTLOC0" low="0" high="6" type="uint"/>
			<bitfield name="OUTLOC1" low="8" high="14" type="uint"/>
			<bitfield name="OUTLOC2" low="16" high="22" type="uint"/>
			<bitfield name="OUTLOC3" low="24" high="30" type="uint"/>
		</reg32>
	</array>
	<reg32 offset="0x22d4" name="SP_VS_OBJ_OFFSET_REG" type="sp_vs_fs_obj_offset_reg"/>
	<doc>
		SP_VS_OBJ_START_REG contains pointer to the vertex shader program,
		immediately followed by the binning shader program (although I
		guess that is probably just re-using the same gpu buffer)
	</doc>
	<reg32 offset="0x22d5" name="SP_VS_OBJ_START_REG"/>
	<reg32 offset="0x22d6" name="SP_VS_PVT_MEM_PARAM_REG">
		<bitfield name="MEMSIZEPERITEM" low="0" high="7" shr="7">
			<doc>The size of memory that ldp/stp can address, in 128 byte increments.</doc>
		</bitfield>
		<bitfield name="HWSTACKOFFSET" low="8" high="23" type="uint"/>
		<bitfield name="HWSTACKSIZEPERTHREAD" low="24" high="31" type="uint"/>
	</reg32>
	<reg32 offset="0x22d7" name="SP_VS_PVT_MEM_ADDR_REG">
		<bitfield name="BURSTLEN" low="0" high="4"/>
		<bitfield name="SHADERSTARTADDRESS" shr="5" low="5" high="31"/>
	</reg32>
	<reg32 offset="0x22d8" name="SP_VS_PVT_MEM_SIZE_REG"/>
	<reg32 offset="0x22df" name="SP_VS_LENGTH_REG" type="a3xx_vs_fs_length_reg"/>
	<reg32 offset="0x22e0" name="SP_FS_CTRL_REG0">
		<bitfield name="THREADMODE" pos="0" type="a3xx_threadmode"/>
		<bitfield name="INSTRBUFFERMODE" pos="1" type="a3xx_instrbuffermode"/>
		<!-- maybe CACHEINVALID is two bits?? -->
		<bitfield name="CACHEINVALID" pos="2" type="boolean"/>
		<bitfield name="ALUSCHMODE" pos="3" type="boolean"/>
		<doc>
			The full/half register footprint is in units of four components,
			so if r0.x is used, that counts as all of r0.[xyzw] as used.
			There are separate full/half register footprint values as the
			full and half registers are independent (not overlapping).
			Presumably the thread scheduler hardware allocates the full/half
			register names from the actual physical register file and
			handles the register renaming.
		</doc>
		<bitfield name="HALFREGFOOTPRINT" low="4" high="9" type="uint"/>
		<bitfield name="FULLREGFOOTPRINT" low="10" high="15" type="uint"/>
		<bitfield name="FSBYPASSENABLE" pos="17" type="boolean"/>
		<bitfield name="INOUTREGOVERLAP" pos="18" type="boolean"/>
		<bitfield name="OUTORDERED" pos="19" type="boolean"/>
		<bitfield name="THREADSIZE" pos="20" type="a3xx_threadsize"/>
		<bitfield name="SUPERTHREADMODE" pos="21" type="boolean"/>
		<bitfield name="PIXLODENABLE" pos="22" type="boolean"/>
		<bitfield name="COMPUTEMODE" pos="23" type="boolean"/>
		<doc>
			From regspec:
			SP_FS_CTRL_REG0.FS_LENGTH [31:24]: FS length, unit = 256bits.
			If bit31 is 1, it means overflow
			or any long shader.
		</doc>
		<bitfield name="LENGTH" low="24" high="31" type="uint"/>
	</reg32>
	<reg32 offset="0x22e1" name="SP_FS_CTRL_REG1">
		<bitfield name="CONSTLENGTH" low="0" high="9" type="uint"/>
		<bitfield name="CONSTFOOTPRINT" low="10" high="19" type="uint"/>
		<bitfield name="INITIALOUTSTANDING" low="20" high="23" type="uint"/>
		<bitfield name="HALFPRECVAROFFSET" low="24" high="30" type="uint"/>
	</reg32>
	<reg32 offset="0x22e2" name="SP_FS_OBJ_OFFSET_REG" type="sp_vs_fs_obj_offset_reg"/>
	<doc>SP_FS_OBJ_START_REG contains pointer to fragment shader program</doc>
	<reg32 offset="0x22e3" name="SP_FS_OBJ_START_REG"/>
	<reg32 offset="0x22e4" name="SP_FS_PVT_MEM_PARAM_REG">
		<bitfield name="MEMSIZEPERITEM" low="0" high="7" type="uint"/>
		<bitfield name="HWSTACKOFFSET" low="8" high="23" type="uint"/>
		<bitfield name="HWSTACKSIZEPERTHREAD" low="24" high="31" type="uint"/>
	</reg32>
	<reg32 offset="0x22e5" name="SP_FS_PVT_MEM_ADDR_REG">
		<bitfield name="BURSTLEN" low="0" high="4"/>
		<bitfield name="SHADERSTARTADDRESS" shr="5" low="5" high="31"/>
	</reg32>
	<reg32 offset="0x22e6" name="SP_FS_PVT_MEM_SIZE_REG"/>
	<reg32 offset="0x22e8" name="SP_FS_FLAT_SHAD_MODE_REG_0">
		<doc>seems to be one bit per scalar, '1' for flat, '0' for smooth</doc>
	</reg32>
	<reg32 offset="0x22e9" name="SP_FS_FLAT_SHAD_MODE_REG_1">
		<doc>seems to be one bit per scalar, '1' for flat, '0' for smooth</doc>
	</reg32>
	<reg32 offset="0x22ec" name="SP_FS_OUTPUT_REG">
		<bitfield name="MRT" low="0" high="1" type="uint">
			<doc>render targets - 1</doc>
		</bitfield>
		<bitfield name="DEPTH_ENABLE" pos="7" type="boolean"/>
		<bitfield name="DEPTH_REGID" low="8" high="15" type="a3xx_regid"/>
	</reg32>
	<array offset="0x22f0" name="SP_FS_MRT" stride="1" length="4">
		<reg32 offset="0x0" name="REG">
			<bitfield name="REGID" low="0" high="7" type="a3xx_regid"/>
			<bitfield name="HALF_PRECISION" pos="8" type="boolean"/>
			<bitfield name="SINT" pos="10" type="boolean"/>
			<bitfield name="UINT" pos="11" type="boolean"/>
		</reg32>
	</array>
	<array offset="0x22f4" name="SP_FS_IMAGE_OUTPUT" stride="1" length="4">
		<reg32 offset="0x0" name="REG">
			<bitfield name="MRTFORMAT" low="0" high="5" type="a3xx_color_fmt"/>
		</reg32>
	</array>
	<reg32 offset="0x22ff" name="SP_FS_LENGTH_REG" type="a3xx_vs_fs_length_reg"/>

	<reg32 offset="0x2301" name="PA_SC_AA_CONFIG"/>
	<!-- TPL1 registers -->
	<!-- assume VS/FS_TEX_OFFSET is same -->
	<bitset name="a3xx_tpl1_tp_vs_fs_tex_offset" inline="yes">
		<bitfield name="SAMPLEROFFSET" low="0" high="7" type="uint"/>
		<bitfield name="MEMOBJOFFSET" low="8" high="15" type="uint"/>
		<!-- not sure the size of this: -->
		<bitfield name="BASETABLEPTR" low="16" high="31" type="uint"/>
	</bitset>
	<reg32 offset="0x2340" name="TPL1_TP_VS_TEX_OFFSET" type="a3xx_tpl1_tp_vs_fs_tex_offset"/>
	<reg32 offset="0x2341" name="TPL1_TP_VS_BORDER_COLOR_BASE_ADDR"/>
	<reg32 offset="0x2342" name="TPL1_TP_FS_TEX_OFFSET" type="a3xx_tpl1_tp_vs_fs_tex_offset"/>
	<reg32 offset="0x2343" name="TPL1_TP_FS_BORDER_COLOR_BASE_ADDR"/>

	<!-- VBIF registers -->
	<reg32 offset="0x3001" name="VBIF_CLKON"/>
	<reg32 offset="0x300c" name="VBIF_FIXED_SORT_EN"/>
	<reg32 offset="0x300d" name="VBIF_FIXED_SORT_SEL0"/>
	<reg32 offset="0x300e" name="VBIF_FIXED_SORT_SEL1"/>
	<reg32 offset="0x301c" name="VBIF_ABIT_SORT"/>
	<reg32 offset="0x301d" name="VBIF_ABIT_SORT_CONF"/>
	<reg32 offset="0x302a" name="VBIF_GATE_OFF_WRREQ_EN"/>
	<reg32 offset="0x302c" name="VBIF_IN_RD_LIM_CONF0"/>
	<reg32 offset="0x302d" name="VBIF_IN_RD_LIM_CONF1"/>
	<reg32 offset="0x3030" name="VBIF_IN_WR_LIM_CONF0"/>
	<reg32 offset="0x3031" name="VBIF_IN_WR_LIM_CONF1"/>
	<reg32 offset="0x3034" name="VBIF_OUT_RD_LIM_CONF0"/>
	<reg32 offset="0x3035" name="VBIF_OUT_WR_LIM_CONF0"/>
	<reg32 offset="0x3036" name="VBIF_DDR_OUT_MAX_BURST"/>
	<reg32 offset="0x303c" name="VBIF_ARB_CTL"/>
	<reg32 offset="0x3049" name="VBIF_ROUND_ROBIN_QOS_ARB"/>
	<reg32 offset="0x3058" name="VBIF_OUT_AXI_AMEMTYPE_CONF0"/>
	<reg32 offset="0x305e" name="VBIF_OUT_AXI_AOOO_EN"/>
	<reg32 offset="0x305f" name="VBIF_OUT_AXI_AOOO"/>

	<bitset name="a3xx_vbif_perf_cnt" inline="yes">
		<bitfield name="CNT0" pos="0" type="boolean"/>
		<bitfield name="CNT1" pos="1" type="boolean"/>
		<bitfield name="PWRCNT0" pos="2" type="boolean"/>
		<bitfield name="PWRCNT1" pos="3" type="boolean"/>
		<bitfield name="PWRCNT2" pos="4" type="boolean"/>
	</bitset>

	<reg32 offset="0x3070" name="VBIF_PERF_CNT_EN" type="a3xx_vbif_perf_cnt"/>
	<reg32 offset="0x3071" name="VBIF_PERF_CNT_CLR" type="a3xx_vbif_perf_cnt"/>
	<reg32 offset="0x3072" name="VBIF_PERF_CNT_SEL"/>
	<reg32 offset="0x3073" name="VBIF_PERF_CNT0_LO"/>
	<reg32 offset="0x3074" name="VBIF_PERF_CNT0_HI"/>
	<reg32 offset="0x3075" name="VBIF_PERF_CNT1_LO"/>
	<reg32 offset="0x3076" name="VBIF_PERF_CNT1_HI"/>
	<reg32 offset="0x3077" name="VBIF_PERF_PWR_CNT0_LO"/>
	<reg32 offset="0x3078" name="VBIF_PERF_PWR_CNT0_HI"/>
	<reg32 offset="0x3079" name="VBIF_PERF_PWR_CNT1_LO"/>
	<reg32 offset="0x307a" name="VBIF_PERF_PWR_CNT1_HI"/>
	<reg32 offset="0x307b" name="VBIF_PERF_PWR_CNT2_LO"/>
	<reg32 offset="0x307c" name="VBIF_PERF_PWR_CNT2_HI"/>


	<reg32 offset="0x0c01" name="VSC_BIN_SIZE">
		<bitfield name="WIDTH" low="0" high="4" shr="5" type="uint"/>
		<bitfield name="HEIGHT" low="5" high="9" shr="5" type="uint"/>
	</reg32>

	<reg32 offset="0x0c02" name="VSC_SIZE_ADDRESS"/>
	<array offset="0x0c06" name="VSC_PIPE" stride="3" length="8">
		<reg32 offset="0x0" name="CONFIG">
			<doc>
				Configures the mapping between VSC_PIPE buffer and
				bin, X/Y specify the bin index in the horiz/vert
				direction (0,0 is upper left, 0,1 is leftmost bin
				on second row, and so on).  W/H specify the number
				of bins assigned to this VSC_PIPE in the horiz/vert
				dimension.
			</doc>
			<bitfield name="X" low="0" high="9" type="uint"/>
			<bitfield name="Y" low="10" high="19" type="uint"/>
			<bitfield name="W" low="20" high="23" type="uint"/>
			<bitfield name="H" low="24" high="27" type="uint"/>
		</reg32>
		<reg32 offset="0x1" name="DATA_ADDRESS"/>
		<reg32 offset="0x2" name="DATA_LENGTH"/>
	</array>
	<reg32 offset="0x0c3c" name="VSC_BIN_CONTROL">
		<doc>seems to be set to 0x00000001 during binning pass</doc>
		<bitfield name="BINNING_ENABLE" pos="0" type="boolean"/>
	</reg32>
	<reg32 offset="0x0c3d" name="UNKNOWN_0C3D">
		<doc>seems to be always set to 0x00000001</doc>
	</reg32>
	<reg32 offset="0x0c48" name="PC_PERFCOUNTER0_SELECT" type="a3xx_pc_perfcounter_select"/>
	<reg32 offset="0x0c49" name="PC_PERFCOUNTER1_SELECT" type="a3xx_pc_perfcounter_select"/>
	<reg32 offset="0x0c4a" name="PC_PERFCOUNTER2_SELECT" type="a3xx_pc_perfcounter_select"/>
	<reg32 offset="0x0c4b" name="PC_PERFCOUNTER3_SELECT" type="a3xx_pc_perfcounter_select"/>
	<reg32 offset="0x0c81" name="GRAS_TSE_DEBUG_ECO">
		<doc>seems to be always set to 0x00000001</doc>
	</reg32>

	<reg32 offset="0x0c88" name="GRAS_PERFCOUNTER0_SELECT" type="a3xx_gras_tse_perfcounter_select"/>
	<reg32 offset="0x0c89" name="GRAS_PERFCOUNTER1_SELECT" type="a3xx_gras_tse_perfcounter_select"/>
	<reg32 offset="0x0c8a" name="GRAS_PERFCOUNTER2_SELECT" type="a3xx_gras_ras_perfcounter_select"/>
	<reg32 offset="0x0c8b" name="GRAS_PERFCOUNTER3_SELECT" type="a3xx_gras_ras_perfcounter_select"/>
	<array offset="0x0ca0" name="GRAS_CL_USER_PLANE" stride="4" length="6">
		<reg32 offset="0x0" name="X"/>
		<reg32 offset="0x1" name="Y"/>
		<reg32 offset="0x2" name="Z"/>
		<reg32 offset="0x3" name="W"/>
	</array>
	<reg32 offset="0x0cc0" name="RB_GMEM_BASE_ADDR"/>
	<reg32 offset="0x0cc1" name="RB_DEBUG_ECO_CONTROLS_ADDR"/>
	<reg32 offset="0x0cc6" name="RB_PERFCOUNTER0_SELECT" type="a3xx_rb_perfcounter_select"/>
	<reg32 offset="0x0cc7" name="RB_PERFCOUNTER1_SELECT" type="a3xx_rb_perfcounter_select"/>
	<reg32 offset="0x0ce0" name="RB_FRAME_BUFFER_DIMENSION">
		<bitfield name="WIDTH" low="0" high="13" type="uint"/>
		<bitfield name="HEIGHT" low="14" high="27" type="uint"/>
	</reg32>
	<reg32 offset="0x0e00" name="HLSQ_PERFCOUNTER0_SELECT" type="a3xx_hlsq_perfcounter_select"/>
	<reg32 offset="0x0e01" name="HLSQ_PERFCOUNTER1_SELECT" type="a3xx_hlsq_perfcounter_select"/>
	<reg32 offset="0x0e02" name="HLSQ_PERFCOUNTER2_SELECT" type="a3xx_hlsq_perfcounter_select"/>
	<reg32 offset="0x0e03" name="HLSQ_PERFCOUNTER3_SELECT" type="a3xx_hlsq_perfcounter_select"/>
	<reg32 offset="0x0e04" name="HLSQ_PERFCOUNTER4_SELECT" type="a3xx_hlsq_perfcounter_select"/>
	<reg32 offset="0x0e05" name="HLSQ_PERFCOUNTER5_SELECT" type="a3xx_hlsq_perfcounter_select"/>
	<reg32 offset="0x0e43" name="UNKNOWN_0E43">
		<doc>seems to be always set to 0x00000001</doc>
	</reg32>
	<reg32 offset="0x0e44" name="VFD_PERFCOUNTER0_SELECT" type="a3xx_vfd_perfcounter_select"/>
	<reg32 offset="0x0e45" name="VFD_PERFCOUNTER1_SELECT" type="a3xx_vfd_perfcounter_select"/>
	<reg32 offset="0x0e61" name="VPC_VPC_DEBUG_RAM_SEL"/>
	<reg32 offset="0x0e62" name="VPC_VPC_DEBUG_RAM_READ"/>
	<reg32 offset="0x0e64" name="VPC_PERFCOUNTER0_SELECT" type="a3xx_vpc_perfcounter_select"/>
	<reg32 offset="0x0e65" name="VPC_PERFCOUNTER1_SELECT" type="a3xx_vpc_perfcounter_select"/>
	<reg32 offset="0x0e82" name="UCHE_CACHE_MODE_CONTROL_REG"/>
	<reg32 offset="0x0e84" name="UCHE_PERFCOUNTER0_SELECT" type="a3xx_uche_perfcounter_select"/>
	<reg32 offset="0x0e85" name="UCHE_PERFCOUNTER1_SELECT" type="a3xx_uche_perfcounter_select"/>
	<reg32 offset="0x0e86" name="UCHE_PERFCOUNTER2_SELECT" type="a3xx_uche_perfcounter_select"/>
	<reg32 offset="0x0e87" name="UCHE_PERFCOUNTER3_SELECT" type="a3xx_uche_perfcounter_select"/>
	<reg32 offset="0x0e88" name="UCHE_PERFCOUNTER4_SELECT" type="a3xx_uche_perfcounter_select"/>
	<reg32 offset="0x0e89" name="UCHE_PERFCOUNTER5_SELECT" type="a3xx_uche_perfcounter_select"/>
	<reg32 offset="0x0ea0" name="UCHE_CACHE_INVALIDATE0_REG">
		<!-- might be shifted right by 5, assuming 32byte cache line size.. -->
		<bitfield name="ADDR" low="0" high="27" type="hex"/>
	</reg32>
	<reg32 offset="0x0ea1" name="UCHE_CACHE_INVALIDATE1_REG">
		<!-- might be shifted right by 5, assuming 32byte cache line size.. -->
		<bitfield name="ADDR" low="0" high="27" type="hex"/>
		<!-- I'd assume 2 bits, for FLUSH/INVALIDATE/CLEAN? -->
		<bitfield name="OPCODE" low="28" high="29" type="a3xx_cache_opcode"/>
		<bitfield name="ENTIRE_CACHE" pos="31" type="boolean"/>
	</reg32>
	<reg32 offset="0x0ea6" name="UNKNOWN_0EA6"/>
	<reg32 offset="0x0ec4" name="SP_PERFCOUNTER0_SELECT" type="a3xx_sp_perfcounter_select"/>
	<reg32 offset="0x0ec5" name="SP_PERFCOUNTER1_SELECT" type="a3xx_sp_perfcounter_select"/>
	<reg32 offset="0x0ec6" name="SP_PERFCOUNTER2_SELECT" type="a3xx_sp_perfcounter_select"/>
	<reg32 offset="0x0ec7" name="SP_PERFCOUNTER3_SELECT" type="a3xx_sp_perfcounter_select"/>
	<reg32 offset="0x0ec8" name="SP_PERFCOUNTER4_SELECT" type="a3xx_sp_perfcounter_select"/>
	<reg32 offset="0x0ec9" name="SP_PERFCOUNTER5_SELECT" type="a3xx_sp_perfcounter_select"/>
	<reg32 offset="0x0eca" name="SP_PERFCOUNTER6_SELECT" type="a3xx_sp_perfcounter_select"/>
	<reg32 offset="0x0ecb" name="SP_PERFCOUNTER7_SELECT" type="a3xx_sp_perfcounter_select"/>
	<reg32 offset="0x0ee0" name="UNKNOWN_0EE0">
		<doc>seems to be always set to 0x00000003</doc>
	</reg32>
	<reg32 offset="0x0f03" name="UNKNOWN_0F03">
		<doc>seems to be always set to 0x00000001</doc>
	</reg32>
	<reg32 offset="0x0f04" name="TP_PERFCOUNTER0_SELECT" type="a3xx_tp_perfcounter_select"/>
	<reg32 offset="0x0f05" name="TP_PERFCOUNTER1_SELECT" type="a3xx_tp_perfcounter_select"/>
	<reg32 offset="0x0f06" name="TP_PERFCOUNTER2_SELECT" type="a3xx_tp_perfcounter_select"/>
	<reg32 offset="0x0f07" name="TP_PERFCOUNTER3_SELECT" type="a3xx_tp_perfcounter_select"/>
	<reg32 offset="0x0f08" name="TP_PERFCOUNTER4_SELECT" type="a3xx_tp_perfcounter_select"/>
	<reg32 offset="0x0f09" name="TP_PERFCOUNTER5_SELECT" type="a3xx_tp_perfcounter_select"/>

	<!-- this seems to be the register that CP_RUN_OPENCL writes: -->
	<reg32 offset="0x21f0" name="VGT_CL_INITIATOR"/>

	<!-- seems to be same as a2xx according to fwdump.. -->
	<reg32 offset="0x21f9" name="VGT_EVENT_INITIATOR"/>
	<reg32 offset="0x21fc" name="VGT_DRAW_INITIATOR" type="vgt_draw_initiator"/>
	<reg32 offset="0x21fd" name="VGT_IMMED_DATA"/>
</domain>

<domain name="A3XX_TEX_SAMP" width="32">
	<doc>Texture sampler dwords</doc>
	<enum name="a3xx_tex_filter">
		<value name="A3XX_TEX_NEAREST" value="0"/>
		<value name="A3XX_TEX_LINEAR" value="1"/>
		<value name="A3XX_TEX_ANISO" value="2"/>
	</enum>
	<enum name="a3xx_tex_clamp">
		<value name="A3XX_TEX_REPEAT" value="0"/>
		<value name="A3XX_TEX_CLAMP_TO_EDGE" value="1"/>
		<value name="A3XX_TEX_MIRROR_REPEAT" value="2"/>
		<value name="A3XX_TEX_CLAMP_TO_BORDER" value="3"/>
		<value name="A3XX_TEX_MIRROR_CLAMP" value="4"/>
	</enum>
	<enum name="a3xx_tex_aniso">
		<value name="A3XX_TEX_ANISO_1" value="0"/>
		<value name="A3XX_TEX_ANISO_2" value="1"/>
		<value name="A3XX_TEX_ANISO_4" value="2"/>
		<value name="A3XX_TEX_ANISO_8" value="3"/>
		<value name="A3XX_TEX_ANISO_16" value="4"/>
	</enum>
	<reg32 offset="0" name="0">
		<bitfield name="CLAMPENABLE" pos="0" type="boolean"/>
		<bitfield name="MIPFILTER_LINEAR" pos="1" type="boolean"/>
		<bitfield name="XY_MAG" low="2" high="3" type="a3xx_tex_filter"/>
		<bitfield name="XY_MIN" low="4" high="5" type="a3xx_tex_filter"/>
		<bitfield name="WRAP_S" low="6" high="8" type="a3xx_tex_clamp"/>
		<bitfield name="WRAP_T" low="9" high="11" type="a3xx_tex_clamp"/>
		<bitfield name="WRAP_R" low="12" high="14" type="a3xx_tex_clamp"/>
		<bitfield name="ANISO" low="15" high="17" type="a3xx_tex_aniso"/>
		<bitfield name="COMPARE_FUNC" low="20" high="22" type="adreno_compare_func"/>
		<bitfield name="CUBEMAPSEAMLESSFILTOFF" pos="24" type="boolean"/>
		<!-- UNNORM_COORDS == CLK_NORMALIZED_COORDS_FALSE -->
		<bitfield name="UNNORM_COORDS" pos="31" type="boolean"/>
	</reg32>
	<reg32 offset="1" name="1">
		<bitfield name="LOD_BIAS" low="0" high="10" type="fixed" radix="6"/>
		<bitfield name="MAX_LOD" low="12" high="21" type="ufixed" radix="6"/>
		<bitfield name="MIN_LOD" low="22" high="31" type="ufixed" radix="6"/>
	</reg32>
</domain>

<domain name="A3XX_TEX_CONST" width="32">
	<doc>Texture constant dwords</doc>
	<enum name="a3xx_tex_swiz">
		<!-- same as a2xx? -->
		<value name="A3XX_TEX_X" value="0"/>
		<value name="A3XX_TEX_Y" value="1"/>
		<value name="A3XX_TEX_Z" value="2"/>
		<value name="A3XX_TEX_W" value="3"/>
		<value name="A3XX_TEX_ZERO" value="4"/>
		<value name="A3XX_TEX_ONE" value="5"/>
	</enum>
	<enum name="a3xx_tex_type">
		<value name="A3XX_TEX_1D" value="0"/>
		<value name="A3XX_TEX_2D" value="1"/>
		<value name="A3XX_TEX_CUBE" value="2"/>
		<value name="A3XX_TEX_3D" value="3"/>
	</enum>
	<enum name="a3xx_tex_msaa">
		<value name="A3XX_TPL1_MSAA1X" value="0"/>
		<value name="A3XX_TPL1_MSAA2X" value="1"/>
		<value name="A3XX_TPL1_MSAA4X" value="2"/>
		<value name="A3XX_TPL1_MSAA8X" value="3"/>
	</enum>
	<reg32 offset="0" name="0">
		<bitfield name="TILE_MODE" low="0" high="1" type="a3xx_tile_mode"/>
		<bitfield name="SRGB" pos="2" type="boolean"/>
		<bitfield name="SWIZ_X" low="4" high="6" type="a3xx_tex_swiz"/>
		<bitfield name="SWIZ_Y" low="7" high="9" type="a3xx_tex_swiz"/>
		<bitfield name="SWIZ_Z" low="10" high="12" type="a3xx_tex_swiz"/>
		<bitfield name="SWIZ_W" low="13" high="15" type="a3xx_tex_swiz"/>
		<bitfield name="MIPLVLS" low="16" high="19" type="uint"/>
		<bitfield name="MSAATEX" low="20" high="21" type="a3xx_tex_msaa"/>
		<bitfield name="FMT" low="22" high="28" type="a3xx_tex_fmt"/>
		<bitfield name="NOCONVERT" pos="29" type="boolean"/>
		<bitfield name="TYPE" low="30" high="31" type="a3xx_tex_type"/>
	</reg32>
	<reg32 offset="1" name="1">
		<bitfield name="HEIGHT" low="0" high="13" type="uint"/>
		<bitfield name="WIDTH" low="14" high="27" type="uint"/>
		<!-- minimum pitch (for mipmap levels): log2(pitchalign / 16) -->
		<bitfield name="PITCHALIGN" low="28" high="31" type="uint"/>
	</reg32>
	<reg32 offset="2" name="2">
		<doc>INDX is index of texture address(es) in MIPMAP state block</doc>
		<bitfield name="INDX" low="0" high="8" type="uint"/>
		<doc>Pitch in bytes (so actually stride)</doc>
		<bitfield name="PITCH" low="12" high="29" type="uint"/>
		<doc>SWAP bit is set for BGRA instead of RGBA</doc>
		<bitfield name="SWAP" low="30" high="31" type="a3xx_color_swap"/>
	</reg32>
	<reg32 offset="3" name="3">
		<!--
		Update: the two LAYERSZn seem not to be the same thing.
		According to Ilia's experimentation the first one goes up
		to at *least* bit 14..
		 -->
		<bitfield name="LAYERSZ1" low="0" high="16" shr="12" type="uint"/>
		<bitfield name="DEPTH" low="17" high="27" type="uint"/>
		<bitfield name="LAYERSZ2" low="28" high="31" shr="12" type="uint"/>
	</reg32>
</domain>

</database>
