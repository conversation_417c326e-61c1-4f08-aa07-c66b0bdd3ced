# SPDX-License-Identifier: GPL-2.0

dtb-$(CONFIG_ARCH_LAYERSCAPE) += fsl-ls1012a-2g5rdb.dtb
dtb-$(CONFIG_ARCH_LAYERSCAPE) += fsl-ls1012a-frdm.dtb
dtb-$(CONFIG_ARCH_LAYERSCAPE) += fsl-ls1012a-frwy.dtb
dtb-$(CONFIG_ARCH_LAYERSCAPE) += fsl-ls1012a-oxalis.dtb
dtb-$(CONFIG_ARCH_LAYERSCAPE) += fsl-ls1012a-qds.dtb
dtb-$(CONFIG_ARCH_LAYERSCAPE) += fsl-ls1012a-rdb.dtb
dtb-$(CONFIG_ARCH_LAYERSCAPE) += fsl-ls1028a-kontron-kbox-a-230-ls.dtb
dtb-$(CONFIG_ARCH_LAYERSCAPE) += fsl-ls1028a-kontron-sl28.dtb
dtb-$(CONFIG_ARCH_LAYERSCAPE) += fsl-ls1028a-kontron-sl28-var1.dtb
dtb-$(CONFIG_ARCH_LAYERSCAPE) += fsl-ls1028a-kontron-sl28-var2.dtb
dtb-$(CONFIG_ARCH_LAYERSCAPE) += fsl-ls1028a-kontron-sl28-var3.dtb
dtb-$(CONFIG_ARCH_LAYERSCAPE) += fsl-ls1028a-kontron-sl28-var3-ads2.dtb
dtb-$(CONFIG_ARCH_LAYERSCAPE) += fsl-ls1028a-kontron-sl28-var4.dtb
dtb-$(CONFIG_ARCH_LAYERSCAPE) += fsl-ls1028a-qds.dtb
dtb-$(CONFIG_ARCH_LAYERSCAPE) += fsl-ls1028a-rdb.dtb
dtb-$(CONFIG_ARCH_LAYERSCAPE) += fsl-ls1028a-rdb-dpdk.dtb
dtb-$(CONFIG_ARCH_LAYERSCAPE) += fsl-ls1043a-qds.dtb
dtb-$(CONFIG_ARCH_LAYERSCAPE) += fsl-ls1043a-qds-sdk.dtb
dtb-$(CONFIG_ARCH_LAYERSCAPE) += fsl-ls1043a-rdb.dtb
dtb-$(CONFIG_ARCH_LAYERSCAPE) += fsl-ls1043a-rdb-sdk.dtb
dtb-$(CONFIG_ARCH_LAYERSCAPE) += fsl-ls1043a-rdb-usdpaa.dtb
dtb-$(CONFIG_ARCH_LAYERSCAPE) += fsl-ls1043a-tqmls1043a-mbls10xxa.dtb
dtb-$(CONFIG_ARCH_LAYERSCAPE) += fsl-ls1046a-frwy.dtb
dtb-$(CONFIG_ARCH_LAYERSCAPE) += fsl-ls1046a-frwy-sdk.dtb
dtb-$(CONFIG_ARCH_LAYERSCAPE) += fsl-ls1046a-frwy-usdpaa.dtb
dtb-$(CONFIG_ARCH_LAYERSCAPE) += fsl-ls1046a-qds.dtb
dtb-$(CONFIG_ARCH_LAYERSCAPE) += fsl-ls1046a-qds-sdk.dtb
dtb-$(CONFIG_ARCH_LAYERSCAPE) += fsl-ls1046a-rdb.dtb
dtb-$(CONFIG_ARCH_LAYERSCAPE) += fsl-ls1046a-rdb-sdk.dtb
dtb-$(CONFIG_ARCH_LAYERSCAPE) += fsl-ls1046a-rdb-usdpaa.dtb
dtb-$(CONFIG_ARCH_LAYERSCAPE) += fsl-ls1046a-rdb-shared-mac9-only.dtb
dtb-$(CONFIG_ARCH_LAYERSCAPE) += fsl-ls1046a-rdb-usdpaa-shared-mac10.dtb
dtb-$(CONFIG_ARCH_LAYERSCAPE) += fsl-ls1046a-rdb-usdpaa-shared.dtb
dtb-$(CONFIG_ARCH_LAYERSCAPE) += fsl-ls1046a-tqmls1046a-mbls10xxa.dtb
DTC_FLAGS_fsl-ls1088a-qds := -Wno-interrupt_map
dtb-$(CONFIG_ARCH_LAYERSCAPE) += fsl-ls1088a-qds.dtb
DTC_FLAGS_fsl-ls1088a-rdb := -Wno-interrupt_map
dtb-$(CONFIG_ARCH_LAYERSCAPE) += fsl-ls1088a-rdb.dtb
DTC_FLAGS_fsl-ls1088a-ten64 := -Wno-interrupt_map
dtb-$(CONFIG_ARCH_LAYERSCAPE) += fsl-ls1088a-ten64.dtb
DTC_FLAGS_fsl-ls1088a-tqmls1088a-mbls10xxa := -Wno-interrupt_map
dtb-$(CONFIG_ARCH_LAYERSCAPE) += fsl-ls1088a-tqmls1088a-mbls10xxa.dtb
DTC_FLAGS_fsl-ls2080a-qds := -Wno-interrupt_map
dtb-$(CONFIG_ARCH_LAYERSCAPE) += fsl-ls2080a-qds.dtb
DTC_FLAGS_fsl-ls2080a-rdb := -Wno-interrupt_map
dtb-$(CONFIG_ARCH_LAYERSCAPE) += fsl-ls2080a-rdb.dtb
DTC_FLAGS_fsl-ls2081a-rdb := -Wno-interrupt_map
dtb-$(CONFIG_ARCH_LAYERSCAPE) += fsl-ls2081a-rdb.dtb
DTC_FLAGS_fsl-ls2080a-simu := -Wno-interrupt_map
dtb-$(CONFIG_ARCH_LAYERSCAPE) += fsl-ls2080a-simu.dtb
DTC_FLAGS_fsl-ls2088a-qds := -Wno-interrupt_map
dtb-$(CONFIG_ARCH_LAYERSCAPE) += fsl-ls2088a-qds.dtb
DTC_FLAGS_fsl-ls2088a-rdb := -Wno-interrupt_map
dtb-$(CONFIG_ARCH_LAYERSCAPE) += fsl-ls2088a-rdb.dtb
DTC_FLAGS_fsl-lx2160a-bluebox3 := -Wno-interrupt_map
dtb-$(CONFIG_ARCH_LAYERSCAPE) += fsl-lx2160a-bluebox3.dtb
DTC_FLAGS_fsl-lx2160a-bluebox3-rev-a := -Wno-interrupt_map
dtb-$(CONFIG_ARCH_LAYERSCAPE) += fsl-lx2160a-bluebox3-rev-a.dtb
DTC_FLAGS_fsl-lx2160a-clearfog-cx := -Wno-interrupt_map
dtb-$(CONFIG_ARCH_LAYERSCAPE) += fsl-lx2160a-clearfog-cx.dtb
DTC_FLAGS_fsl-lx2160a-honeycomb := -Wno-interrupt_map
dtb-$(CONFIG_ARCH_LAYERSCAPE) += fsl-lx2160a-honeycomb.dtb
DTC_FLAGS_fsl-lx2160a-qds := -Wno-interrupt_map
dtb-$(CONFIG_ARCH_LAYERSCAPE) += fsl-lx2160a-qds.dtb
DTC_FLAGS_fsl-lx2160a-rdb := -Wno-interrupt_map
dtb-$(CONFIG_ARCH_LAYERSCAPE) += fsl-lx2160a-rdb.dtb
DTC_FLAGS_fsl-lx2162a-clearfog := -Wno-interrupt_map
dtb-$(CONFIG_ARCH_LAYERSCAPE) += fsl-lx2162a-clearfog.dtb
DTC_FLAGS_fsl-lx2162a-qds := -Wno-interrupt_map
dtb-$(CONFIG_ARCH_LAYERSCAPE) += fsl-lx2162a-qds.dtb

fsl-ls1028a-qds-13bb-dtbs := fsl-ls1028a-qds.dtb fsl-ls1028a-qds-13bb.dtbo
fsl-ls1028a-qds-65bb-dtbs := fsl-ls1028a-qds.dtb fsl-ls1028a-qds-65bb.dtbo
fsl-ls1028a-qds-7777-dtbs := fsl-ls1028a-qds.dtb fsl-ls1028a-qds-7777.dtbo
fsl-ls1028a-qds-85bb-dtbs := fsl-ls1028a-qds.dtb fsl-ls1028a-qds-85bb.dtbo
fsl-ls1028a-qds-899b-dtbs := fsl-ls1028a-qds.dtb fsl-ls1028a-qds-899b.dtbo
fsl-ls1028a-qds-9999-dtbs := fsl-ls1028a-qds.dtb fsl-ls1028a-qds-9999.dtbo
fsl-ls1028a-qds-backplane-dtbs := \
	fsl-ls1028a-qds.dtb \
	fsl-ls1028a-lane-a-backplane.dtbo \
	fsl-ls1028a-lane-b-backplane.dtbo \
	fsl-ls1028a-lane-c-backplane.dtbo \
	fsl-ls1028a-lane-d-backplane.dtbo

dtb-$(CONFIG_ARCH_LAYERSCAPE) += fsl-ls1028a-qds-13bb.dtb
dtb-$(CONFIG_ARCH_LAYERSCAPE) += fsl-ls1028a-qds-65bb.dtb
dtb-$(CONFIG_ARCH_LAYERSCAPE) += fsl-ls1028a-qds-7777.dtb
dtb-$(CONFIG_ARCH_LAYERSCAPE) += fsl-ls1028a-qds-85bb.dtb
dtb-$(CONFIG_ARCH_LAYERSCAPE) += fsl-ls1028a-qds-899b.dtb
dtb-$(CONFIG_ARCH_LAYERSCAPE) += fsl-ls1028a-qds-9999.dtb
dtb-$(CONFIG_ARCH_LAYERSCAPE) += fsl-ls1028a-qds-backplane.dtb

DTC_FLAGS_fsl-lx2160a-tqmlx2160a-mblx2160a := -Wno-interrupt_map
fsl-lx2160a-tqmlx2160a-mblx2160a-12-11-x-dtbs := fsl-lx2160a-tqmlx2160a-mblx2160a.dtb \
	fsl-lx2160a-tqmlx2160a-mblx2160a_12_x_x.dtbo \
	fsl-lx2160a-tqmlx2160a-mblx2160a_x_11_x.dtbo
fsl-lx2160a-tqmlx2160a-mblx2160a-12-7-x-dtbs := fsl-lx2160a-tqmlx2160a-mblx2160a.dtb \
	fsl-lx2160a-tqmlx2160a-mblx2160a_12_x_x.dtbo \
	fsl-lx2160a-tqmlx2160a-mblx2160a_x_7_x.dtbo
fsl-lx2160a-tqmlx2160a-mblx2160a-12-8-x-dtbs := fsl-lx2160a-tqmlx2160a-mblx2160a.dtb \
	fsl-lx2160a-tqmlx2160a-mblx2160a_12_x_x.dtbo \
	fsl-lx2160a-tqmlx2160a-mblx2160a_x_8_x.dtbo
fsl-lx2160a-tqmlx2160a-mblx2160a-14-7-x-dtbs := fsl-lx2160a-tqmlx2160a-mblx2160a.dtb \
	fsl-lx2160a-tqmlx2160a-mblx2160a_14_x_x.dtbo \
	fsl-lx2160a-tqmlx2160a-mblx2160a_x_7_x.dtbo
fsl-lx2160a-tqmlx2160a-mblx2160a-14-8-x-dtbs := fsl-lx2160a-tqmlx2160a-mblx2160a.dtb \
	fsl-lx2160a-tqmlx2160a-mblx2160a_14_x_x.dtbo \
	fsl-lx2160a-tqmlx2160a-mblx2160a_x_8_x.dtbo
fsl-lx2160a-tqmlx2160a-mblx2160a-14-11-x-dtbs := fsl-lx2160a-tqmlx2160a-mblx2160a.dtb \
	fsl-lx2160a-tqmlx2160a-mblx2160a_14_x_x.dtbo \
	fsl-lx2160a-tqmlx2160a-mblx2160a_x_11_x.dtbo

dtb-$(CONFIG_ARCH_LAYERSCAPE) += fsl-lx2160a-tqmlx2160a-mblx2160a-12-11-x.dtb
dtb-$(CONFIG_ARCH_LAYERSCAPE) += fsl-lx2160a-tqmlx2160a-mblx2160a-12-8-x.dtb
dtb-$(CONFIG_ARCH_LAYERSCAPE) += fsl-lx2160a-tqmlx2160a-mblx2160a-12-7-x.dtb
dtb-$(CONFIG_ARCH_LAYERSCAPE) += fsl-lx2160a-tqmlx2160a-mblx2160a-14-11-x.dtb
dtb-$(CONFIG_ARCH_LAYERSCAPE) += fsl-lx2160a-tqmlx2160a-mblx2160a-14-8-x.dtb
dtb-$(CONFIG_ARCH_LAYERSCAPE) += fsl-lx2160a-tqmlx2160a-mblx2160a-14-7-x.dtb

dtb-$(CONFIG_ARCH_MXC) += imx8dxl-evk.dtb
dtb-$(CONFIG_ARCH_MXC) += imx8mm-ab2.dtb imx8mm-ab2-m4.dtb imx8mm-ddr4-ab2.dtb imx8mm-ddr4-ab2-m4.dtb \
			  imx8mm-ddr4-ab2-revb.dtb imx8mm-ddr4-ab2-m4-revb.dtb
dtb-$(CONFIG_ARCH_MXC) += imx8mm-evk.dtb imx8mm-evk-rpmsg.dtb imx8mm-evk-rm67191.dtb \
			  imx8mm-evk-root.dtb imx8mm-evk-inmate.dtb imx8mm-evk-revb-qca-wifi.dtb \
			  imx8mm-evk-ecspi-slave.dtb \
			  imx8mm-evk-pcie-ep.dtb \
			  imx8mm-evk-usd-wifi.dtb \
			  imx8mm-evk-qca-wifi.dtb \
			  imx8mm-evk-dpdk.dtb \
			  imx8mm-evk-rm67199.dtb imx8mm-evk-rm67191-cmd-ram.dtb imx8mm-evk-rm67199-cmd-ram.dtb \
			  imx8mm-evk-lk.dtb imx8mm-evk-rpmsg-wm8524-lpv.dtb
dtb-$(CONFIG_ARCH_MXC) += imx8mm-evk-rpmsg-wm8524.dtb
dtb-$(CONFIG_ARCH_MXC) += imx8mm-evk-ak4497.dtb imx8mm-evk-ak5558.dtb imx8mm-evk-audio-tdm.dtb
dtb-$(CONFIG_ARCH_MXC) += imx8mm-evk-8mic-revE.dtb imx8mm-evk-8mic-swpdm.dtb \
			  imx8mm-evk-iqaudio-dacplus.dtb imx8mm-evk-iqaudio-dacpro.dtb imx8mm-evk-hifiberry-dacplus.dtb \
			  imx8mm-evk-hifiberry-dac2.dtb imx8mm-evk-hifiberry-dacplusadc.dtb
dtb-$(CONFIG_ARCH_MXC) += imx8mm-ddr4-evk.dtb imx8mm-ddr4-evk-rm67191.dtb imx8mm-ddr4-evk-revb.dtb \
			  imx8mm-ddr4-evk-revb-rm67191.dtb \
			  imx8mm-ddr4-evk-revb-rm67191-cmd-ram.dtb \
			  imx8mm-ddr4-evk-root.dtb \
			  imx8mm-ddr4-evk-inmate.dtb \
			  imx8mm-ddr4-evk-pcie-ep.dtb \
			  imx8mm-ddr4-evk-revb-rm67199.dtb \
			  imx8mm-ddr4-evk-revb-rm67199-cmd-ram.dtb \
			  imx8mm-ddr4-evk-rm67199.dtb \
			  imx8mm-ddr4-evk-rm67191-cmd-ram.dtb \
			  imx8mm-ddr4-evk-rm67199-cmd-ram.dtb
dtb-$(CONFIG_ARCH_MXC) += imx8mm-emcon-avari.dtb
dtb-$(CONFIG_ARCH_MXC) += imx8mm-emtop-baseboard.dtb
dtb-$(CONFIG_ARCH_MXC) += imx8mm-icore-mx8mm-ctouch2.dtb
dtb-$(CONFIG_ARCH_MXC) += imx8mm-icore-mx8mm-edimm2.2.dtb
dtb-$(CONFIG_ARCH_MXC) += imx8mm-nitrogen-r2.dtb

dtb-$(CONFIG_ARCH_MXC) += imx8mn-dimonoff-gateway-evk.dtb
dtb-$(CONFIG_ARCH_MXC) += imx8mn-evk.dtb imx8mn-evk-rm67191.dtb imx8mn-evk-rm67199.dtb \
			  imx8mn-evk-rm67191-cmd-ram.dtb imx8mn-evk-rm67199-cmd-ram.dtb \
			  imx8mn-evk-rpmsg.dtb imx8mn-evk-8mic-revE.dtb \
			  imx8mn-evk-8mic-swpdm.dtb imx8mn-evk-usd-wifi.dtb
dtb-$(CONFIG_ARCH_MXC) += imx8mn-ddr3l-evk.dtb
dtb-$(CONFIG_ARCH_MXC) += imx8mn-ddr4-evk.dtb imx8mn-ddr4-evk-rm67191.dtb imx8mn-ddr4-evk-rm67199.dtb \
			  imx8mn-ddr4-evk-rm67191-cmd-ram.dtb imx8mn-ddr4-evk-rm67199-cmd-ram.dtb \
			  imx8mn-ddr4-evk-usd-wifi.dtb
dtb-$(CONFIG_ARCH_MXC) += imx8mn-ddr4-evk-rpmsg.dtb imx8mn-ddr3l-evk-rpmsg.dtb
dtb-$(CONFIG_ARCH_MXC) += imx8mn-ddr4-evk-root.dtb imx8mn-ddr4-evk-inmate.dtb imx8mn-evk-root.dtb imx8mn-evk-inmate.dtb \
			  imx8mn-evk-lk.dtb imx8mn-ddr4-evk-lk.dtb
dtb-$(CONFIG_ARCH_MXC) += imx8mn-ddr3l-evk-ak5558.dtb imx8mn-ddr4-evk-ak5558.dtb imx8mn-evk-ak5558.dtb
dtb-$(CONFIG_ARCH_MXC) += imx8mn-ab2.dtb imx8mn-ddr3l-ab2.dtb imx8mn-ddr4-ab2.dtb
dtb-$(CONFIG_ARCH_MXC) += imx8mn-evk-iqaudio-dacplus.dtb imx8mn-evk-iqaudio-dacpro.dtb imx8mn-evk-hifiberry-dacplus.dtb \
			  imx8mn-evk-hifiberry-dac2.dtb
dtb-$(CONFIG_ARCH_MXC) += imx8mp-evk.dtb imx8mp-evk-rm67191.dtb imx8mp-evk-it6263-lvds-dual-channel.dtb \
			  imx8mp-evk-pcie-ep.dtb  imx8mp-evk-rpmsg.dtb imx8mp-evk-ecspi-slave.dtb \
			  imx8mp-evk-jdi-wuxga-lvds-panel.dtb imx8mp-evk-flexcan2.dtb \
			  imx8mp-evk-root.dtb imx8mp-evk-inmate.dtb imx8mp-evk-ov2775.dtb \
			  imx8mp-evk-ov2775-ov5640.dtb imx8mp-evk-basler-ov5640.dtb imx8mp-evk-basler.dtb \
			  imx8mp-evk-basler-ov2775.dtb imx8mp-evk-dual-basler.dtb \
			  imx8mp-evk-dual-ov2775.dtb imx8mp-evk-spdif-lb.dtb imx8mp-evk-dsp.dtb \
			  imx8mp-evk-sof-wm8960.dtb imx8mp-evk-sof-pdm.dtb \
			  imx8mp-evk-os08a20-ov5640.dtb imx8mp-evk-os08a20.dtb \
			  imx8mp-evk-dual-os08a20.dtb \
			  imx8mp-evk-iqaudio-dacplus.dtb imx8mp-evk-iqaudio-dacpro.dtb imx8mp-evk-hifiberry-dacplus.dtb \
			  imx8mp-evk-hifiberry-dac2.dtb imx8mp-evk-hifiberry-dacplusadc.dtb \
			  imx8mp-evk-usdhc1-m2.dtb imx8mp-evk-rm67199.dtb \
			  imx8mp-evk-dpdk.dtb imx8mp-evk-8mic-swpdm.dtb imx8mp-evk-rpmsg-lpv.dtb imx8mp-evk-revA3-8mic-revE.dtb
dtb-$(CONFIG_ARCH_MXC) += imx8mp-navqp.dtb
dtb-$(CONFIG_ARCH_MXC) += imx8mp-ab2.dtb
dtb-$(CONFIG_ARCH_MXC) += imx8mp-navq.dtb imx8mp-navq-ov5640-ov5645.dtb imx8mp-navq-ov5647-ov5640.dtb
dtb-$(CONFIG_ARCH_MXC) += imx8mp-ddr4-evk.dtb
dtb-$(CONFIG_ARCH_MXC) += imx8mp-evk-ndm.dtb

imx8mp-evk-revb4-dtbs := imx8mp-evk.dtb imx8mp-evk-revb4.dtbo
imx8mp-evk-revb4-rm67191-dtbs := imx8mp-evk-rm67191.dtb imx8mp-evk-revb4.dtbo
imx8mp-evk-revb4-it6263-lvds-dual-channel-dtbs := imx8mp-evk-it6263-lvds-dual-channel.dtb imx8mp-evk-revb4.dtbo
imx8mp-evk-revb4-pcie-ep-dtbs := imx8mp-evk-pcie-ep.dtb imx8mp-evk-revb4.dtbo
imx8mp-evk-revb4-ecspi-slave-dtbs := imx8mp-evk-ecspi-slave.dtb imx8mp-evk-revb4.dtbo
imx8mp-evk-revb4-jdi-wuxga-lvds-panel-dtbs := imx8mp-evk-jdi-wuxga-lvds-panel.dtb imx8mp-evk-revb4.dtbo
imx8mp-evk-revb4-flexcan2-dtbs := imx8mp-evk-flexcan2.dtb imx8mp-evk-revb4.dtbo
imx8mp-evk-revb4-root-dtbs := imx8mp-evk-root.dtb imx8mp-evk-revb4.dtbo
imx8mp-evk-revb4-ov2775-dtbs := imx8mp-evk-ov2775.dtb imx8mp-evk-revb4.dtbo
imx8mp-evk-revb4-ov2775-ov5640-dtbs := imx8mp-evk-ov2775-ov5640.dtb imx8mp-evk-revb4.dtbo
imx8mp-evk-revb4-basler-ov5640-dtbs := imx8mp-evk-basler-ov5640.dtb imx8mp-evk-revb4.dtbo
imx8mp-evk-revb4-basler-dtbs := imx8mp-evk-basler.dtb imx8mp-evk-revb4.dtbo
imx8mp-evk-revb4-basler-ov2775-dtbs := imx8mp-evk-basler-ov2775.dtb imx8mp-evk-revb4.dtbo
imx8mp-evk-revb4-dual-basler-dtbs := imx8mp-evk-dual-basler.dtb imx8mp-evk-revb4.dtbo
imx8mp-evk-revb4-dual-ov2775-dtbs := imx8mp-evk-dual-ov2775.dtb imx8mp-evk-revb4.dtbo
imx8mp-evk-revb4-spdif-lb-dtbs := imx8mp-evk-spdif-lb.dtb imx8mp-evk-revb4.dtbo
imx8mp-evk-revb4-os08a20-ov5640-dtbs := imx8mp-evk-os08a20-ov5640.dtb imx8mp-evk-revb4.dtbo
imx8mp-evk-revb4-os08a20-dtbs := imx8mp-evk-os08a20.dtb imx8mp-evk-revb4.dtbo
imx8mp-evk-revb4-dual-os08a20-dtbs := imx8mp-evk-dual-os08a20.dtb imx8mp-evk-revb4.dtbo
imx8mp-evk-revb4-iqaudio-dacplus-dtbs := imx8mp-evk-iqaudio-dacplus.dtb imx8mp-evk-revb4.dtbo
imx8mp-evk-revb4-iqaudio-dacpro-dtbs := imx8mp-evk-iqaudio-dacpro.dtb imx8mp-evk-revb4.dtbo
imx8mp-evk-revb4-hifiberry-dacplus-dtbs := imx8mp-evk-hifiberry-dacplus.dtb imx8mp-evk-revb4.dtbo
imx8mp-evk-revb4-hifiberry-dac2-dtbs := imx8mp-evk-hifiberry-dac2.dtb imx8mp-evk-revb4.dtbo
imx8mp-evk-revb4-hifiberry-dacplusadc-dtbs := imx8mp-evk-hifiberry-dacplusadc.dtb imx8mp-evk-revb4.dtbo
imx8mp-evk-revb4-usdhc1-m2-dtbs := imx8mp-evk-usdhc1-m2.dtb imx8mp-evk-revb4.dtbo
imx8mp-evk-revb4-rm67199-dtbs := imx8mp-evk-rm67199.dtb imx8mp-evk-revb4.dtbo
imx8mp-evk-revb4-dpdk-dtbs := imx8mp-evk-dpdk.dtb imx8mp-evk-revb4.dtbo
imx8mp-evk-revb4-8mic-swpdm-dtbs := imx8mp-evk-8mic-swpdm.dtb imx8mp-evk-revb4.dtbo
imx8mp-evk-revb4-8mic-revE-dtbs := imx8mp-evk-revA3-8mic-revE.dtb imx8mp-evk-revb4.dtbo
imx8mp-ddr4-evk-revb4-dtbs := imx8mp-ddr4-evk.dtb imx8mp-evk-revb4.dtbo
imx8mp-evk-revb4-ndm-dtbs := imx8mp-evk-ndm.dtb imx8mp-evk-revb4.dtbo

dtb-$(CONFIG_ARCH_MXC) += imx8mp-evk-revb4.dtb
dtb-$(CONFIG_ARCH_MXC) += imx8mp-evk-revb4-rm67191.dtb
dtb-$(CONFIG_ARCH_MXC) += imx8mp-evk-revb4-it6263-lvds-dual-channel.dtb
dtb-$(CONFIG_ARCH_MXC) += imx8mp-evk-revb4-pcie-ep.dtb
dtb-$(CONFIG_ARCH_MXC) += imx8mp-evk-revb4-ecspi-slave.dtb
dtb-$(CONFIG_ARCH_MXC) += imx8mp-evk-revb4-jdi-wuxga-lvds-panel.dtb
dtb-$(CONFIG_ARCH_MXC) += imx8mp-evk-revb4-flexcan2.dtb
dtb-$(CONFIG_ARCH_MXC) += imx8mp-evk-revb4-root.dtb
dtb-$(CONFIG_ARCH_MXC) += imx8mp-evk-revb4-ov2775.dtb
dtb-$(CONFIG_ARCH_MXC) += imx8mp-evk-revb4-ov2775-ov5640.dtb
dtb-$(CONFIG_ARCH_MXC) += imx8mp-evk-revb4-basler-ov5640.dtb
dtb-$(CONFIG_ARCH_MXC) += imx8mp-evk-revb4-basler.dtb
dtb-$(CONFIG_ARCH_MXC) += imx8mp-evk-revb4-basler-ov2775.dtb
dtb-$(CONFIG_ARCH_MXC) += imx8mp-evk-revb4-dual-basler.dtb
dtb-$(CONFIG_ARCH_MXC) += imx8mp-evk-revb4-dual-ov2775.dtb
dtb-$(CONFIG_ARCH_MXC) += imx8mp-evk-revb4-spdif-lb.dtb
dtb-$(CONFIG_ARCH_MXC) += imx8mp-evk-revb4-os08a20-ov5640.dtb
dtb-$(CONFIG_ARCH_MXC) += imx8mp-evk-revb4-os08a20.dtb
dtb-$(CONFIG_ARCH_MXC) += imx8mp-evk-revb4-dual-os08a20.dtb
dtb-$(CONFIG_ARCH_MXC) += imx8mp-evk-revb4-iqaudio-dacplus.dtb
dtb-$(CONFIG_ARCH_MXC) += imx8mp-evk-revb4-iqaudio-dacpro.dtb
dtb-$(CONFIG_ARCH_MXC) += imx8mp-evk-revb4-hifiberry-dacplus.dtb
dtb-$(CONFIG_ARCH_MXC) += imx8mp-evk-revb4-hifiberry-dac2.dtb
dtb-$(CONFIG_ARCH_MXC) += imx8mp-evk-revb4-hifiberry-dacplusadc.dtb
dtb-$(CONFIG_ARCH_MXC) += imx8mp-evk-revb4-usdhc1-m2.dtb
dtb-$(CONFIG_ARCH_MXC) += imx8mp-evk-revb4-rm67199.dtb
dtb-$(CONFIG_ARCH_MXC) += imx8mp-evk-revb4-dpdk.dtb
dtb-$(CONFIG_ARCH_MXC) += imx8mp-evk-revb4-8mic-swpdm.dtb
dtb-$(CONFIG_ARCH_MXC) += imx8mp-evk-revb4-8mic-revE.dtb
dtb-$(CONFIG_ARCH_MXC) += imx8mp-ddr4-evk-revb4.dtb
dtb-$(CONFIG_ARCH_MXC) += imx8mp-evk-revb4-ndm.dtb
dtb-$(CONFIG_ARCH_MXC) += imx8mp-evk-revb4-sof-wm8962.dtb
dtb-$(CONFIG_ARCH_MXC) += imx8mp-evk-revb4-rpmsg.dtb
dtb-$(CONFIG_ARCH_MXC) += imx8mp-evk-revb4-rpmsg-lpv.dtb

dtb-$(CONFIG_ARCH_MXC) += imx8mq-evk.dtb imx8mq-evk-rpmsg.dtb
dtb-$(CONFIG_ARCH_MXC) += imx8mq-evk.dtb imx8mq-evk-rpmsg.dtb imx8mq-evk-pcie1-m2.dtb imx8mq-evk-usd-wifi.dtb \
			  imx8mq-evk-usdhc2-m2.dtb
dtb-$(CONFIG_ARCH_MXC) += imx8mq-evk-ak4497.dtb imx8mq-evk-audio-tdm.dtb imx8mq-evk-pdm.dtb
dtb-$(CONFIG_ARCH_MXC) += imx8mq-evk-root.dtb imx8mq-evk-inmate.dtb
dtb-$(CONFIG_ARCH_MXC) += imx8mq-evk-lcdif-rm67191.dtb imx8mq-evk-lcdif-adv7535.dtb
dtb-$(CONFIG_ARCH_MXC) += imx8mq-evk-lcdif-rm67199.dtb
dtb-$(CONFIG_ARCH_MXC) += imx8mq-evk-dcss-rm67191.dtb imx8mq-evk-dcss-adv7535.dtb
dtb-$(CONFIG_ARCH_MXC) += imx8mq-evk-dcss-rm67199.dtb
dtb-$(CONFIG_ARCH_MXC) += imx8mq-evk-dual-display.dtb
dtb-$(CONFIG_ARCH_MXC) += imx8mq-hummingboard-pulse.dtb
dtb-$(CONFIG_ARCH_MXC) += imx8mq-kontron-pitx-imx8m.dtb
dtb-$(CONFIG_ARCH_MXC) += imx8mq-mnt-reform2.dtb
dtb-$(CONFIG_ARCH_MXC) += imx8mq-nitrogen.dtb
dtb-$(CONFIG_ARCH_MXC) += imx8mq-phanbell.dtb
dtb-$(CONFIG_ARCH_MXC) += imx8mq-pico-pi.dtb
dtb-$(CONFIG_ARCH_MXC) += imx8mq-thor96.dtb
dtb-$(CONFIG_ARCH_MXC) += imx8mq-evk-dp.dtb
dtb-$(CONFIG_ARCH_MXC) += imx8mq-zii-ultra-rmb3.dtb
dtb-$(CONFIG_ARCH_MXC) += imx8mq-zii-ultra-zest.dtb
dtb-$(CONFIG_ARCH_MXC) += imx8mq-ddr3l-val.dtb imx8mq-ddr4-val.dtb imx8mq-ddr4-val-gpmi-nand.dtb
dtb-$(CONFIG_ARCH_MXC) += imx8mq-evk-pcie-ep.dtb
dtb-$(CONFIG_ARCH_MXC) += imx8mq-wevk.dtb

#dtb-$(CONFIG_ARCH_MXC) += imx8qm-apalis-eval.dtb
#dtb-$(CONFIG_ARCH_MXC) += imx8qm-apalis-ixora-v1.1.dtb
#dtb-$(CONFIG_ARCH_MXC) += imx8qm-apalis-v1.1-eval.dtb
#dtb-$(CONFIG_ARCH_MXC) += imx8qm-apalis-v1.1-ixora-v1.1.dtb
#dtb-$(CONFIG_ARCH_MXC) += imx8qm-apalis-v1.1-ixora-v1.2.dtb
dtb-$(CONFIG_ARCH_MXC) += imx8qm-mek.dtb \
			  imx8qm-mek-enet2-tja1100.dtb imx8qm-mek-rpmsg.dtb \
			  imx8qm-mek-hdmi.dtb \
			  imx8qm-mek-jdi-wuxga-lvds1-panel.dtb \
			  imx8qm-mek-jdi-wuxga-lvds1-panel-rpmsg.dtb \
			  imx8qm-mek-pcie-ep.dtb \
			  imx8qm-mek-usdhc3-m2.dtb imx8qm-mek-usd-wifi.dtb \
			  imx8qm-lpddr4-val.dtb imx8qm-lpddr4-val-mqs.dtb \
			  imx8qm-lpddr4-val-spdif.dtb imx8qm-mek-ca53.dtb \
			  imx8qm-mek-ca72.dtb imx8qm-lpddr4-val-ca53.dtb \
			  imx8qm-lpddr4-val-ca72.dtb imx8qm-ddr4-val.dtb \
			  imx8qm-lpddr4-val-lpspi.dtb imx8qm-lpddr4-val-lpspi-slave.dtb \
			  imx8qm-mek-dsi-rm67191.dtb imx8qm-mek-dsi-rm67199.dtb \
			  imx8qm-lpddr4-val-dp.dtb\
			  imx8qp-lpddr4-val.dtb imx8dm-lpddr4-val.dtb \
			  imx8qm-mek-cockpit-a53.dtb imx8qm-mek-cockpit-a72.dtb \
			  imx8qm-mek-dsi-serdes-rpmsg.dtb \
			  imx8qm-mek-dsi-serdes.dtb imx8qm-mek-dsi-serdes-dual-display-rpmsg.dtb \
			  imx8qm-mek-dsi-serdes-dual-display.dtb
dtb-$(CONFIG_ARCH_MXC) += imx8qm-mek-dom0.dtb imx8qm-mek-domu.dtb \
			  imx8qm-mek-root.dtb imx8qm-mek-inmate.dtb

imx8qm-mek-ov5640-csi0-dtbs := imx8qm-mek.dtb imx8qm-mek-ov5640-csi0.dtbo
dtb-${CONFIG_ARCH_MXC} += imx8qm-mek-ov5640-csi0.dtb
imx8qm-mek-ov5640-csi1-dtbs := imx8qm-mek.dtb imx8qm-mek-ov5640-csi1.dtbo
dtb-${CONFIG_ARCH_MXC} += imx8qm-mek-ov5640-csi1.dtb
imx8qm-mek-ov5640-dual-dtbs := imx8qm-mek.dtb imx8qm-mek-ov5640-csi0.dtbo imx8qm-mek-ov5640-csi1.dtbo
dtb-${CONFIG_ARCH_MXC} += imx8qm-mek-ov5640-dual.dtb
imx8qm-mek-ov5640-csi0-rpmsg-dtbs := imx8qm-mek-rpmsg.dtb imx8qm-mek-ov5640-csi0.dtbo
dtb-${CONFIG_ARCH_MXC} += imx8qm-mek-ov5640-csi0-rpmsg.dtb
imx8qm-mek-ov5640-csi1-rpmsg-dtbs := imx8qm-mek-rpmsg.dtb imx8qm-mek-ov5640-csi1.dtbo
dtb-${CONFIG_ARCH_MXC} += imx8qm-mek-ov5640-csi1-rpmsg.dtb
imx8qm-mek-ov5640-dual-rpmsg-dtbs := imx8qm-mek-rpmsg.dtb imx8qm-mek-ov5640-csi0.dtbo imx8qm-mek-ov5640-csi1.dtbo
dtb-${CONFIG_ARCH_MXC} += imx8qm-mek-ov5640-dual-rpmsg.dtb
imx8qm-mek-max9286-csi0-dtbs := imx8qm-mek.dtb imx8qm-mek-max9286-csi0.dtbo
dtb-${CONFIG_ARCH_MXC} += imx8qm-mek-max9286-csi0.dtb
imx8qm-mek-max9286-csi1-dtbs := imx8qm-mek.dtb imx8qm-mek-max9286-csi1.dtbo
dtb-${CONFIG_ARCH_MXC} += imx8qm-mek-max9286-csi1.dtb
imx8qm-mek-max9286-dual-dtbs := imx8qm-mek.dtb imx8qm-mek-max9286-csi0.dtbo imx8qm-mek-max9286-csi1.dtbo
dtb-${CONFIG_ARCH_MXC} += imx8qm-mek-max9286-dual.dtb

imx8qm-mek-revd-dtbs := imx8qm-mek.dtb imx8qm-mek-revd.dtbo
imx8qm-mek-revd-rpmsg-dtbs := imx8qm-mek-rpmsg.dtb imx8qm-mek-revd.dtbo
imx8qm-mek-revd-ov5640-csi0-dtbs := imx8qm-mek-ov5640-csi0.dtb imx8qm-mek-revd.dtbo
imx8qm-mek-revd-ov5640-csi1-dtbs := imx8qm-mek-ov5640-csi1.dtb imx8qm-mek-revd.dtbo
imx8qm-mek-revd-ov5640-dual-dtbs := imx8qm-mek-ov5640-dual.dtb imx8qm-mek-revd.dtbo
imx8qm-mek-revd-max9286-csi0-dtbs := imx8qm-mek-max9286-csi0.dtb imx8qm-mek-revd.dtbo
imx8qm-mek-revd-max9286-csi1-dtbs := imx8qm-mek-max9286-csi1.dtb imx8qm-mek-revd.dtbo
imx8qm-mek-revd-max9286-dual-dtbs := imx8qm-mek-max9286-dual.dtb imx8qm-mek-revd.dtbo
imx8qm-mek-revd-enet2-tja1100-dtbs := imx8qm-mek-enet2-tja1100.dtb imx8qm-mek-revd.dtbo
imx8qm-mek-revd-hdmi-dtbs := imx8qm-mek-hdmi.dtb imx8qm-mek-revd.dtbo
imx8qm-mek-revd-jdi-wuxga-lvds1-panel-dtbs := imx8qm-mek-jdi-wuxga-lvds1-panel.dtb imx8qm-mek-revd.dtbo
imx8qm-mek-revd-jdi-wuxga-lvds1-panel-rpmsg-dtbs := imx8qm-mek-jdi-wuxga-lvds1-panel-rpmsg.dtb imx8qm-mek-revd.dtbo
imx8qm-mek-revd-pcie-ep-dtbs := imx8qm-mek-pcie-ep.dtb imx8qm-mek-revd.dtbo
imx8qm-mek-revd-usdhc3-m2-dtbs := imx8qm-mek-usdhc3-m2.dtb imx8qm-mek-revd.dtbo
imx8qm-mek-revd-usd-wifi-dtbs := imx8qm-mek-usd-wifi.dtb imx8qm-mek-revd.dtbo
imx8qm-mek-revd-ca53-dtbs := imx8qm-mek-ca53.dtb imx8qm-mek-revd.dtbo
imx8qm-mek-revd-ca72-dtbs := imx8qm-mek-ca72.dtb imx8qm-mek-revd.dtbo
imx8qm-mek-revd-dsi-rm67191-dtbs := imx8qm-mek-dsi-rm67191.dtb imx8qm-mek-revd.dtbo
imx8qm-mek-revd-dsi-rm67199-dtbs := imx8qm-mek-dsi-rm67199.dtb imx8qm-mek-revd.dtbo
imx8qm-mek-revd-root-dtbs := imx8qm-mek-root.dtb imx8qm-mek-revd.dtbo
imx8qm-mek-revd-sof-cs42888-dtbs := imx8qm-mek-sof-cs42888.dtb imx8qm-mek-revd.dtbo

dtb-$(CONFIG_ARCH_MXC) += imx8qm-mek-revd.dtb
dtb-$(CONFIG_ARCH_MXC) += imx8qm-mek-revd-rpmsg.dtb
dtb-$(CONFIG_ARCH_MXC) += imx8qm-mek-revd-enet2-tja1100.dtb
dtb-$(CONFIG_ARCH_MXC) += imx8qm-mek-revd-hdmi.dtb
dtb-$(CONFIG_ARCH_MXC) += imx8qm-mek-revd-jdi-wuxga-lvds1-panel.dtb
dtb-$(CONFIG_ARCH_MXC) += imx8qm-mek-revd-jdi-wuxga-lvds1-panel-rpmsg.dtb
dtb-$(CONFIG_ARCH_MXC) += imx8qm-mek-revd-pcie-ep.dtb
dtb-$(CONFIG_ARCH_MXC) += imx8qm-mek-revd-usdhc3-m2.dtb
dtb-$(CONFIG_ARCH_MXC) += imx8qm-mek-revd-usd-wifi.dtb
dtb-$(CONFIG_ARCH_MXC) += imx8qm-mek-revd-ca53.dtb
dtb-$(CONFIG_ARCH_MXC) += imx8qm-mek-revd-ca72.dtb
dtb-$(CONFIG_ARCH_MXC) += imx8qm-mek-revd-dsi-rm67191.dtb
dtb-$(CONFIG_ARCH_MXC) += imx8qm-mek-revd-dsi-rm67199.dtb
dtb-$(CONFIG_ARCH_MXC) += imx8qm-mek-revd-root.dtb
dtb-$(CONFIG_ARCH_MXC) += imx8qm-mek-revd-sof-cs42888.dtb
dtb-${CONFIG_ARCH_MXC} += imx8qm-mek-revd-ov5640-csi0.dtb
dtb-${CONFIG_ARCH_MXC} += imx8qm-mek-revd-ov5640-csi1.dtb
dtb-${CONFIG_ARCH_MXC} += imx8qm-mek-revd-ov5640-dual.dtb
dtb-${CONFIG_ARCH_MXC} += imx8qm-mek-revd-max9286-csi0.dtb
dtb-${CONFIG_ARCH_MXC} += imx8qm-mek-revd-max9286-csi1.dtb
dtb-${CONFIG_ARCH_MXC} += imx8qm-mek-revd-max9286-dual.dtb
#dtb-$(CONFIG_ARCH_MXC) += imx8qxp-ai_ml.dtb
#dtb-$(CONFIG_ARCH_MXC) += imx8qxp-colibri-aster.dtb
#dtb-$(CONFIG_ARCH_MXC) += imx8qxp-colibri-eval-v3.dtb
#dtb-$(CONFIG_ARCH_MXC) += imx8qxp-colibri-iris.dtb
#dtb-$(CONFIG_ARCH_MXC) += imx8qxp-colibri-iris-v2.dtb
dtb-$(CONFIG_ARCH_MXC) += imx8ulp-evk.dtb imx8ulp-evk-lpspi-slave.dtb \
			  imx8ulp-evk-i3c.dtb imx8ulp-evk-rk055hdmipi4m.dtb imx8ulp-evk-rk055hdmipi4mv2.dtb \
			  imx8ulp-evk-epdc.dtb imx8ulp-evk-sof-btsco.dtb \
			  imx8ulp-evk-flexio-i2c.dtb imx8ulp-evk-nd.dtb imx8ulp-9x9-evk.dtb \
			  imx8ulp-9x9-evk-rk055hdmipi4m.dtb imx8ulp-9x9-evk-rk055hdmipi4mv2.dtb \
			  imx8ulp-9x9-evk-lpspi.dtb imx8ulp-9x9-evk-lpspi-slave.dtb \
			  imx8ulp-9x9-evk-i3c.dtb imx8ulp-9x9-evk-sof-btsco.dtb \
			  imx8ulp-evk-lpa.dtb imx8ulp-9x9-evk-lpa.dtb \
			  imx8ulp-evk-tpm.dtb imx8ulp-9x9-evk-tpm.dtb
dtb-$(CONFIG_ARCH_MXC) += imx8dxl-evk.dtb \
			  imx8dxl-evk-enet0.dtb imx8dxl-evk-enet0-tja1100.dtb \
			  imx8dxl-evk-pcie-ep.dtb \
			  imx8dxl-evk-lcdif.dtb \
			  imx8dxl-evk-lpspi-slave.dtb \
			  imx8dxl-evk-rpmsg.dtb \
			  imx8dxl-ddr3l-evk.dtb \
			  imx8dxl-ddr3l-evk-rpmsg.dtb
dtb-$(CONFIG_ARCH_MXC) += imx8dxl-phantom-mek.dtb \
			  imx8dxl-phantom-mek-rpmsg.dtb \
			  imx8dxl-orangebox.dtb \
			  imx8dxl-orangebox-sd.dtb
dtb-$(CONFIG_ARCH_MXC) += imx8qxp-mek.dtb \
			  imx8qxp-mek-enet2.dtb imx8qxp-mek-enet2-sja1105.dtb \
			  imx8qxp-mek-enet2-tja1100.dtb \
			  imx8qxp-mek-sof-cs42888.dtb imx8qxp-mek-sof.dtb\
			  imx8qxp-mek-sof-wm8960.dtb imx8qxp-mek-sof-wm8962.dtb\
			  imx8qxp-mek-rpmsg.dtb imx8qxp-mek-a0.dtb \
			  imx8qxp-mek-it6263-lvds0-dual-channel.dtb \
			  imx8qxp-mek-it6263-lvds1-dual-channel.dtb \
			  imx8qxp-mek-it6263-lvds0-dual-channel-rpmsg.dtb \
			  imx8qxp-mek-it6263-lvds1-dual-channel-rpmsg.dtb \
			  imx8qxp-mek-jdi-wuxga-lvds0-panel.dtb \
			  imx8qxp-mek-jdi-wuxga-lvds1-panel.dtb \
			  imx8qxp-mek-jdi-wuxga-lvds0-panel-rpmsg.dtb \
			  imx8qxp-mek-jdi-wuxga-lvds1-panel-rpmsg.dtb \
			  imx8qxp-mek-dsi-rm67191.dtb \
			  imx8qxp-mek-dsi-rm67191-rpmsg.dtb \
			  imx8qxp-mek-dsi-rm67199.dtb \
			  imx8qxp-mek-dsi-rm67199-rpmsg.dtb \
			  imx8qxp-mek-dpu-lcdif.dtb \
			  imx8qxp-mek-dpu-lcdif-rpmsg.dtb \
			  imx8qxp-mek-pcie-ep.dtb \
			  imx8dx-mek.dtb imx8dx-mek-rpmsg.dtb \
			  imx8dx-mek-enet2-tja1100.dtb \
			  imx8dx-mek-it6263-lvds0-dual-channel.dtb \
			  imx8dx-mek-it6263-lvds1-dual-channel.dtb \
			  imx8dx-mek-it6263-lvds0-dual-channel-rpmsg.dtb \
			  imx8dx-mek-it6263-lvds1-dual-channel-rpmsg.dtb \
			  imx8dx-mek-jdi-wuxga-lvds0-panel.dtb \
			  imx8dx-mek-jdi-wuxga-lvds1-panel.dtb \
			  imx8dx-mek-jdi-wuxga-lvds0-panel-rpmsg.dtb \
			  imx8dx-mek-jdi-wuxga-lvds1-panel-rpmsg.dtb \
			  imx8dx-mek-dsi-rm67191.dtb \
			  imx8dx-mek-dsi-rm67191-rpmsg.dtb \
			  imx8qxp-mek-lcdif.dtb \
			  imx8qxp-mek-lcdif-rpmsg.dtb \
			  imx8qxp-lpddr4-val-a0.dtb \
			  imx8qxp-lpddr4-val.dtb imx8qxp-lpddr4-val-mqs.dtb imx8qxp-ddr3l-val.dtb \
			  imx8qxp-lpddr4-val-lpspi.dtb imx8qxp-lpddr4-val-lpspi-slave.dtb \
			  imx8qxp-lpddr4-val-spdif.dtb imx8qxp-lpddr4-val-gpmi-nand.dtb imx8dxp-lpddr4-val.dtb \
			  imx8qxp-17x17-val.dtb imx8dx-lpddr4-val.dtb imx8dx-17x17-val.dtb
dtb-$(CONFIG_ARCH_MXC) += imx8qxp-mek-dom0.dtb imx8qxp-mek-root.dtb \
			  imx8qxp-mek-inmate.dtb

imx8qxp-mek-ov5640-csi-dtbs := imx8qxp-mek.dtb imx8qxp-mek-ov5640-csi.dtbo
dtb-${CONFIG_ARCH_MXC} += imx8qxp-mek-ov5640-csi.dtb
imx8qxp-mek-ov5640-parallel-dtbs := imx8qxp-mek.dtb imx8qxp-mek-ov5640-parallel.dtbo
dtb-${CONFIG_ARCH_MXC} += imx8qxp-mek-ov5640-parallel.dtb
imx8qxp-mek-ov5640-dual-dtbs := imx8qxp-mek.dtb imx8qxp-mek-ov5640-csi.dtbo imx8qxp-mek-ov5640-parallel.dtbo
dtb-${CONFIG_ARCH_MXC} += imx8qxp-mek-ov5640-dual.dtb
imx8qxp-mek-ov5640-csi-rpmsg-dtbs := imx8qxp-mek-rpmsg.dtb imx8qxp-mek-ov5640-csi.dtbo
dtb-${CONFIG_ARCH_MXC} += imx8qxp-mek-ov5640-csi-rpmsg.dtb
imx8qxp-mek-ov5640-parallel-rpmsg-dtbs := imx8qxp-mek-rpmsg.dtb imx8qxp-mek-ov5640-parallel.dtbo
dtb-${CONFIG_ARCH_MXC} += imx8qxp-mek-ov5640-parallel-rpmsg.dtb
imx8qxp-mek-ov5640-dual-rpmsg-dtbs := imx8qxp-mek-rpmsg.dtb imx8qxp-mek-ov5640-csi.dtbo imx8qxp-mek-ov5640-parallel.dtbo
dtb-${CONFIG_ARCH_MXC} += imx8qxp-mek-ov5640-dual-rpmsg.dtb
imx8qxp-mek-max9286-dtbs := imx8qxp-mek.dtb imx8qxp-mek-max9286.dtbo
dtb-${CONFIG_ARCH_MXC} += imx8qxp-mek-max9286.dtb
imx8qxp-mek-max9286-rpmsg-dtbs := imx8qxp-mek-rpmsg.dtb imx8qxp-mek-max9286.dtbo
dtb-${CONFIG_ARCH_MXC} += imx8qxp-mek-max9286-rpmsg.dtb

dtb-$(CONFIG_ARCH_MXC) += imx93-14x14-evk.dtb \
			  imx93-14x14-evk-tja1103.dtb imx93-14x14-evk-rm67199.dtb \
			  imx93-14x14-evk-mqs.dtb imx93-14x14-evk-aud-hat.dtb \
			  imx93-14x14-evk-lvds-it6263.dtb imx93-14x14-evk-sja1105.dtb \
			  imx93-14x14-evk-flexspi-m2.dtb imx93-14x14-evk-dsi-serdes.dtb \
			  imx93-14x14-evk-i3c.dtb
dtb-$(CONFIG_ARCH_MXC) += imx93-11x11-evk.dtb \
			  imx93-11x11-evk-inmate.dtb imx93-11x11-evk-root.dtb imx93-11x11-evk-flexio-i2c.dtb \
			  imx93-11x11-evk-i2c-spi-slave.dtb \
			  imx93-11x11-evk-i3c.dtb imx93-11x11-evk-lpuart.dtb \
			  imx93-11x11-evk-mqs.dtb imx93-11x11-evk-aud-hat.dtb \
			  imx93-11x11-evk-rm67199.dtb imx93-11x11-evk-boe-wxga-lvds-panel.dtb \
			  imx93-11x11-evk-flexspi-m2.dtb \
			  imx93-11x11-evk-mt9m114.dtb \
			  imx93-11x11-evk-ld.dtb \
			  imx93-11x11-evk-iw612-otbr.dtb \
			  imx93-11x11-evk-rpmsg.dtb imx93-11x11-evk-rpmsg-lpv.dtb

dtb-$(CONFIG_ARCH_MXC) += imx91-11x11-evk.dtb \
			  imx91-11x11-evk-flexspi-m2.dtb imx91-11x11-evk-flexspi-nand-m2.dtb \
			  imx91-11x11-evk-lpuart.dtb \
			  imx91-11x11-evk-mqs.dtb imx91-11x11-evk-aud-hat.dtb \
			  imx91-11x11-evk-tianma-wvga-panel.dtb \
			  imx91-11x11-evk-mt9m114.dtb \
			  imx91-11x11-evk-ld.dtb \
			  imx91-11x11-evk-i2c-spi-slave.dtb \
			  imx91-11x11-evk-i3c.dtb

imx93-11x11-evk-pmic-pf0900-dtbs := imx93-11x11-evk.dtb imx93-11x11-evk-pmic-pf0900.dtbo
imx93-11x11-evk-pmic-pf0900-root-dtbs := imx93-11x11-evk-root.dtb imx93-11x11-evk-pmic-pf0900.dtbo
imx93-11x11-evk-pmic-pf0900-flexio-i2c-dtbs := imx93-11x11-evk-flexio-i2c.dtb imx93-11x11-evk-pmic-pf0900.dtbo
imx93-11x11-evk-pmic-pf0900-i2c-spi-slave-dtbs := imx93-11x11-evk-i2c-spi-slave.dtb imx93-11x11-evk-pmic-pf0900.dtbo
imx93-11x11-evk-pmic-pf0900-i3c-dtbs := imx93-11x11-evk-i3c.dtb imx93-11x11-evk-pmic-pf0900.dtbo
imx93-11x11-evk-pmic-pf0900-lpuart-dtbs := imx93-11x11-evk-lpuart.dtb imx93-11x11-evk-pmic-pf0900.dtbo
imx93-11x11-evk-pmic-pf0900-mqs-dtbs := imx93-11x11-evk-mqs.dtb imx93-11x11-evk-pmic-pf0900.dtbo
imx93-11x11-evk-pmic-pf0900-aud-hat-dtbs := imx93-11x11-evk-aud-hat.dtb imx93-11x11-evk-pmic-pf0900.dtbo
imx93-11x11-evk-pmic-pf0900-rm67199-dtbs := imx93-11x11-evk-rm67199.dtb imx93-11x11-evk-pmic-pf0900.dtbo
imx93-11x11-evk-pmic-pf0900-boe-wxga-lvds-panel-dtbs := imx93-11x11-evk-boe-wxga-lvds-panel.dtb imx93-11x11-evk-pmic-pf0900.dtbo
imx93-11x11-evk-pmic-pf0900-flexspi-m2-dtbs := imx93-11x11-evk-flexspi-m2.dtb imx93-11x11-evk-pmic-pf0900.dtbo
imx93-11x11-evk-pmic-pf0900-mt9m114-dtbs := imx93-11x11-evk-mt9m114.dtb imx93-11x11-evk-pmic-pf0900.dtbo
imx93-11x11-evk-pmic-pf0900-ld-dtbs := imx93-11x11-evk-ld.dtb imx93-11x11-evk-pmic-pf0900.dtbo
imx93-11x11-evk-pmic-pf0900-rpmsg-dtbs := imx93-11x11-evk-rpmsg.dtb imx93-11x11-evk-pmic-pf0900.dtbo
imx93-11x11-evk-pmic-pf0900-rpmsg-lpv-dtbs := imx93-11x11-evk-rpmsg-lpv.dtb imx93-11x11-evk-pmic-pf0900.dtbo

dtb-$(CONFIG_ARCH_MXC) += imx93-11x11-evk-pmic-pf0900.dtb
dtb-$(CONFIG_ARCH_MXC) += imx93-11x11-evk-pmic-pf0900-root.dtb
dtb-$(CONFIG_ARCH_MXC) += imx93-11x11-evk-pmic-pf0900-flexio-i2c.dtb
dtb-$(CONFIG_ARCH_MXC) += imx93-11x11-evk-pmic-pf0900-i2c-spi-slave.dtb
dtb-$(CONFIG_ARCH_MXC) += imx93-11x11-evk-pmic-pf0900-i3c.dtb
dtb-$(CONFIG_ARCH_MXC) += imx93-11x11-evk-pmic-pf0900-lpuart.dtb
dtb-$(CONFIG_ARCH_MXC) += imx93-11x11-evk-pmic-pf0900-mqs.dtb
dtb-$(CONFIG_ARCH_MXC) += imx93-11x11-evk-pmic-pf0900-aud-hat.dtb
dtb-$(CONFIG_ARCH_MXC) += imx93-11x11-evk-pmic-pf0900-rm67199.dtb
dtb-$(CONFIG_ARCH_MXC) += imx93-11x11-evk-pmic-pf0900-boe-wxga-lvds-panel.dtb
dtb-$(CONFIG_ARCH_MXC) += imx93-11x11-evk-pmic-pf0900-flexspi-m2.dtb
dtb-$(CONFIG_ARCH_MXC) += imx93-11x11-evk-pmic-pf0900-mt9m114.dtb
dtb-$(CONFIG_ARCH_MXC) += imx93-11x11-evk-pmic-pf0900-ld.dtb
dtb-$(CONFIG_ARCH_MXC) += imx93-11x11-evk-pmic-pf0900-rpmsg.dtb
dtb-$(CONFIG_ARCH_MXC) += imx93-11x11-evk-pmic-pf0900-rpmsg-lpv.dtb

dtb-$(CONFIG_ARCH_MXC) += imx93-9x9-qsb.dtb \
			  imx93-9x9-qsb-can1.dtb \
			  imx93-9x9-qsb-flexspi-m2.dtb \
			  imx93-9x9-qsb-tianma-wvga-panel.dtb \
			  imx93-9x9-qsb-ontat-wvga-panel.dtb \
			  imx93-9x9-qsb-mt9m114.dtb \
			  imx93-9x9-qsb-i3c.dtb \
			  imx93-9x9-qsb-lpspi.dtb imx93-9x9-qsb-lpspi-slave.dtb \
			  imx93-9x9-qsb-aud-hat.dtb \
			  imx93-9x9-qsb-ld.dtb \
			  imx93-9x9-qsb-rpmsg.dtb imx93-9x9-qsb-rpmsg-lpv.dtb
dtb-$(CONFIG_ARCH_MXC) += imx93-tqma9352-mba93xxca.dtb
dtb-$(CONFIG_ARCH_MXC) += imx93-tqma9352-mba93xxla.dtb
dtb-$(CONFIG_ARCH_MXC) += imx91-9x9-qsb.dtb \
			  imx91-9x9-qsb-can1.dtb \
			  imx91-9x9-qsb-flexspi-nand.dtb \
			  imx91-9x9-qsb-flexspi-m2.dtb \
			  imx91-9x9-qsb-hfp.dtb \
			  imx91-9x9-qsb-mt9m114.dtb \
			  imx91-9x9-qsb-tianma-wvga-panel.dtb
dtb-$(CONFIG_ARCH_MXC) += imx91-9x9-qsb-aud-hat.dtb
dtb-$(CONFIG_ARCH_MXC) += imx91-9x9-qsb-ld.dtb
dtb-$(CONFIG_ARCH_MXC) += imx91-9x9-qsb-i2c-spi-slave.dtb
dtb-$(CONFIG_ARCH_MXC) += imx91-9x9-qsb-i3c.dtb
dtb-$(CONFIG_ARCH_MXC) += imx95-15x15-evk.dtb
dtb-$(CONFIG_ARCH_MXC) += imx95-15x15-evk-flexspi-m2.dtb
dtb-$(CONFIG_ARCH_MXC) += imx95-15x15-evk-mqs.dtb
dtb-$(CONFIG_ARCH_MXC) += imx95-15x15-evk-aud-hat.dtb
dtb-$(CONFIG_ARCH_MXC) += imx95-15x15-evk-root.dtb imx95-15x15-evk-inmate.dtb
dtb-$(CONFIG_ARCH_MXC) += imx95-15x15-ab2.dtb
dtb-$(CONFIG_ARCH_MXC) += imx95-15x15-evk-i2c-spi-slave.dtb
DTC_FLAGS_imx95-15x15-evk-boe-wxga-lvds0-panel := -@
DTC_FLAGS_imx95-15x15-evk-boe-wxga-lvds1-panel := -@
dtb-$(CONFIG_ARCH_MXC) += imx95-15x15-evk-boe-wxga-lvds0-panel.dtb
dtb-$(CONFIG_ARCH_MXC) += imx95-15x15-evk-boe-wxga-lvds1-panel.dtb
dtb-$(CONFIG_ARCH_MXC) += imx95-15x15-evk-boe-wxga-lvds-two-panels.dtb

imx95-15x15-evk-adv7535-dtbs := imx95-15x15-evk.dtb imx95-15x15-evk-adv7535.dtbo
dtb-${CONFIG_ARCH_MXC} += imx95-15x15-evk-adv7535.dtb

imx95-15x15-evk-ap1302-dtbs := imx95-15x15-evk.dtb imx95-15x15-evk-ap1302.dtbo
dtb-$(CONFIG_ARCH_MXC) += imx95-15x15-evk-ap1302.dtb
imx95-15x15-evk-ap1302_rpi-dtbs := imx95-15x15-evk.dtb \
				   imx95-15x15-evk-ap1302.dtbo \
				   imx95-15x15-evk-ap1302_rpi.dtbo
dtb-$(CONFIG_ARCH_MXC) += imx95-15x15-evk-ap1302_rpi.dtb

imx95-15x15-evk-rm692c9-dtbs := imx95-15x15-evk.dtb imx95-15x15-evk-rm692c9.dtbo
dtb-$(CONFIG_ARCH_MXC) += imx95-15x15-evk-rm692c9.dtb

imx95-15x15-evk-adv7535-ap1302-dtbs := imx95-15x15-evk.dtb \
				       imx95-15x15-evk-adv7535.dtbo \
				       imx95-15x15-evk-ap1302.dtbo
dtb-$(CONFIG_ARCH_MXC) += imx95-15x15-evk-adv7535-ap1302.dtb

imx95-15x15-evk-adv7535-ap1302_rpi-dtbs := imx95-15x15-evk-adv7535-ap1302.dtb \
				       imx95-15x15-evk-ap1302_rpi.dtbo
dtb-$(CONFIG_ARCH_MXC) += imx95-15x15-evk-adv7535-ap1302_rpi.dtb

imx95-15x15-evk-lt9611uxc-dtbs := imx95-15x15-evk.dtb imx95-15x15-evk-lt9611uxc.dtbo
dtb-${CONFIG_ARCH_MXC} += imx95-15x15-evk-lt9611uxc.dtb

imx95-15x15-evk-lt9611uxc-ap1302-dtbs := imx95-15x15-evk.dtb \
					 imx95-15x15-evk-lt9611uxc.dtbo \
					 imx95-15x15-evk-ap1302.dtbo
dtb-$(CONFIG_ARCH_MXC) += imx95-15x15-evk-lt9611uxc-ap1302.dtb

imx95-15x15-evk-ox05b1s-dtbs := imx95-15x15-evk.dtb imx95-15x15-evk-ox05b1s.dtbo
dtb-${CONFIG_ARCH_MXC} += imx95-15x15-evk-ox05b1s.dtb

imx95-15x15-evk-ox05b1s-combo-dtbs := imx95-15x15-evk.dtb imx95-15x15-evk-ox05b1s-combo.dtbo
dtb-${CONFIG_ARCH_MXC} += imx95-15x15-evk-ox05b1s-combo.dtb

imx95-15x15-evk-ox05b1s-isp-adv7535-dtbs := imx95-15x15-evk-adv7535.dtb \
					   imx95-15x15-evk-ox05b1s.dtbo \
					   imx95-19x19-evk-neoisp.dtbo
dtb-$(CONFIG_ARCH_MXC) += imx95-15x15-evk-ox05b1s-isp-adv7535.dtb

imx95-15x15-evk-ox05b1s-isp-lvds-dual-dtbs := imx95-15x15-evk-boe-wxga-lvds-two-panels.dtb \
								   imx95-15x15-evk-ox05b1s.dtbo \
								   imx95-15x15-evk-ox05b1s-combo.dtbo \
								   imx95-19x19-evk-neoisp.dtbo
dtb-$(CONFIG_ARCH_MXC) += imx95-15x15-evk-ox05b1s-isp-lvds-dual.dtb

imx95-15x15-evk-os08a20-dtbs := imx95-15x15-evk.dtb imx95-15x15-evk-os08a20.dtbo
dtb-${CONFIG_ARCH_MXC} += imx95-15x15-evk-os08a20.dtb

imx95-15x15-evk-os08a20-combo-dtbs := imx95-15x15-evk.dtb imx95-15x15-evk-os08a20-combo.dtbo
dtb-$(CONFIG_ARCH_MXC) += imx95-15x15-evk-os08a20-combo.dtb

imx95-15x15-evk-os08a20-isp-adv7535-dtbs := imx95-15x15-evk-adv7535.dtb \
					    imx95-15x15-evk-os08a20.dtbo \
					    imx95-19x19-evk-neoisp.dtbo
dtb-$(CONFIG_ARCH_MXC) += imx95-15x15-evk-os08a20-isp-adv7535.dtb

imx95-15x15-evk-os08a20-isp-lvds-dual-dtbs := imx95-15x15-evk-boe-wxga-lvds-two-panels.dtb \
								   imx95-15x15-evk-os08a20.dtbo \
								   imx95-15x15-evk-os08a20-combo.dtbo \
								   imx95-19x19-evk-neoisp.dtbo
dtb-$(CONFIG_ARCH_MXC) += imx95-15x15-evk-os08a20-isp-lvds-dual.dtb

DTC_FLAGS_imx95-15x15-evk-rpmsg := -@
DTC_FLAGS_imx95-15x15-evk-rpmsg-lpv := -@
dtb-$(CONFIG_ARCH_MXC) += imx95-15x15-evk-rpmsg.dtb imx95-15x15-evk-rpmsg-lpv.dtb

dtb-$(CONFIG_ARCH_MXC) += imx95-19x19-evk.dtb imx95-19x19-evk-root.dtb imx95-19x19-evk-inmate.dtb \
			  imx95-19x19-evk-lpspi-slave.dtb

imx95-15x15-evk-ox03c10-dtbs := imx95-15x15-evk.dtb imx95-15x15-evk-ox03c10.dtbo
dtb-$(CONFIG_ARCH_MXC) += imx95-15x15-evk-ox03c10.dtb

imx95-15x15-evk-ox03c10-isp-adv7535-dtbs := imx95-15x15-evk-adv7535.dtb \
					    imx95-15x15-evk-ox03c10.dtbo \
					    imx95-19x19-evk-neoisp.dtbo
dtb-$(CONFIG_ARCH_MXC) += imx95-15x15-evk-ox03c10-isp-adv7535.dtb
dtb-$(CONFIG_ARCH_MXC) += imx95-19x19-evk-sof-wm8962.dtb

imx95-19x19-evk-pcie1-ep-dtbs := imx95-19x19-evk.dtb imx95-19x19-evk-pcie1-ep.dtbo
dtb-$(CONFIG_ARCH_MXC) += imx95-19x19-evk-pcie1-ep.dtb

imx95-19x19-evk-ap1302-dtbs := imx95-19x19-evk.dtb imx95-19x19-evk-ap1302.dtbo
dtb-$(CONFIG_ARCH_MXC) += imx95-19x19-evk-ap1302.dtb

dtb-$(CONFIG_ARCH_MXC) += imx95-19x19-evk-tja1103-tja1120.dtb imx95-19x19-evk-tja1103-rmii.dtb
dtb-$(CONFIG_ARCH_MXC) += imx95-19x19-evk-netc-rpmsg.dtb

imx95-19x19-evk-os08a20-dtbs := imx95-19x19-evk.dtb imx95-19x19-evk-os08a20.dtbo
dtb-$(CONFIG_ARCH_MXC) += imx95-19x19-evk-os08a20.dtb

imx95-19x19-evk-ox05b1s-dtbs := imx95-19x19-evk.dtb imx95-19x19-evk-ox05b1s.dtbo
dtb-$(CONFIG_ARCH_MXC) += imx95-19x19-evk-ox05b1s.dtb

imx95-19x19-evk-ox05b1s-combo-dtbs := imx95-19x19-evk.dtb imx95-19x19-evk-ox05b1s-combo.dtbo
dtb-$(CONFIG_ARCH_MXC) += imx95-19x19-evk-ox05b1s-combo.dtb

imx95-19x19-evk-ti-serdes-dtbs := imx95-19x19-evk.dtb imx95-19x19-evk-ti-serdes.dtbo
dtb-$(CONFIG_ARCH_MXC) += imx95-19x19-evk-ti-serdes.dtb

imx95-19x19-evk-ti-serdes-combo-dtbs := imx95-19x19-evk.dtb imx95-19x19-evk-ti-serdes-combo.dtbo
dtb-$(CONFIG_ARCH_MXC) += imx95-19x19-evk-ti-serdes-combo.dtb

imx95-19x19-evk-os08a20-combo-dtbs := imx95-19x19-evk.dtb imx95-19x19-evk-os08a20-combo.dtbo
dtb-$(CONFIG_ARCH_MXC) += imx95-19x19-evk-os08a20-combo.dtb

imx95-19x19-evk-os08a20-dual-dtbs := imx95-19x19-evk.dtb \
				     imx95-19x19-evk-os08a20.dtbo \
				     imx95-19x19-evk-os08a20-combo.dtbo
dtb-$(CONFIG_ARCH_MXC) += imx95-19x19-evk-os08a20-dual.dtb

imx95-19x19-evk-adv7535-dtbs := imx95-19x19-evk.dtb imx95-19x19-evk-adv7535.dtbo
dtb-$(CONFIG_ARCH_MXC) += imx95-19x19-evk-adv7535.dtb

imx95-19x19-evk-dsi-serdes-dtbs := imx95-19x19-evk.dtb imx95-19x19-evk-dsi-serdes.dtbo
dtb-$(CONFIG_ARCH_MXC) += imx95-19x19-evk-dsi-serdes.dtb

imx95-19x19-evk-adv7535-ap1302-dtbs := imx95-19x19-evk.dtb \
				       imx95-19x19-evk-adv7535.dtbo \
				       imx95-19x19-evk-ap1302.dtbo
dtb-$(CONFIG_ARCH_MXC) += imx95-19x19-evk-adv7535-ap1302.dtb

imx95-19x19-evk-lt9611uxc-dtbs := imx95-19x19-evk.dtb imx95-19x19-evk-lt9611uxc.dtbo
dtb-$(CONFIG_ARCH_MXC) += imx95-19x19-evk-lt9611uxc.dtb

imx95-19x19-evk-lt9611uxc-ap1302-dtbs := imx95-19x19-evk.dtb \
					 imx95-19x19-evk-lt9611uxc.dtbo \
					 imx95-19x19-evk-ap1302.dtbo
dtb-$(CONFIG_ARCH_MXC) += imx95-19x19-evk-lt9611uxc-ap1302.dtb

imx95-19x19-evk-tevs-dtbs := imx95-19x19-evk.dtb imx95-19x19-evk-tevs-tev-nxp.dtbo
dtb-$(CONFIG_ARCH_MXC) += imx95-19x19-evk-tevs.dtb

imx95-19x19-evk-adv7535-tevs-dtbs := imx95-19x19-evk.dtb \
				       imx95-19x19-evk-adv7535.dtbo \
				       imx95-19x19-evk-tevs-tev-nxp.dtbo
dtb-$(CONFIG_ARCH_MXC) += imx95-19x19-evk-adv7535-tevs.dtb

imx95-19x19-evk-rm692c9-dtbs := imx95-19x19-evk.dtb imx95-19x19-evk-rm692c9.dtbo
dtb-$(CONFIG_ARCH_MXC) += imx95-19x19-evk-rm692c9.dtb

imx95-19x19-evk-ox03c10-dtbs := imx95-19x19-evk.dtb imx95-19x19-evk-ox03c10.dtbo
dtb-$(CONFIG_ARCH_MXC) += imx95-19x19-evk-ox03c10.dtb

imx95-19x19-evk-ox03c10-combo-dtbs := imx95-19x19-evk.dtb imx95-19x19-evk-ox03c10-combo.dtbo
dtb-$(CONFIG_ARCH_MXC) += imx95-19x19-evk-ox03c10-combo.dtb

imx95-19x19-evk-ox03c10-all-dtbs := imx95-19x19-evk.dtb imx95-19x19-evk-ox03c10.dtbo imx95-19x19-evk-ox03c10-combo.dtbo
dtb-$(CONFIG_ARCH_MXC) += imx95-19x19-evk-ox03c10-all.dtb


imx95-19x19-verdin-ox03c10-dtbs := imx95-19x19-verdin.dtb imx95-19x19-verdin-ox03c10.dtbo
dtb-$(CONFIG_ARCH_MXC) += imx95-19x19-verdin-ox03c10.dtb

imx95-19x19-evk-it6263-lvds0-dtbs := imx95-19x19-evk.dtb imx95-19x19-evk-it6263-lvds0.dtbo
dtb-$(CONFIG_ARCH_MXC) += imx95-19x19-evk-it6263-lvds0.dtb

imx95-19x19-evk-it6263-lvds1-dtbs := imx95-19x19-evk.dtb imx95-19x19-evk-it6263-lvds1.dtbo
dtb-$(CONFIG_ARCH_MXC) += imx95-19x19-evk-it6263-lvds1.dtb
imx95-19x19-evk-cs42888-dtbs := imx95-19x19-evk.dtb imx95-19x19-evk-cs42888.dtbo
dtb-$(CONFIG_ARCH_MXC) += imx95-19x19-evk-cs42888.dtb

imx95-19x19-evk-it6263-lvds-two-disp-dtbs := imx95-19x19-evk.dtb imx95-19x19-evk-it6263-lvds0.dtbo imx95-19x19-evk-it6263-lvds1.dtbo
dtb-$(CONFIG_ARCH_MXC) += imx95-19x19-evk-it6263-lvds-two-disp.dtb

imx95-19x19-evk-it6263-lvds-dual-dtbs := imx95-19x19-evk.dtb imx95-19x19-evk-it6263-lvds-dual.dtbo
dtb-$(CONFIG_ARCH_MXC) += imx95-19x19-evk-it6263-lvds-dual.dtb

imx95-19x19-evk-jdi-wuxga-lvds-panel-dtbs := imx95-19x19-evk.dtb imx95-19x19-evk-jdi-wuxga-lvds-panel.dtbo
dtb-$(CONFIG_ARCH_MXC) += imx95-19x19-evk-jdi-wuxga-lvds-panel.dtb

imx95-19x19-evk-jtag-dtbs := imx95-19x19-evk.dtb imx95-19x19-evk-jtag.dtbo
dtb-$(CONFIG_ARCH_MXC) += imx95-19x19-evk-jtag.dtb

imx95-19x19-evk-neoisp-dtbs := imx95-19x19-evk.dtb imx95-19x19-evk-neoisp.dtbo
dtb-$(CONFIG_ARCH_MXC) += imx95-19x19-evk-neoisp.dtb

imx95-19x19-evk-os08a20-isp-it6263-lvds0-dtbs := imx95-19x19-evk-neoisp.dtb imx95-19x19-evk-os08a20.dtbo \
                        imx95-19x19-evk-it6263-lvds0.dtbo
dtb-$(CONFIG_ARCH_MXC) += imx95-19x19-evk-os08a20-isp-it6263-lvds0.dtb

imx95-19x19-evk-dual-os08a20-isp-it6263-lvds0-dtbs := imx95-19x19-evk-neoisp.dtb \
						      imx95-19x19-evk-os08a20.dtbo \
						      imx95-19x19-evk-os08a20-combo.dtbo \
						      imx95-19x19-evk-it6263-lvds0.dtbo
dtb-$(CONFIG_ARCH_MXC) += imx95-19x19-evk-dual-os08a20-isp-it6263-lvds0.dtb

imx95-19x19-evk-os08a20-isp-lvds-dual-dtbs := imx95-19x19-evk-neoisp.dtb \
							  imx95-19x19-evk-os08a20.dtbo \
							  imx95-19x19-evk-os08a20-combo.dtbo \
							  imx95-19x19-evk-it6263-lvds-dual.dtbo
dtb-$(CONFIG_ARCH_MXC) += imx95-19x19-evk-os08a20-isp-lvds-dual.dtb

imx95-19x19-evk-ox03c10-isp-it6263-lvds0-dtbs := imx95-19x19-evk-neoisp.dtb imx95-19x19-evk-ox03c10.dtbo \
                        imx95-19x19-evk-it6263-lvds0.dtbo
dtb-$(CONFIG_ARCH_MXC) += imx95-19x19-evk-ox03c10-isp-it6263-lvds0.dtb

imx95-19x19-evk-ox05b1s-isp-it6263-lvds0-dtbs := imx95-19x19-evk-neoisp.dtb imx95-19x19-evk-ox05b1s.dtbo \
						 imx95-19x19-evk-it6263-lvds0.dtbo
dtb-$(CONFIG_ARCH_MXC) += imx95-19x19-evk-ox05b1s-isp-it6263-lvds0.dtb

imx95-19x19-evk-ox05b1s-isp-lvds-dual-dtbs := imx95-19x19-evk-neoisp.dtb imx95-19x19-evk-ox05b1s.dtbo \
							  imx95-19x19-evk-ox05b1s-combo.dtbo \
							  imx95-19x19-evk-it6263-lvds-dual.dtbo
dtb-$(CONFIG_ARCH_MXC) += imx95-19x19-evk-ox05b1s-isp-lvds-dual.dtb

dtb-$(CONFIG_ARCH_MXC) += imx95-19x19-verdin.dtb
DTC_FLAGS_imx95-19x19-verdin-rpmsg := -@
dtb-$(CONFIG_ARCH_MXC) += imx95-19x19-verdin-rpmsg.dtb

imx95-19x19-verdin-adv7535-dtbs := imx95-19x19-verdin.dtb imx95-19x19-verdin-adv7535.dtbo
dtb-$(CONFIG_ARCH_MXC) += imx95-19x19-verdin-adv7535.dtb

imx95-19x19-verdin-lt8912-dtbs := imx95-19x19-verdin.dtb imx95-19x19-verdin-lt8912.dtbo
dtb-$(CONFIG_ARCH_MXC) += imx95-19x19-verdin-lt8912.dtb

imx95-19x19-verdin-lt9611uxc-dtbs := imx95-19x19-verdin.dtb imx95-19x19-verdin-lt9611uxc.dtbo
dtb-$(CONFIG_ARCH_MXC) += imx95-19x19-verdin-lt9611uxc.dtb

imx95-19x19-verdin-panel-cap-touch-10inch-dsi-dtbs := imx95-19x19-verdin.dtb imx95-19x19-verdin-panel-cap-touch-10inch-dsi.dtbo
dtb-$(CONFIG_ARCH_MXC) += imx95-19x19-verdin-panel-cap-touch-10inch-dsi.dtb

dtb-$(CONFIG_ARCH_MXC) += imx95-19x19-verdin-panel-cap-touch-10inch-lvds.dtb

imx95-19x19-verdin-ap1302-dtbs := imx95-19x19-verdin.dtb imx95-19x19-verdin-ap1302.dtbo
dtb-$(CONFIG_ARCH_MXC) += imx95-19x19-verdin-ap1302.dtb

imx95-19x19-verdin-adv7535-ap1302-dtbs := imx95-19x19-verdin.dtb \
				       imx95-19x19-verdin-adv7535.dtbo \
				       imx95-19x19-verdin-ap1302.dtbo
dtb-$(CONFIG_ARCH_MXC) += imx95-19x19-verdin-adv7535-ap1302.dtb

imx95-19x19-verdin-lt8912-ap1302-dtbs := imx95-19x19-verdin.dtb \
				       imx95-19x19-verdin-lt8912.dtbo \
				       imx95-19x19-verdin-ap1302.dtbo
dtb-$(CONFIG_ARCH_MXC) += imx95-19x19-verdin-lt8912-ap1302.dtb

imx95-19x19-verdin-lt9611uxc-ap1302-dtbs := imx95-19x19-verdin.dtb \
					  imx95-19x19-verdin-lt9611uxc.dtbo \
					  imx95-19x19-verdin-ap1302.dtbo
dtb-$(CONFIG_ARCH_MXC) += imx95-19x19-verdin-lt9611uxc-ap1302.dtb

imx95-19x19-verdin-os08a20-dtbs := imx95-19x19-verdin.dtb imx95-19x19-verdin-os08a20.dtbo
dtb-$(CONFIG_ARCH_MXC) += imx95-19x19-verdin-os08a20.dtb

imx95-19x19-verdin-os08a20-combo-dtbs := imx95-19x19-verdin.dtb imx95-19x19-verdin-os08a20-combo.dtbo
dtb-$(CONFIG_ARCH_MXC) += imx95-19x19-verdin-os08a20-combo.dtb

imx95-19x19-verdin-os08a20-isp-lt8912-dtbs := imx95-19x19-verdin.dtb \
					      imx95-19x19-verdin-os08a20.dtbo \
					      imx95-19x19-evk-neoisp.dtbo \
					      imx95-19x19-verdin-lt8912.dtbo
dtb-$(CONFIG_ARCH_MXC) += imx95-19x19-verdin-os08a20-isp-lt8912.dtb

imx95-19x19-verdin-os08a20-isp-lvds-dual-dtbs := imx95-19x19-verdin-panel-cap-touch-10inch-lvds.dtb \
					      imx95-19x19-verdin-os08a20.dtbo \
					      imx95-19x19-verdin-os08a20-combo.dtbo \
					      imx95-19x19-evk-neoisp.dtbo
dtb-$(CONFIG_ARCH_MXC) += imx95-19x19-verdin-os08a20-isp-lvds-dual.dtb

imx95-19x19-verdin-ox05b1s-dtbs := imx95-19x19-verdin.dtb imx95-19x19-verdin-ox05b1s.dtbo
dtb-$(CONFIG_ARCH_MXC) += imx95-19x19-verdin-ox05b1s.dtb

imx95-19x19-verdin-ox05b1s-combo-dtbs := imx95-19x19-verdin.dtb imx95-19x19-verdin-ox05b1s-combo.dtbo
dtb-$(CONFIG_ARCH_MXC) += imx95-19x19-verdin-ox05b1s-combo.dtb

imx95-19x19-verdin-ox05b1s-isp-lt8912-dtbs := imx95-19x19-verdin.dtb \
					      imx95-19x19-verdin-ox05b1s.dtbo \
					      imx95-19x19-evk-neoisp.dtbo \
					      imx95-19x19-verdin-lt8912.dtbo
dtb-$(CONFIG_ARCH_MXC) += imx95-19x19-verdin-ox05b1s-isp-lt8912.dtb

imx95-19x19-verdin-ox05b1s-isp-lvds-dual-dtbs := imx95-19x19-verdin-panel-cap-touch-10inch-lvds.dtb \
								    imx95-19x19-verdin-ox05b1s.dtbo \
								    imx95-19x19-verdin-ox05b1s-combo.dtbo \
								    imx95-19x19-evk-neoisp.dtbo
dtb-$(CONFIG_ARCH_MXC) += imx95-19x19-verdin-ox05b1s-isp-lvds-dual.dtb

imx95-19x19-verdin-ox03c10-dtbs := imx95-19x19-verdin.dtb imx95-19x19-verdin-ox03c10.dtbo
dtb-$(CONFIG_ARCH_MXC) += imx95-19x19-verdin-ox03c10.dtb

imx95-19x19-verdin-ox03c10-isp-lt8912-dtbs := imx95-19x19-verdin.dtb \
					      imx95-19x19-verdin-ox03c10.dtbo \
					      imx95-19x19-evk-neoisp.dtbo \
					      imx95-19x19-verdin-lt8912.dtbo
dtb-$(CONFIG_ARCH_MXC) += imx95-19x19-verdin-ox03c10-isp-lt8912.dtb

imx95-19x19-verdin-ti-serdes-dtbs := imx95-19x19-verdin.dtb imx95-19x19-verdin-ti-serdes.dtbo
dtb-$(CONFIG_ARCH_MXC) += imx95-19x19-verdin-ti-serdes.dtb

DTC_FLAGS_imx95-19x19-evk-rpmsg := -@
DTC_FLAGS_imx95-19x19-evk-rpmsg-lpv := -@
dtb-$(CONFIG_ARCH_MXC) += imx95-19x19-evk-rpmsg.dtb imx95-19x19-evk-rpmsg-lpv.dtb

imx95-19x19-verdin-rm692c9-dtbs := imx95-19x19-verdin.dtb imx95-19x19-verdin-rm692c9.dtbo
dtb-$(CONFIG_ARCH_MXC) += imx95-19x19-verdin-rm692c9.dtb

imx95-19x19-verdin-root-dtbs := imx95-19x19-verdin.dtb imx95-19x19-verdin-root.dtbo
dtb-$(CONFIG_ARCH_MXC) += imx95-19x19-verdin-root.dtb imx95-19x19-verdin-inmate.dtb

dtb-$(CONFIG_ARCH_MXC) += imx95-edm-wb.dtb
dtb-$(CONFIG_ARCH_MXC) += imx95-edm-wb-lvds-vl10112880.dtb
dtb-$(CONFIG_ARCH_MXC) += imx95-edm-wb-lvds-vl156192108.dtb
dtb-$(CONFIG_ARCH_MXC) += imx95-edm-wb-tevs.dtb
dtb-$(CONFIG_ARCH_MXC) += imx95-edm-wb-tevs-csi1.dtb
dtb-$(CONFIG_ARCH_MXC) += imx95-edm-wb-fusion-lvds-vl10112880.dtb
dtb-$(CONFIG_ARCH_MXC) += imx95-edm-wb-fusion-lvds-vl156192108.dtb

dtb-$(CONFIG_ARCH_MXC) += imx95-edm-wizard.dtb
dtb-$(CONFIG_ARCH_MXC) += imx95-edm-wizard-lvds-vl10112880.dtb
dtb-$(CONFIG_ARCH_MXC) += imx95-edm-wizard-lvds-vl156192108.dtb
dtb-$(CONFIG_ARCH_MXC) += imx95-edm-wizard-tevs.dtb
dtb-$(CONFIG_ARCH_MXC) += imx95-edm-wizard-tevs-csi1.dtb

dtb-$(CONFIG_ARCH_MXC) += imx95-edm-evm.dtb
dtb-$(CONFIG_ARCH_MXC) += imx95-edm-evm-lvds-vl10112880.dtb
dtb-$(CONFIG_ARCH_MXC) += imx95-edm-evm-lvds-vl156192108.dtb
dtb-$(CONFIG_ARCH_MXC) += imx95-edm-evm-mipi2hdmi-adv7535.dtb
dtb-$(CONFIG_ARCH_MXC) += imx95-edm-evm-tevs.dtb
dtb-$(CONFIG_ARCH_MXC) += imx95-edm-evm-tevs-csi1.dtb
dtb-$(CONFIG_ARCH_MXC) += imx95-edm-evm-tevs-csi0-csi1.dtb
dtb-$(CONFIG_ARCH_MXC) += imx95-edm-evm-fusion-lvds-vl10112880.dtb
dtb-$(CONFIG_ARCH_MXC) += imx95-edm-evm-fusion-lvds-vl156192108.dtb

dtb-$(CONFIG_ARCH_S32) += s32g274a-evb.dtb
dtb-$(CONFIG_ARCH_S32) += s32g274a-rdb2.dtb
dtb-$(CONFIG_ARCH_S32) += s32g399a-rdb3.dtb
dtb-$(CONFIG_ARCH_S32) += s32v234-evb.dtb \
			  s32v234-sbc.dtb
dtb-$(CONFIG_ARCH_MXC) += imx8qm-mek-sof-cs42888.dtb imx8qm-mek-sof-wm8960.dtb \
			  imx8qm-mek-revd-sof-wm8962.dtb imx8qm-mek-sof.dtb
