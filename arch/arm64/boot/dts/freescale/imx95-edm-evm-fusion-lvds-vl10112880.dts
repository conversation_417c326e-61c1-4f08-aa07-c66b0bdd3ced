// SPDX-License-Identifier: (GPL-2.0+ OR MIT)
/*
 * Copyright 2024 Technexion Ltd.
 *
 * Author: <PERSON> <<EMAIL>>
 */

/dts-v1/;

#include "imx95-edm-evm.dts"

/ {
	model = "TechNexion EDM-IMX95 and EVM baseboard with 10.1 inch LVDS panel";
	compatible = "fsl,imx95-edm", "fsl,imx95";

	reg_lvds_pwr: regulator_lvdspwr {	/* LVDS0_VDDEN */
		status = "okay";
	};

	lvds0_backlight: lvds0_backlight {
		status = "okay";
	};

	lvds0_panel {
		compatible = "vxt,vl10112880", "panel-lvds";
		backlight = <&lvds0_backlight>;
		power-supply = <&reg_lvds_pwr>;
		data-mapping = "vesa-24";
		height-mm = <161>;
		width-mm = <243>;

		panel-timing {
			clock-frequency = <71100000>;
			hactive = <1280>;
			vactive = <800>;
			hback-porch = <40>;
			hfront-porch = <40>;
			vback-porch = <10>;
			vfront-porch = <3>;
			hsync-len = <80>;
			vsync-len = <10>;
			de-active = <1>;
		};
		port {
			panel_in: endpoint {
				remote-endpoint = <&lvds0_out>;
			};
		};
	};

	clk_ref: fixed-clock-25M {
		compatible = "fixed-clock";
		#clock-cells = <0>;
		clock-frequency = <25000000>;
		clock-output-names = "clk_ref";
	};
};

&display_pixel_link {
	status = "okay";
};

&ldb {
	#address-cells = <1>;
	#size-cells = <0>;
	assigned-clocks = <&scmi_clk IMX95_CLK_LDBPLL_VCO>,
			  <&scmi_clk IMX95_CLK_LDBPLL>;
	assigned-clock-rates = <2986200000>, <497700000>;
	status = "okay";

	channel@0 {
		#address-cells = <1>;
		#size-cells = <0>;
		reg = <0>;
		status = "okay";

		port@1 {
			reg = <1>;

			lvds0_out: endpoint {
				remote-endpoint = <&panel_in>;
			};
		};
	};
};

&ldb0_phy {
	status = "okay";
};

&pixel_interleaver {
	#address-cells = <1>;
	#size-cells = <0>;
	status = "okay";

	channel@0 {
		reg = <0>;
		status = "okay";
	};
};

&lpi2c2 {
	#address-cells = <1>;
	#size-cells = <0>;
	clock-frequency = <400000>;

	deser_0@30 {
		compatible = "ti,ds90ub960-q1";
		reg = <0x30>;
		clocks = <&clk_ref>;
		clock-names = "refclk";
		powerdown-gpios  = <&tca9555_c21 12 GPIO_ACTIVE_HIGH>;  /* CSI0_PDB */
		i2c-alias-pool = <0x48 0x49 0x4a 0x4b 0x4c 0x4d 0x4e 0x4f>;

		ds90ub960_0_ports: ports {
			#address-cells = <1>;
			#size-cells = <0>;

			/* FPDLink RX 0 */
			port@0 {
				reg = <0>;

				ub960_0_fpd3_1_in: endpoint {
					remote-endpoint = <&ub953_0_1_out>;
				};
			};

			port@1 {
				reg = <1>;

				ub960_0_fpd3_2_in: endpoint {
					remote-endpoint = <&ub953_0_2_out>;
				};
			};

			port@2 {
				reg = <2>;

				ub960_0_fpd3_3_in: endpoint {
					remote-endpoint = <&ub953_0_3_out>;
				};
			};

			port@3 {
				reg = <3>;

				ub960_0_fpd3_4_in: endpoint {
					remote-endpoint = <&ub953_0_4_out>;
				};
			};

			/* CSI-2 TX */
			port@4 {
				reg = <4>;

				ds90ub960_0_csi_out: endpoint {
					remote-endpoint = <&mipi_csi0_ep>;
					data-lanes = <1 2 3 4>;
					link-frequencies = /bits/ 64 <800000000>;
				};
			};
		};

		ds90ub960_0_links: links {
			#address-cells = <1>;
			#size-cells = <0>;

			/* Link 0 has DS90UB953 serializer and TEVS sensor */

			link@0 {
				reg = <0>;
				i2c-alias = <0x43>;

				ti,rx-mode = <3>;
				ti,bc-gpio = <0>;

				serializer_0_1: serializer {
					compatible = "ti,ds90ub953-q1";

					gpio-controller;
					#gpio-cells = <2>;

					#clock-cells = <0>;

					ports {
						#address-cells = <1>;
						#size-cells = <0>;

						port@0 {
							reg = <0>;

							ub953_0_1_in: endpoint {
								data-lanes = <1 2>;
								remote-endpoint = <&tevs_0_1_out>;
							};
						};

						port@1 {
							reg = <1>;

							ub953_0_1_out: endpoint {
								remote-endpoint = <&ub960_0_fpd3_1_in>;
							};
						};
					};

					i2c {
						#address-cells = <1>;
						#size-cells = <0>;

						vizionlink_pca9554_0_1: vizionlink_pca9554_0@25 {
							compatible = "nxp,pca9554";
							reg = <0x25>;
							gpio-controller;
							#gpio-cells = <2>;
							status = "okay";
						};

						tevs_0_1: tevs@48 {
							compatible = "tn,tevs";
							reg = <0x48>;
							reset-gpios = <&vizionlink_pca9554_0_1 4 GPIO_ACTIVE_HIGH>;
							standby-gpios = <&vizionlink_pca9554_0_1 2 GPIO_ACTIVE_HIGH>;

							data-lanes = <2>;
							data-frequency = <460>;
							continuous-clock = <0>;
							status = "okay";

							port {
								tevs_0_1_out: endpoint {
									remote-endpoint = <&ub953_0_1_in>;
								};
							};
						};
					};
				};
			};

			/* Link 1 has DS90UB953 serializer and OX03C10 sensor */

			link@1 {
				reg = <1>;
				i2c-alias = <0x44>;

				ti,rx-mode = <3>;
				ti,bc-gpio = <0>;

				serializer_0_2: serializer {
					compatible = "ti,ds90ub953-q1";

					gpio-controller;
					#gpio-cells = <2>;

					#clock-cells = <0>;

					ports {
						#address-cells = <1>;
						#size-cells = <0>;

						port@0 {
							reg = <0>;

							ub953_0_2_in: endpoint {
								data-lanes = <1 2>;
								remote-endpoint = <&tevs_0_2_out>;
							};
						};

						port@1 {
							reg = <1>;

							ub953_0_2_out: endpoint {
								remote-endpoint = <&ub960_0_fpd3_2_in>;
							};
						};
					};

					i2c {
						#address-cells = <1>;
						#size-cells = <0>;

						vizionlink_pca9554_0_2: vizionlink_pca9554_0@25 {
							compatible = "nxp,pca9554";
							reg = <0x25>;
							gpio-controller;
							#gpio-cells = <2>;
							status = "okay";
						};

						tevs_0_2: tevs@48 {
							compatible = "tn,tevs";
							reg = <0x48>;
							reset-gpios = <&vizionlink_pca9554_0_2 4 GPIO_ACTIVE_HIGH>;
							standby-gpios = <&vizionlink_pca9554_0_2 2 GPIO_ACTIVE_HIGH>;

							data-lanes = <2>;
							data-frequency = <460>;
							continuous-clock = <0>;
							status = "okay";

							port {
								tevs_0_2_out: endpoint {
									remote-endpoint = <&ub953_0_2_in>;
								};
							};
						};
					};
				};
			};

			/* Link 2 has DS90UB953 serializer and OX03C10 sensor */

			link@2 {
				reg = <2>;
				i2c-alias = <0x45>;

				ti,rx-mode = <3>;
				ti,bc-gpio = <0>;

				serializer_0_3: serializer {
					compatible = "ti,ds90ub953-q1";

					gpio-controller;
					#gpio-cells = <2>;

					#clock-cells = <0>;

					ports {
						#address-cells = <1>;
						#size-cells = <0>;

						port@0 {
							reg = <0>;

							ub953_0_3_in: endpoint {
								data-lanes = <1 2>;
								remote-endpoint = <&tevs_0_3_out>;
							};
						};

						port@1 {
							reg = <1>;

							ub953_0_3_out: endpoint {
								remote-endpoint = <&ub960_0_fpd3_3_in>;
							};
						};
					};

					i2c {
						#address-cells = <1>;
						#size-cells = <0>;

						vizionlink_pca9554_0_3: vizionlink_pca9554_0@25 {
							compatible = "nxp,pca9554";
							reg = <0x25>;
							gpio-controller;
							#gpio-cells = <2>;
							status = "okay";
						};

						tevs_0_3: tevs@48 {
							compatible = "tn,tevs";
							reg = <0x48>;
							reset-gpios = <&vizionlink_pca9554_0_3 4 GPIO_ACTIVE_HIGH>;
							standby-gpios = <&vizionlink_pca9554_0_3 2 GPIO_ACTIVE_HIGH>;

							data-lanes = <2>;
							data-frequency = <460>;
							continuous-clock = <0>;
							status = "okay";

							port {
								tevs_0_3_out: endpoint {
									remote-endpoint = <&ub953_0_3_in>;
								};
							};
						};
					};
				};
			};

			/* Link 3 has DS90UB953 serializer and OX03C10 sensor */

			link@3 {
				reg = <3>;
				i2c-alias = <0x46>;

				ti,rx-mode = <3>;
				ti,bc-gpio = <0>;

				serializer_0_4: serializer {
					compatible = "ti,ds90ub953-q1";

					gpio-controller;
					#gpio-cells = <2>;

					#clock-cells = <0>;

					ports {
						#address-cells = <1>;
						#size-cells = <0>;

						port@0 {
							reg = <0>;

							ub953_0_4_in: endpoint {
								data-lanes = <1 2>;
								remote-endpoint = <&tevs_0_4_out>;
							};
						};

						port@1 {
							reg = <1>;

							ub953_0_4_out: endpoint {
								remote-endpoint = <&ub960_0_fpd3_4_in>;
							};
						};
					};

					i2c {
						#address-cells = <1>;
						#size-cells = <0>;

						vizionlink_pca9554_0_4: vizionlink_pca9554_0@25 {
							compatible = "nxp,pca9554";
							reg = <0x25>;
							gpio-controller;
							#gpio-cells = <2>;
							status = "okay";
						};

						tevs_0_4: tevs@48 {
							compatible = "tn,tevs";
							reg = <0x48>;
							reset-gpios = <&vizionlink_pca9554_0_4 4 GPIO_ACTIVE_HIGH>;
							standby-gpios = <&vizionlink_pca9554_0_4 2 GPIO_ACTIVE_HIGH>;

							data-lanes = <2>;
							data-frequency = <460>;
							continuous-clock = <0>;
							status = "okay";

							port {
								tevs_0_4_out: endpoint {
									remote-endpoint = <&ub953_0_4_in>;
								};
							};
						};
					};
				};
			};
		};
	};
};

&lpi2c4 {
	#address-cells = <1>;
	#size-cells = <0>;
	clock-frequency = <400000>;

	deser_1@30 {
		compatible = "ti,ds90ub960-q1";
		reg = <0x30>;
		clocks = <&clk_ref>;
		clock-names = "refclk";
		powerdown-gpios  = <&tca9555_c21 13 GPIO_ACTIVE_HIGH>;  /* CSI1_PDB */
		i2c-alias-pool = <0x48 0x49 0x4a 0x4b 0x4c 0x4d 0x4e 0x4f>;

		ds90ub960_1_ports: ports {
			#address-cells = <1>;
			#size-cells = <0>;

			/* FPDLink RX 0 */
			port@0 {
				reg = <0>;

				ub960_1_fpd3_1_in: endpoint {
					remote-endpoint = <&ub953_1_1_out>;
				};
			};

			port@1 {
				reg = <1>;

				ub960_1_fpd3_2_in: endpoint {
					remote-endpoint = <&ub953_1_2_out>;
				};
			};

			port@2 {
				reg = <2>;

				ub960_1_fpd3_3_in: endpoint {
					remote-endpoint = <&ub953_1_3_out>;
				};
			};

			port@3 {
				reg = <3>;

				ub960_1_fpd3_4_in: endpoint {
					remote-endpoint = <&ub953_1_4_out>;
				};
			};

			/* CSI-2 TX */
			port@4 {
				reg = <4>;

				ds90ub960_1_csi_out: endpoint {
					remote-endpoint = <&mipi_csi1_ep>;
					data-lanes = <1 2 3 4>;
					link-frequencies = /bits/ 64 <800000000>;
				};
			};
		};

		ds90ub960_1_links: links {
			#address-cells = <1>;
			#size-cells = <0>;

			/* Link 0 has DS90UB953 serializer and TEVS sensor */

			link@0 {
				reg = <0>;
				i2c-alias = <0x43>;

				ti,rx-mode = <3>;
				ti,bc-gpio = <0>;

				serializer_1_1: serializer {
					compatible = "ti,ds90ub953-q1";

					gpio-controller;
					#gpio-cells = <2>;

					#clock-cells = <0>;

					ports {
						#address-cells = <1>;
						#size-cells = <0>;

						port@0 {
							reg = <0>;

							ub953_1_1_in: endpoint {
								data-lanes = <1 2>;
								remote-endpoint = <&tevs_1_1_out>;
							};
						};

						port@1 {
							reg = <1>;

							ub953_1_1_out: endpoint {
								remote-endpoint = <&ub960_1_fpd3_1_in>;
							};
						};
					};

					i2c {
						#address-cells = <1>;
						#size-cells = <0>;

						vizionlink_pca9554_1_1: vizionlink_pca9554_1@25 {
							compatible = "nxp,pca9554";
							reg = <0x25>;
							gpio-controller;
							#gpio-cells = <2>;
							status = "okay";
						};

						tevs_1_1: tevs@48 {
							compatible = "tn,tevs";
							reg = <0x48>;
							reset-gpios = <&vizionlink_pca9554_1_1 4 GPIO_ACTIVE_HIGH>;
							standby-gpios = <&vizionlink_pca9554_1_1 2 GPIO_ACTIVE_HIGH>;

							data-lanes = <2>;
							data-frequency = <460>;
							continuous-clock = <0>;
							status = "okay";

							port {
								tevs_1_1_out: endpoint {
									remote-endpoint = <&ub953_1_1_in>;
								};
							};
						};
					};
				};
			};

			/* Link 1 has DS90UB953 serializer and OX03C10 sensor */

			link@1 {
				reg = <1>;
				i2c-alias = <0x44>;

				ti,rx-mode = <3>;
				ti,bc-gpio = <0>;

				serializer_1_2: serializer {
					compatible = "ti,ds90ub953-q1";

					gpio-controller;
					#gpio-cells = <2>;

					#clock-cells = <0>;

					ports {
						#address-cells = <1>;
						#size-cells = <0>;

						port@0 {
							reg = <0>;

							ub953_1_2_in: endpoint {
								data-lanes = <1 2>;
								remote-endpoint = <&tevs_1_2_out>;
							};
						};

						port@1 {
							reg = <1>;

							ub953_1_2_out: endpoint {
								remote-endpoint = <&ub960_1_fpd3_2_in>;
							};
						};
					};

					i2c {
						#address-cells = <1>;
						#size-cells = <0>;

						vizionlink_pca9554_1_2: vizionlink_pca9554_0@25 {
							compatible = "nxp,pca9554";
							reg = <0x25>;
							gpio-controller;
							#gpio-cells = <2>;
							status = "okay";
						};

						tevs_1_2: tevs@48 {
							compatible = "tn,tevs";
							reg = <0x48>;
							reset-gpios = <&vizionlink_pca9554_1_2 4 GPIO_ACTIVE_HIGH>;
							standby-gpios = <&vizionlink_pca9554_1_2 2 GPIO_ACTIVE_HIGH>;

							data-lanes = <2>;
							data-frequency = <460>;
							continuous-clock = <0>;
							status = "okay";

							port {
								tevs_1_2_out: endpoint {
									remote-endpoint = <&ub953_1_2_in>;
								};
							};
						};
					};
				};
			};

			/* Link 2 has DS90UB953 serializer and OX03C10 sensor */

			link@2 {
				reg = <2>;
				i2c-alias = <0x45>;

				ti,rx-mode = <3>;
				ti,bc-gpio = <0>;

				serializer_1_3: serializer {
					compatible = "ti,ds90ub953-q1";

					gpio-controller;
					#gpio-cells = <2>;

					#clock-cells = <0>;

					ports {
						#address-cells = <1>;
						#size-cells = <0>;

						port@0 {
							reg = <0>;

							ub953_1_3_in: endpoint {
								data-lanes = <1 2>;
								remote-endpoint = <&tevs_1_3_out>;
							};
						};

						port@1 {
							reg = <1>;

							ub953_1_3_out: endpoint {
								remote-endpoint = <&ub960_1_fpd3_3_in>;
							};
						};
					};

					i2c {
						#address-cells = <1>;
						#size-cells = <0>;

						vizionlink_pca9554_1_3: vizionlink_pca9554_0@25 {
							compatible = "nxp,pca9554";
							reg = <0x25>;
							gpio-controller;
							#gpio-cells = <2>;
							status = "okay";
						};

						tevs_1_3: tevs@48 {
							compatible = "tn,tevs";
							reg = <0x48>;
							reset-gpios = <&vizionlink_pca9554_1_3 4 GPIO_ACTIVE_HIGH>;
							standby-gpios = <&vizionlink_pca9554_1_3 2 GPIO_ACTIVE_HIGH>;

							data-lanes = <2>;
							data-frequency = <460>;
							continuous-clock = <0>;
							status = "okay";

							port {
								tevs_1_3_out: endpoint {
									remote-endpoint = <&ub953_1_3_in>;
								};
							};
						};
					};
				};
			};

			/* Link 3 has DS90UB953 serializer and OX03C10 sensor */

			link@3 {
				reg = <3>;
				i2c-alias = <0x46>;

				ti,rx-mode = <3>;
				ti,bc-gpio = <0>;

				serializer_1_4: serializer {
					compatible = "ti,ds90ub953-q1";

					gpio-controller;
					#gpio-cells = <2>;

					#clock-cells = <0>;

					ports {
						#address-cells = <1>;
						#size-cells = <0>;

						port@0 {
							reg = <0>;

							ub953_1_4_in: endpoint {
								data-lanes = <1 2>;
								remote-endpoint = <&tevs_1_4_out>;
							};
						};

						port@1 {
							reg = <1>;

							ub953_1_4_out: endpoint {
								remote-endpoint = <&ub960_1_fpd3_4_in>;
							};
						};
					};

					i2c {
						#address-cells = <1>;
						#size-cells = <0>;

						vizionlink_pca9554_1_4: vizionlink_pca9554_0@25 {
							compatible = "nxp,pca9554";
							reg = <0x25>;
							gpio-controller;
							#gpio-cells = <2>;
							status = "okay";
						};

						tevs_1_4: tevs@48 {
							compatible = "tn,tevs";
							reg = <0x48>;
							reset-gpios = <&vizionlink_pca9554_1_4 4 GPIO_ACTIVE_HIGH>;
							standby-gpios = <&vizionlink_pca9554_1_4 2 GPIO_ACTIVE_HIGH>;

							data-lanes = <2>;
							data-frequency = <460>;
							continuous-clock = <0>;
							status = "okay";

							port {
								tevs_1_4_out: endpoint {
									remote-endpoint = <&ub953_1_4_in>;
								};
							};
						};
					};
				};
			};
		};
	};
};

//**CSI-0 Part**//

&dphy_rx {
	status = "okay";
};

&mipi_csi0 {
	status = "okay";

	ports {
		#address-cells = <1>;
		#size-cells = <0>;

		port@0 {
			reg = <0>;
			mipi_csi0_ep: endpoint {
				remote-endpoint = <&ds90ub960_0_csi_out>;
				data-lanes = <1 2 3 4>;
				clock-lanes = <0>;
			};
		};

		port@1 {
			reg = <1>;
				mipi_csi0_out: endpoint {
				remote-endpoint = <&formatter_0_in>;
			};
		};
	};
};

&csi_pixel_formatter_0 {
	status = "okay";

	ports {
		#address-cells = <1>;
		#size-cells = <0>;

		port@0 {
			reg = <0>;

			formatter_0_in: endpoint {
				remote-endpoint = <&mipi_csi0_out>;
			};
		};

		port@1 {
			reg = <1>;

			formatter_0_out: endpoint {
				remote-endpoint = <&isi_in_2>;
			};
		};
	};
};

//**CSI-1 Part**//

&display_stream_csr {
	status = "disabled";
};

&display_master_csr {
	status = "disabled";
};

&mipi_tx_phy_csr {
	status = "disabled";
};

&mipi_dsi_intf {
	status = "okay";
};

&combo_rx {
	status = "okay";
};

&mipi_csi1 {
	status = "okay";

	ports {
		#address-cells = <1>;
		#size-cells = <0>;

		port@0 {
			reg = <0>;
			mipi_csi1_ep: endpoint {
				remote-endpoint = <&ds90ub960_1_csi_out>;
				data-lanes = <1 2 3 4>;
				clock-lanes = <0>;
			};
		};

		port@1 {
			reg = <1>;
				mipi_csi1_out: endpoint {
				remote-endpoint = <&formatter_1_in>;
			};
		};
	};
};

&csi_pixel_formatter_1 {
	status = "okay";

	ports {
		#address-cells = <1>;
		#size-cells = <0>;

		port@0 {
			reg = <0>;

			formatter_1_in: endpoint {
				remote-endpoint = <&mipi_csi1_out>;
			};
		};

		port@1 {
			reg = <1>;

			formatter_1_out: endpoint {
				remote-endpoint = <&isi_in_3>;
			};
		};
	};
};


&isi {
	status = "okay";

	ports {
		#address-cells = <1>;
		#size-cells = <0>;

		port@2 {
			reg = <2>;

			isi_in_2: endpoint {
				remote-endpoint = <&formatter_0_out>;
			};
		};

		port@3 {
			reg = <3>;

			isi_in_3: endpoint {
				remote-endpoint = <&formatter_1_out>;
			};
		};
	};
};
